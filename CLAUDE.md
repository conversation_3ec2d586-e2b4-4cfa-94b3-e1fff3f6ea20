# Sabi Chat - Claude Code Context

## Project Overview
Sabi Chat is a modern AI-powered chat platform that enables users to interact with multiple LLM providers (OpenAI, Anthropic, Google Gemini, DeepSeek) in a unified interface. It features workspaces, conversation management, subscription billing, and team collaboration capabilities.

## Key Technologies
- **Frontend**: Next.js 15 (App Router), React 19, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes, Supabase (PostgreSQL)
- **Authentication**: Supabase Auth (email/magic-link)
- **Payments**: Stripe subscriptions and add-ons
- **State Management**: React Context + Zustand
- **Testing**: Jest, React Testing Library
- **Observability**: Sentry, Honeycomb OpenTelemetry

## Project Structure
```
src/
├── app/                    # Next.js App Router pages and API routes
│   ├── (main)/            # Main app pages (chat, workspaces, etc.)
│   ├── api/               # API route handlers
│   ├── auth/              # Authentication pages
│   └── settings/          # User settings pages
├── components/            # React components
│   ├── chat/              # Chat-related components
│   ├── ui/                # Reusable UI components (shadcn-style)
│   ├── sidebar/           # Sidebar navigation
│   └── workspaces/        # Workspace management
├── providers/             # React context providers
├── services/              # Business logic and external API integrations
├── lib/                   # Utility functions and shared code
├── hooks/                 # Custom React hooks
└── types/                 # TypeScript type definitions
```

## Development Commands
```bash
# Development
yarn dev                   # Start dev server with Turbopack
yarn build                 # Build for production
yarn start                 # Start production server

# Code Quality
yarn lint                  # Lint all source code
yarn lint:changed          # Lint only changed files
yarn lint:fix              # Auto-fix linting issues
yarn validate              # TypeScript check + lint
yarn format                # Format code with Prettier
yarn format:check          # Check code formatting

# Testing
yarn test                  # Run unit tests
yarn test:watch            # Run tests in watch mode
```

## Key Features
1. **Multi-LLM Chat**: Support for OpenAI, Anthropic, Google Gemini, DeepSeek
2. **Workspaces**: Organize conversations with context files and shared settings
3. **Conversation Management**: Archive, search, rename, favorite conversations
4. **Prompt Library**: Save and reuse prompts with variables
5. **Sharing**: Public read-only share links for conversations
6. **Subscription Billing**: Tiered plans with quota management via Stripe
7. **File Attachments**: Upload images and documents to chat
8. **Dark/Light Themes**: System and manual theme switching

## Core Architecture Patterns

### LLM Integration
- Strategy pattern via `LLMProviderFactory` in `src/services/llm/`
- Polymorphic `LLMProvider` interface for consistent API across vendors
- Streaming responses via Server-Sent Events
- Token counting and quota enforcement

### State Management
- Global providers wrap the app in `src/app/layout.tsx`
- Chat state managed via `ChatProvider`
- Sidebar state via `SidebarProvider`
- Feature flags via `FeatureFlagsProvider`

### Database Access
- Supabase client/server utilities in `src/utils/supabase/`
- Row Level Security (RLS) enforced on all tables
- Database functions for complex operations (quotas, usage tracking)

### API Structure
- RESTful endpoints under `src/app/api/`
- Streaming chat endpoint at `/api/chat/stream`
- Webhook handlers for Stripe at `/api/webhooks/stripe`

## Testing Strategy
- Unit tests for components in `__tests__/` directories
- Mocked services in `__mocks__/` directories
- React Testing Library for component testing
- MSW (Mock Service Worker) for API mocking

## Environment Setup
1. Node.js >= 21.1.0
2. Yarn package manager
3. Supabase CLI for local development
4. Environment variables in `.env.local`

## Important Files to Know
- `src/app/layout.tsx` - Root layout with all providers
- `src/components/chat/ChatArea.tsx` - Main chat interface
- `src/services/llm/service.ts` - LLM service orchestration
- `src/services/quota.ts` - Usage quota management
- `src/app/api/chat/stream/route.ts` - Streaming chat endpoint
- `supabase/migrations/` - Database schema and functions

## Security Considerations
- All API routes validate Supabase sessions
- RLS policies protect user data
- Sensitive operations require explicit authorization
- No client-side API keys or secrets

## Observability
- Sentry for error monitoring (client, server, edge)
- Honeycomb for distributed tracing via OpenTelemetry
- Custom ErrorBoundary components for React error handling