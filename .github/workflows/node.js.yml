# This workflow will do a clean installation of node dependencies, cache/restore them, build the source code and run tests across different versions of node
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-nodejs

name: Node.js CI

on:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main" ]

jobs:
  build:

    runs-on: ubuntu-latest

    timeout-minutes: 10

    strategy:
      matrix:
        node-version: [21.1.0]
        # See supported Node.js release schedule at https://nodejs.org/en/about/releases/

    steps:
    - uses: actions/checkout@v4
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'yarn'
    - name: Set env
      run: |
        echo "NEXT_PUBLIC_SUPABASE_URL=${{ vars.NEXT_PUBLIC_SUPABASE_URL }}" >> $GITHUB_ENV
        echo "NEXT_PUBLIC_SUPABASE_ANON_KEY=${{ vars.NEXT_PUBLIC_SUPABASE_ANON_KEY }}" >> $GITHUB_ENV
        echo "STRIPE_SECRET_KEY=${{ vars.STRIPE_SECRET_KEY }}" >> $GITHUB_ENV
        echo "OPENAI_API_KEY=${{ vars.OPENAI_API_KEY }}" >> $GITHUB_ENV
        echo "HONEYCOMB_API_KEY=${{ vars.HONEYCOMB_API_KEY }}" >> $GITHUB_ENV
        echo "HONEYCOMB_API_ENDPOINT=${{ vars.HONEYCOMB_API_ENDPOINT }}" >> $GITHUB_ENV
        echo "SENTRY_AUTH_TOKEN=${{ vars.SENTRY_AUTH_TOKEN }}" >> $GITHUB_ENV
        echo "SENTRY_ORG=${{ vars.SENTRY_ORG }}" >> $GITHUB_ENV
        echo "SENTRY_PROJECT=${{ vars.SENTRY_PROJECT }}" >> $GITHUB_ENV
        echo "SENTRY_RELEASE=${{ vars.SENTRY_RELEASE }}" >> $GITHUB_ENV
        echo "SENTRY_DSN=${{ vars.SENTRY_DSN }}" >> $GITHUB_ENV
        echo "NEXT_PUBLIC_SENTRY_DSN=${{ vars.NEXT_PUBLIC_SENTRY_DSN }}" >> $GITHUB_ENV
        echo "NODE_ENV=${{ vars.NODE_ENV }}" >> $GITHUB_ENV
        echo "STRIPE_WEBHOOK_SECRET=${{ vars.STRIPE_WEBHOOK_SECRET }}" >> $GITHUB_ENV
        echo "STRIPE_PRICE_STARTER_MONTHLY=${{ vars.STRIPE_PRICE_STARTER_MONTHLY }}" >> $GITHUB_ENV
        echo "STRIPE_PRICE_PREMIUM_MONTHLY=${{ vars.STRIPE_PRICE_PREMIUM_MONTHLY }}" >> $GITHUB_ENV
        echo "STRIPE_PRICE_STARTER_YEARLY=${{ vars.STRIPE_PRICE_STARTER_YEARLY }}" >> $GITHUB_ENV
        echo "STRIPE_PRICE_PREMIUM_YEARLY=${{ vars.STRIPE_PRICE_PREMIUM_YEARLY }}" >> $GITHUB_ENV

    - run: yarn install
    - run: yarn validate
    - name: Increase Node.js memory and build
      run: |
        export NODE_OPTIONS=--max-old-space-size=4096
        yarn build
