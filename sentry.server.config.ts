// This file configures the initialization of Sentry on the server.
// The config you add here will be used whenever the server handles a request.
// https://docs.sentry.io/platforms/javascript/guides/nextjs/

import * as Sentry from '@sentry/nextjs';

// Only initialize Sentry in production environment
if (process.env.NODE_ENV === 'production') {
  Sentry.init({
    dsn: process.env.SENTRY_DSN,

    // Adjust sample rate for production - 10% for performance monitoring
    tracesSampleRate: 0.1,

    // Disable debug in production
    debug: false,

    // Set environment for better error tracking
    environment: process.env.NODE_ENV,

    // Add release information if available
    release: process.env.SENTRY_RELEASE || process.env.VERCEL_GIT_COMMIT_SHA,

    // Filter out noisy errors
    beforeSend(event) {
      // Don't send errors from health checks or other automated tools
      if (event.request?.url?.includes('/api/health')) {
        return null;
      }

      // Filter database connection timeout errors that are handled gracefully
      if (event.exception) {
        const error = event.exception.values?.[0];
        if (
          error?.value?.includes('connection timeout') &&
          error?.value?.includes('postgres')
        ) {
          return null;
        }
      }

      return event;
    },

    // Add server context
    initialScope: {
      tags: {
        runtime: 'nodejs',
      },
    },
  });
}
