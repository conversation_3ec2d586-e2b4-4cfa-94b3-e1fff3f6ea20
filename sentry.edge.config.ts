// This file configures the initialization of Sentry for edge features (middleware, edge routes, and so on).
// The config you add here will be used whenever one of the edge features is loaded.
// Note that this config is unrelated to the Vercel Edge Runtime and is also required when running locally.
// https://docs.sentry.io/platforms/javascript/guides/nextjs/

import * as Sentry from '@sentry/nextjs';

// Only initialize Sentry in production environment
if (process.env.NODE_ENV === 'production') {
  Sentry.init({
    dsn: process.env.SENTRY_DSN,

    // Adjust sample rate for production - 5% for edge runtime to reduce overhead
    tracesSampleRate: 0.05,

    // Disable debug in production
    debug: false,

    // Set environment for better error tracking
    environment: process.env.NODE_ENV,

    // Add release information if available
    release: process.env.SENTRY_RELEASE || process.env.VERCEL_GIT_COMMIT_SHA,

    // Add edge context
    initialScope: {
      tags: {
        runtime: 'edge',
      },
    },

    // Filter out edge runtime specific non-critical errors
    beforeSend(event) {
      if (event.exception) {
        const error = event.exception.values?.[0];
        // Filter out common edge runtime errors that aren't actionable
        if (error?.value?.includes('Dynamic Code Evaluation')) {
          return null;
        }
      }
      return event;
    },
  });
}
