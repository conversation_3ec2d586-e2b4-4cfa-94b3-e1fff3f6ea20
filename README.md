# Sabi Chat

A modern AI chat application built with Next.js, React, and Supabase. Supports multiple LLM providers including Claude, GPT-4, and Gemini.

## Features

- Chat with multiple AI assistants
- Upload files and images to chat
- Compare responses from different models side-by-side
- Web search integration
- Conversation management (rename, archive, delete, favorite)
- Multiple themes and appearance settings
- Subscription tiers with different model access

## Tech Stack

- **Frontend**: Next.js, React, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes, Supabase
- **Database**: PostgreSQL (via Supabase)
- **Authentication**: Supabase Auth
- **AI Integration**: OpenAI, Anthropic, Google Gemini
- **Testing**: Jest, React Testing Library, Playwright

## Getting Started

### Prerequisites

- Node.js 21.1.0
- Yarn package manager
- Supabase CLI

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/chatty-app.git
   cd chatty-app
   ```

2. Install dependencies:
   ```bash
   yarn install
   ```

3. Set up environment variables:
   ```bash
   cp .env.example .env.local
   ```
   Then edit `.env.local` with your API keys and configuration.

4. Start Supabase local development:
   ```bash
   npx supabase start
   ```

5. Run the development server:
   ```bash
   yarn dev
   ```

6. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Testing

For detailed testing information, see [TESTING.md](./TESTING.md).

### Running Tests

```bash
# Unit tests
yarn test

# Unit tests in watch mode
yarn test:watch

# E2E tests
yarn test:e2e

# E2E tests with UI
yarn test:e2e:ui
```

## Deployment

The application can be deployed to Vercel:

```bash
yarn build
yarn start
```

For production deployment, connect your GitHub repository to Vercel for automatic deployments.

## Project Structure

- `src/app` - Next.js app router pages and API routes
- `src/components` - React components
- `src/providers` - React context providers
- `src/services` - Service modules for external APIs
- `src/lib` - Utility functions and shared code
- `src/hooks` - Custom React hooks
- `src/types` - TypeScript type definitions
- `src/utils` - Utility functions
- `public` - Static assets
- `e2e` - Playwright E2E tests
- `supabase` - Supabase configuration and migrations

## Contributing

1. Fork the repository
2. Create your feature branch: `git checkout -b feature/amazing-feature`
3. Commit your changes: `git commit -m 'Add some amazing feature'`
4. Push to the branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.