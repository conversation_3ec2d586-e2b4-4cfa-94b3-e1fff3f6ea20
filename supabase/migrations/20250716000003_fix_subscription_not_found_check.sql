-- Fix bug in find_subscription_by_customer_id function
-- The previous implementation used IF NOT FOUND after RETURN QUERY,
-- but RETURN QUERY does not set the FOUND variable, so the exception was never raised

CREATE OR REPLACE FUNCTION find_subscription_by_customer_id(
  p_stripe_customer_id TEXT
) RETURNS TABLE(
  id UUID,
  user_id UUID,
  plan TEXT,
  status TEXT,
  current_period_end TIMESTAMP,
  cancel_at_period_end BOOLEAN,
  created_at TIMESTAMP,
  updated_at TIMESTAMP,
  stripe_customer_id TEXT,
  stripe_subscription_id TEXT
)
SECURITY DEFINER
AS $$
DECLARE
  subscription_count INTEGER;
BEGIN
  -- Validate input
  IF p_stripe_customer_id IS NULL THEN
    RAISE EXCEPTION 'Customer ID cannot be null';
  END IF;

  -- Trim whitespace from customer ID
  p_stripe_customer_id := TRIM(p_stripe_customer_id);

  -- Check if subscription exists first
  SELECT COUNT(*) INTO subscription_count
  FROM subscriptions s
  WHERE s.stripe_customer_id = p_stripe_customer_id;

  -- Raise exception if no subscription found
  IF subscription_count = 0 THEN
    RAISE EXCEPTION 'Subscription not found for customer ID: %', p_stripe_customer_id;
  END IF;

  -- Return subscription data
  RETURN QUERY
  SELECT
    s.id,
    s.user_id,
    s.plan,
    s.status,
    s.current_period_end,
    s.cancel_at_period_end,
    s.created_at,
    s.updated_at,
    s.stripe_customer_id,
    s.stripe_subscription_id
  FROM subscriptions s
  WHERE s.stripe_customer_id = p_stripe_customer_id;
END;
$$ LANGUAGE plpgsql;

-- Update the comment to reflect the fix
COMMENT ON FUNCTION find_subscription_by_customer_id(TEXT) IS 'SECURITY: Retrieves subscription data by Stripe customer ID. Uses SECURITY DEFINER to bypass RLS for webhook operations. Fixed to properly raise exception when subscription not found.';