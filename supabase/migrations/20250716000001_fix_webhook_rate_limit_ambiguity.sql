-- Fix column ambiguity in webhook rate limit function
-- The function was using a local variable 'expires_at' with same name as table column
CREATE OR REPLACE FUNCTION "public"."check_and_increment_webhook_rate_limit"("p_rate_key" "text", "p_max_requests" integer DEFAULT 100, "p_window_seconds" integer DEFAULT 300) RETURNS TABLE("allowed" boolean, "current_count" integer, "reset_time" timestamp with time zone)
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    rate_record record;
    window_start timestamp with time zone;
    local_expires_at timestamp with time zone;  -- Renamed from expires_at to avoid ambiguity
    now_time timestamp with time zone;
BEGIN
    now_time := CLOCK_TIMESTAMP();
    window_start := now_time;
    local_expires_at := now_time + (p_window_seconds * interval '1 second');

    -- Try to get existing rate limit record
    SELECT * INTO rate_record
    FROM webhook_rate_limits
    WHERE rate_key = p_rate_key;

    -- If no record exists or record has expired, create/reset
    IF NOT FOUND OR rate_record.expires_at <= now_time THEN
        INSERT INTO webhook_rate_limits (rate_key, request_count, window_start, expires_at, updated_at)
        VALUES (p_rate_key, 1, window_start, local_expires_at, now_time)
        ON CONFLICT (rate_key)
        DO UPDATE SET
            request_count = 1,
            window_start = EXCLUDED.window_start,
            expires_at = EXCLUDED.expires_at,
            updated_at = EXCLUDED.updated_at;

        RETURN QUERY SELECT true, 1, local_expires_at;
        RETURN;
    END IF;

    -- Check if we can increment within the limit
    IF rate_record.request_count >= p_max_requests THEN
        RETURN QUERY SELECT false, rate_record.request_count, rate_record.expires_at;
        RETURN;
    END IF;

    -- Increment the counter
    UPDATE webhook_rate_limits
    SET request_count = request_count + 1,
        updated_at = now_time
    WHERE rate_key = p_rate_key;

    RETURN QUERY SELECT true, rate_record.request_count + 1, rate_record.expires_at;
END;
$$;

-- Update the comment to reflect the fix
COMMENT ON FUNCTION "public"."check_and_increment_webhook_rate_limit"("p_rate_key" "text", "p_max_requests" integer, "p_window_seconds" integer) IS 'Atomically checks and increments webhook rate limit counters. Fixed variable name ambiguity issue.';