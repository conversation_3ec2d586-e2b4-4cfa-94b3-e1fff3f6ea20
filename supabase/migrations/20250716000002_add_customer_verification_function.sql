-- Create function to verify customer ownership with elevated privileges
-- This function bypasses <PERSON><PERSON> to check if a user owns a specific Stripe customer
CREATE OR REPLACE FUNCTION verify_customer_ownership(
  p_stripe_customer_id TEXT,
  p_user_id UUID
) RETURNS BOOLEAN
SECURITY DEFINER
AS $$
BEGIN
  -- Validate inputs
  IF p_stripe_customer_id IS NULL OR p_user_id IS NULL THEN
    RETURN FALSE;
  END IF;
  
  -- Trim whitespace from customer ID to handle any formatting issues
  p_stripe_customer_id := TRIM(p_stripe_customer_id);
  
  -- Check if user owns this customer
  RETURN EXISTS(
    SELECT 1 FROM subscriptions 
    WHERE stripe_customer_id = p_stripe_customer_id 
    AND user_id = p_user_id
  );
END;
$$ LANGUAGE plpgsql;

-- Grant execute permissions to allow webhook handler to call this function
GRANT EXECUTE ON FUNCTION verify_customer_ownership(TEXT, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION verify_customer_ownership(TEXT, UUID) TO anon;
GRANT EXECUTE ON FUNCTION verify_customer_ownership(TEXT, UUID) TO service_role;

-- Add comment for documentation
COMMENT ON FUNCTION verify_customer_ownership(TEXT, UUID) IS 'SECURITY: Verifies if a user owns a specific Stripe customer ID. Uses SECURITY DEFINER to bypass RLS for webhook operations.';

-- Create function to find subscription by customer ID with elevated privileges
-- This function bypasses RLS to retrieve subscription data for webhook operations
CREATE OR REPLACE FUNCTION find_subscription_by_customer_id(
  p_stripe_customer_id TEXT
) RETURNS TABLE(
  id UUID,
  user_id UUID,
  plan TEXT,
  status TEXT,
  current_period_end TIMESTAMP,
  cancel_at_period_end BOOLEAN,
  created_at TIMESTAMP,
  updated_at TIMESTAMP,
  stripe_customer_id TEXT,
  stripe_subscription_id TEXT
)
SECURITY DEFINER
AS $$
BEGIN
  -- Validate input
  IF p_stripe_customer_id IS NULL THEN
    RAISE EXCEPTION 'Customer ID cannot be null';
  END IF;
  
  -- Trim whitespace from customer ID
  p_stripe_customer_id := TRIM(p_stripe_customer_id);
  
  -- Return subscription data
  RETURN QUERY
  SELECT 
    s.id,
    s.user_id,
    s.plan,
    s.status,
    s.current_period_end,
    s.cancel_at_period_end,
    s.created_at,
    s.updated_at,
    s.stripe_customer_id,
    s.stripe_subscription_id
  FROM subscriptions s
  WHERE s.stripe_customer_id = p_stripe_customer_id;
  
  -- Check if no results found
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Subscription not found for customer ID: %', p_stripe_customer_id;
  END IF;
END;
$$ LANGUAGE plpgsql;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION find_subscription_by_customer_id(TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION find_subscription_by_customer_id(TEXT) TO anon;
GRANT EXECUTE ON FUNCTION find_subscription_by_customer_id(TEXT) TO service_role;

-- Add comment for documentation
COMMENT ON FUNCTION find_subscription_by_customer_id(TEXT) IS 'SECURITY: Retrieves subscription data by Stripe customer ID. Uses SECURITY DEFINER to bypass RLS for webhook operations.';