-- Create consolidated RPC function for chat initialization
-- This function combines multiple DB operations into a single atomic transaction
-- to reduce round trips and improve TTFT (Time to First Token)

CREATE OR REPLACE FUNCTION initialize_chat_session(
  p_user_id UUID,
  p_message_content TEXT,
  p_model_id UUID,
  p_provider_id UUID,
  p_conversation_id UUID DEFAULT NULL,
  p_group_conversation_id UUID DEFAULT NULL,
  p_parent_message_id UUID DEFAULT NULL,
  p_attachments JSONB DEFAULT NULL,
  p_workspace_id UUID DEFAULT NULL,
  p_is_comparison BOOLEAN DEFAULT FALSE,
  p_is_temporary BOOLEAN DEFAULT FALSE,
  p_comparison_index INTEGER DEFAULT NULL,
  p_group_title TEXT DEFAULT 'New Conversation'
) RETURNS TABLE(
  conversation_id UUID,
  group_conversation_id UUID,
  user_message_id UUID,
  assistant_message_id UUID,
  is_new_conversation BOOLEAN
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_conversation_id UUID;
  v_group_conversation_id UUID;
  v_user_message_id UUID;
  v_assistant_message_id UUID;
  v_is_new_conversation BOOLEAN := FALSE;
  v_conversation_record RECORD;
  v_group_record RECORD;
BEGIN
  -- Input validation
  IF p_user_id IS NULL OR p_message_content IS NULL OR p_model_id IS NULL OR p_provider_id IS NULL THEN
    RAISE EXCEPTION 'Required parameters cannot be null: user_id, message_content, model_id, provider_id';
  END IF;

  -- Generate IDs if not provided
  v_user_message_id := gen_random_uuid();
  v_assistant_message_id := gen_random_uuid();

  -- Handle conversation management
  IF p_conversation_id IS NOT NULL THEN
    -- Existing conversation - verify ownership and update if needed
    SELECT * INTO v_conversation_record
    FROM conversations
    WHERE id = p_conversation_id AND user_id = p_user_id;

    IF NOT FOUND THEN
      RAISE EXCEPTION 'Conversation not found or unauthorized: %', p_conversation_id;
    END IF;

    v_conversation_id := p_conversation_id;
    v_group_conversation_id := v_conversation_record.group_conversation_id;

    -- Update last model used if changed
    IF v_conversation_record.last_model_used IS DISTINCT FROM p_model_id THEN
      UPDATE conversations
      SET last_model_used = p_model_id, updated_at = NOW()
      WHERE id = p_conversation_id;
    END IF;
  ELSE
    -- New conversation
    v_is_new_conversation := TRUE;

    -- Handle group conversation
    IF p_group_conversation_id IS NOT NULL THEN
      -- Check if group conversation exists and belongs to user
      SELECT * INTO v_group_record
      FROM group_conversations
      WHERE id = p_group_conversation_id AND user_id = p_user_id;

      IF FOUND THEN
        v_group_conversation_id := p_group_conversation_id;
      ELSE
        -- Group doesn't exist or doesn't belong to user, create new one
        v_group_conversation_id := gen_random_uuid();
      END IF;
    ELSE
      -- Generate new group conversation ID
      v_group_conversation_id := gen_random_uuid();
    END IF;

    -- Create or update group conversation
    INSERT INTO group_conversations (
      id,
      user_id,
      title,
      is_active,
      is_comparison,
      is_temporary,
      workspace_id,
      created_at,
      updated_at
    )
    VALUES (
      v_group_conversation_id,
      p_user_id,
      p_group_title,
      TRUE,
      p_is_comparison,
      p_is_temporary,
      p_workspace_id,
      NOW(),
      NOW()
    )
    ON CONFLICT (id) DO UPDATE SET
      updated_at = NOW(),
      is_comparison = EXCLUDED.is_comparison,
      is_temporary = EXCLUDED.is_temporary,
      workspace_id = EXCLUDED.workspace_id;

    -- Create conversation
    INSERT INTO conversations (
      id,
      user_id,
      title,
      last_model_used,
      group_conversation_id,
      comparison_index,
      created_at,
      updated_at
    )
    VALUES (
      gen_random_uuid(),
      p_user_id,
      'New Conversation',
      p_model_id,
      v_group_conversation_id,
      p_comparison_index,
      NOW(),
      NOW()
    )
    RETURNING id INTO v_conversation_id;
  END IF;

  -- Create user message
  INSERT INTO messages (
    id,
    conversation_id,
    content,
    role,
    parent_message_id,
    attachments,
    created_at,
    updated_at
  )
  VALUES (
    v_user_message_id,
    v_conversation_id,
    p_message_content,
    'user',
    p_parent_message_id,
    p_attachments,
    NOW(),
    NOW()
  );

  -- Create assistant placeholder message
  INSERT INTO messages (
    id,
    conversation_id,
    content,
    role,
    parent_message_id,
    model_id,
    provider_id,
    created_at,
    updated_at
  )
  VALUES (
    v_assistant_message_id,
    v_conversation_id,
    '',
    'assistant',
    v_user_message_id,
    p_model_id,
    p_provider_id,
    NOW(),
    NOW()
  );

  -- Return the results
  RETURN QUERY SELECT
    v_conversation_id,
    v_group_conversation_id,
    v_user_message_id,
    v_assistant_message_id,
    v_is_new_conversation;
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION initialize_chat_session(
  UUID, TEXT, UUID, UUID, UUID, UUID, UUID, JSONB, UUID, BOOLEAN, BOOLEAN, INTEGER, TEXT
) TO authenticated;

-- Add comment for documentation
COMMENT ON FUNCTION initialize_chat_session(
  UUID, TEXT, UUID, UUID, UUID, UUID, UUID, JSONB, UUID, BOOLEAN, BOOLEAN, INTEGER, TEXT
) IS 'PERFORMANCE: Atomic chat session initialization that consolidates multiple DB operations into a single transaction to improve TTFT. Creates/updates group conversation, conversation, and both user and assistant messages.';