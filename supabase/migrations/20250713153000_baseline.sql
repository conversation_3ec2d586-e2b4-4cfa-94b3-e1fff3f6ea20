

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;


CREATE SCHEMA IF NOT EXISTS "public";


ALTER SCHEMA "public" OWNER TO "pg_database_owner";


COMMENT ON SCHEMA "public" IS 'standard public schema';



CREATE TYPE "public"."conversation_state" AS ENUM (
    'healthy',
    'error',
    'exceeds_limit'
);


ALTER TYPE "public"."conversation_state" OWNER TO "postgres";


COMMENT ON TYPE "public"."conversation_state" IS 'Health check on conversation';



CREATE TYPE "public"."job_status" AS ENUM (
    'initiated',
    'pending',
    'completed',
    'error'
);


ALTER TYPE "public"."job_status" OWNER TO "postgres";


CREATE TYPE "public"."subscription_plan" AS ENUM (
    'free',
    'starter',
    'premium'
);


ALTER TYPE "public"."subscription_plan" OWNER TO "postgres";


CREATE TYPE "public"."subscription_status" AS ENUM (
    'active',
    'canceled',
    'incomplete',
    'past_due',
    'trialing'
);


ALTER TYPE "public"."subscription_status" OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."add_persistent_credit_balance"("p_user_id" "uuid", "p_credits" integer) RETURNS "void"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    -- Input validation
    IF p_credits <= 0 THEN
        RAISE EXCEPTION 'Credit amount must be positive: %', p_credits;
    END IF;
    
    -- Insert or update balance
    INSERT INTO user_balances (user_id, credits_purchased_total)
    VALUES (p_user_id, p_credits)
    ON CONFLICT (user_id)
    DO UPDATE SET
        credits_purchased_total = user_balances.credits_purchased_total + p_credits,
        updated_at = NOW();
        
    -- Log the transaction for audit trail
    INSERT INTO stripe_events (event_id, event_type, event_data)
    VALUES (
        'balance_credit_' || extract(epoch from now())::text || '_' || p_user_id::text,
        'balance.credits_added',
        jsonb_build_object(
            'user_id', p_user_id,
            'credits_added', p_credits,
            'timestamp', now()
        )
    );
END;
$$;


ALTER FUNCTION "public"."add_persistent_credit_balance"("p_user_id" "uuid", "p_credits" integer) OWNER TO "postgres";


COMMENT ON FUNCTION "public"."add_persistent_credit_balance"("p_user_id" "uuid", "p_credits" integer) IS 'SECURITY: Only callable by service_role - handles paid credit additions from Stripe webhooks';



CREATE OR REPLACE FUNCTION "public"."add_persistent_token_balance"("p_user_id" "uuid", "p_tokens" bigint) RETURNS "void"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    -- Input validation
    IF p_tokens <= 0 THEN
        RAISE EXCEPTION 'Token amount must be positive: %', p_tokens;
    END IF;
    
    -- Insert or update balance
    INSERT INTO user_balances (user_id, tokens_purchased_total)
    VALUES (p_user_id, p_tokens)
    ON CONFLICT (user_id)
    DO UPDATE SET
        tokens_purchased_total = user_balances.tokens_purchased_total + p_tokens,
        updated_at = NOW();
        
    -- Log the transaction for audit trail
    INSERT INTO stripe_events (event_id, event_type, event_data)
    VALUES (
        'balance_credit_' || extract(epoch from now())::text || '_' || p_user_id::text,
        'balance.tokens_added',
        jsonb_build_object(
            'user_id', p_user_id,
            'tokens_added', p_tokens,
            'timestamp', now()
        )
    );
END;
$$;


ALTER FUNCTION "public"."add_persistent_token_balance"("p_user_id" "uuid", "p_tokens" bigint) OWNER TO "postgres";


COMMENT ON FUNCTION "public"."add_persistent_token_balance"("p_user_id" "uuid", "p_tokens" bigint) IS 'SECURITY: Only callable by service_role - handles paid token additions from Stripe webhooks';



CREATE OR REPLACE FUNCTION "public"."atomic_comparison_check_and_increment"("p_user_id" "uuid", "p_date" "date", "p_daily_limit" integer) RETURNS TABLE("success" boolean, "remaining" integer)
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    current_count integer;
    new_count integer;
BEGIN
    -- Try to update existing row atomically with limit check
    UPDATE user_daily_usage 
    SET 
        comparison_count = comparison_count + 1,
        updated_at = NOW()
    WHERE user_id = p_user_id 
      AND date = p_date
      AND comparison_count + 1 <= p_daily_limit
    RETURNING comparison_count INTO new_count;
    
    -- If we updated a row, return success
    IF FOUND THEN
        RETURN QUERY SELECT true, p_daily_limit - new_count;
        RETURN;
    END IF;
    
    -- Check if row exists but limit would be exceeded
    SELECT COALESCE(comparison_count, 0) INTO current_count
    FROM user_daily_usage 
    WHERE user_id = p_user_id AND date = p_date;
    
    IF FOUND THEN
        -- Row exists but limit exceeded
        RETURN QUERY SELECT false, p_daily_limit - current_count;
        RETURN;
    END IF;
    
    -- No row exists, check if initial increment would exceed limit
    IF 1 > p_daily_limit THEN
        RETURN QUERY SELECT false, p_daily_limit;
        RETURN;
    END IF;
    
    -- Safe to create new row
    INSERT INTO user_daily_usage (user_id, date, comparison_count, created_at, updated_at)
    VALUES (p_user_id, p_date, 1, NOW(), NOW());
    
    RETURN QUERY SELECT true, p_daily_limit - 1;
END;
$$;


ALTER FUNCTION "public"."atomic_comparison_check_and_increment"("p_user_id" "uuid", "p_date" "date", "p_daily_limit" integer) OWNER TO "postgres";


COMMENT ON FUNCTION "public"."atomic_comparison_check_and_increment"("p_user_id" "uuid", "p_date" "date", "p_daily_limit" integer) IS 'FIXED: Now uses proper UUID and DATE parameter types for efficient index usage';



CREATE OR REPLACE FUNCTION "public"."atomic_comparison_check_and_increment_safe"("p_user_id" "uuid", "p_date" "text", "p_daily_limit" integer) RETURNS TABLE("success" boolean, "remaining" integer)
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    attempt_count integer := 0;
    max_attempts integer := 3;
    current_count integer;
    new_count integer;
BEGIN
    LOOP
        BEGIN
            -- Try to update existing row atomically with limit check
            UPDATE user_daily_usage 
            SET 
                comparison_count = comparison_count + 1,
                updated_at = NOW()
            WHERE user_id = p_user_id 
              AND date = p_date::DATE
              AND comparison_count + 1 <= p_daily_limit
            RETURNING comparison_count INTO new_count;
            
            -- If we updated a row, return success
            IF FOUND THEN
                RETURN QUERY SELECT true, p_daily_limit - new_count;
                RETURN;
            END IF;
            
            -- Check if row exists but limit would be exceeded
            SELECT COALESCE(comparison_count, 0) INTO current_count
            FROM user_daily_usage 
            WHERE user_id = p_user_id AND date = p_date::DATE;
            
            IF FOUND THEN
                -- Row exists but limit exceeded
                RETURN QUERY SELECT false, p_daily_limit - current_count;
                RETURN;
            END IF;
            
            -- No row exists, check if initial increment would exceed limit
            IF 1 > p_daily_limit THEN
                RETURN QUERY SELECT false, p_daily_limit;
                RETURN;
            END IF;
            
            -- Safe to create new row
            INSERT INTO user_daily_usage (user_id, date, comparison_count, created_at, updated_at)
            VALUES (p_user_id, p_date::DATE, 1, NOW(), NOW());
            
            RETURN QUERY SELECT true, p_daily_limit - 1;
            RETURN;
            
        EXCEPTION 
            WHEN unique_violation THEN
                attempt_count := attempt_count + 1;
                IF attempt_count >= max_attempts THEN
                    RAISE NOTICE 'Max retry attempts exceeded for comparison quota operation: user_id=%, attempts=%', p_user_id, max_attempts;
                    RETURN QUERY SELECT false, 0;
                    RETURN;
                END IF;
                PERFORM pg_sleep(0.01 * attempt_count);
        END;
    END LOOP;
END;
$$;


ALTER FUNCTION "public"."atomic_comparison_check_and_increment_safe"("p_user_id" "uuid", "p_date" "text", "p_daily_limit" integer) OWNER TO "postgres";


COMMENT ON FUNCTION "public"."atomic_comparison_check_and_increment_safe"("p_user_id" "uuid", "p_date" "text", "p_daily_limit" integer) IS 'SECURITY: Only callable by service_role - prevents quota manipulation attacks';



CREATE OR REPLACE FUNCTION "public"."atomic_image_check_and_increment"("p_user_id" "uuid", "p_credits" integer, "p_year_month" "text", "p_quota_limit" integer) RETURNS TABLE("success" boolean, "remaining" integer)
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    current_usage integer;
    new_usage integer;
BEGIN
    -- Input validation
    IF p_credits <= 0 THEN
        RAISE EXCEPTION 'Credit usage must be positive: %', p_credits;
    END IF;
    
    IF p_quota_limit <= 0 THEN
        RAISE EXCEPTION 'Quota limit must be positive: %', p_quota_limit;
    END IF;
    
    -- Try to increment usage if within quota (atomic operation)
    INSERT INTO user_monthly_usage (user_id, year_month, image_credits_used)
    VALUES (p_user_id, p_year_month, p_credits)
    ON CONFLICT (user_id, year_month)
    DO UPDATE SET 
        image_credits_used = user_monthly_usage.image_credits_used + p_credits,
        updated_at = NOW()
    WHERE user_monthly_usage.image_credits_used + p_credits <= p_quota_limit
    RETURNING image_credits_used INTO new_usage;
    
    -- Check if update succeeded (within quota)
    IF NOT FOUND THEN
        -- Get current usage to return accurate remaining count
        SELECT COALESCE(image_credits_used, 0) 
        INTO current_usage
        FROM user_monthly_usage 
        WHERE user_id = p_user_id AND year_month = p_year_month;
        
        current_usage := COALESCE(current_usage, 0);
        
        -- Return failure with remaining quota
        RETURN QUERY SELECT false, GREATEST(0, p_quota_limit - current_usage);
        RETURN;
    END IF;
    
    -- Return success with remaining quota
    RETURN QUERY SELECT true, GREATEST(0, p_quota_limit - new_usage);
END;
$$;


ALTER FUNCTION "public"."atomic_image_check_and_increment"("p_user_id" "uuid", "p_credits" integer, "p_year_month" "text", "p_quota_limit" integer) OWNER TO "postgres";


COMMENT ON FUNCTION "public"."atomic_image_check_and_increment"("p_user_id" "uuid", "p_credits" integer, "p_year_month" "text", "p_quota_limit" integer) IS 'SECURITY: Only callable by service_role to prevent quota manipulation';



CREATE OR REPLACE FUNCTION "public"."atomic_image_check_and_increment_safe"("p_user_id" "uuid", "p_credits" integer, "p_year_month" "text", "p_quota_limit" integer) RETURNS TABLE("success" boolean, "remaining" integer)
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    attempt_count integer := 0;
    max_attempts integer := 3;
    current_usage integer;
    new_usage integer;
BEGIN
    LOOP
        BEGIN
            -- Input validation
            IF p_credits <= 0 THEN
                RAISE EXCEPTION 'Credit usage must be positive: %', p_credits;
            END IF;
            
            IF p_quota_limit <= 0 THEN
                RAISE EXCEPTION 'Quota limit must be positive: %', p_quota_limit;
            END IF;
            
            -- Try to increment usage if within quota (atomic operation)
            INSERT INTO user_monthly_usage (user_id, year_month, image_credits_used)
            VALUES (p_user_id, p_year_month, p_credits)
            ON CONFLICT (user_id, year_month)
            DO UPDATE SET 
                image_credits_used = user_monthly_usage.image_credits_used + p_credits,
                updated_at = NOW()
            WHERE user_monthly_usage.image_credits_used + p_credits <= p_quota_limit
            RETURNING image_credits_used INTO new_usage;
            
            -- Check if update succeeded (within quota)
            IF FOUND THEN
                RETURN QUERY SELECT true, p_quota_limit - new_usage;
                RETURN;
            END IF;
            
            -- Get current usage to return accurate remaining count
            SELECT COALESCE(image_credits_used, 0) 
            INTO current_usage
            FROM user_monthly_usage 
            WHERE user_id = p_user_id AND year_month = p_year_month;
            
            current_usage := COALESCE(current_usage, 0);
            
            -- Return failure with remaining quota
            RETURN QUERY SELECT false, GREATEST(0, p_quota_limit - current_usage);
            RETURN;
            
        EXCEPTION 
            WHEN unique_violation THEN
                attempt_count := attempt_count + 1;
                IF attempt_count >= max_attempts THEN
                    RAISE NOTICE 'Max retry attempts exceeded for image quota operation: user_id=%, attempts=%', p_user_id, max_attempts;
                    RETURN QUERY SELECT false, 0;
                    RETURN;
                END IF;
                PERFORM pg_sleep(0.01 * attempt_count);
        END;
    END LOOP;
END;
$$;


ALTER FUNCTION "public"."atomic_image_check_and_increment_safe"("p_user_id" "uuid", "p_credits" integer, "p_year_month" "text", "p_quota_limit" integer) OWNER TO "postgres";


COMMENT ON FUNCTION "public"."atomic_image_check_and_increment_safe"("p_user_id" "uuid", "p_credits" integer, "p_year_month" "text", "p_quota_limit" integer) IS 'SECURITY: Only callable by service_role - prevents quota manipulation attacks';



CREATE OR REPLACE FUNCTION "public"."atomic_token_check_and_increment"("p_user_id" "uuid", "p_tokens" bigint, "p_year_month" "text", "p_quota_limit" bigint) RETURNS TABLE("success" boolean, "remaining" bigint)
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    result_row RECORD;
    current_tokens bigint;
BEGIN
    -- First, try to update existing row atomically with quota check
    UPDATE user_monthly_usage 
    SET 
        tokens_used = tokens_used + p_tokens,
        updated_at = NOW()
    WHERE user_id = p_user_id 
      AND year_month = p_year_month 
      AND tokens_used + p_tokens <= p_quota_limit
    RETURNING tokens_used INTO current_tokens;
    
    -- If we updated a row, return success
    IF FOUND THEN
        RETURN QUERY SELECT true, p_quota_limit - current_tokens;
        RETURN;
    END IF;
    
    -- Check if row exists but quota would be exceeded
    SELECT tokens_used INTO current_tokens
    FROM user_monthly_usage 
    WHERE user_id = p_user_id AND year_month = p_year_month;
    
    IF FOUND THEN
        -- Row exists but quota exceeded
        RETURN QUERY SELECT false, p_quota_limit - current_tokens;
        RETURN;
    END IF;
    
    -- No row exists, check if initial usage would exceed quota
    IF p_tokens > p_quota_limit THEN
        RETURN QUERY SELECT false, p_quota_limit;
        RETURN;
    END IF;
    
    -- Safe to create new row
    INSERT INTO user_monthly_usage (user_id, year_month, tokens_used, created_at, updated_at)
    VALUES (p_user_id, p_year_month, p_tokens, NOW(), NOW());
    
    RETURN QUERY SELECT true, p_quota_limit - p_tokens;
END;
$$;


ALTER FUNCTION "public"."atomic_token_check_and_increment"("p_user_id" "uuid", "p_tokens" bigint, "p_year_month" "text", "p_quota_limit" bigint) OWNER TO "postgres";


COMMENT ON FUNCTION "public"."atomic_token_check_and_increment"("p_user_id" "uuid", "p_tokens" bigint, "p_year_month" "text", "p_quota_limit" bigint) IS 'SECURITY: Only callable by service_role to prevent quota manipulation';



CREATE OR REPLACE FUNCTION "public"."atomic_token_check_and_increment_safe"("p_user_id" "uuid", "p_tokens" bigint, "p_year_month" "text", "p_quota_limit" bigint) RETURNS TABLE("success" boolean, "remaining" bigint)
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    attempt_count integer := 0;
    max_attempts integer := 3;
    current_tokens bigint;
    new_usage bigint;
BEGIN
    LOOP
        BEGIN
            -- Try to update existing row atomically with quota check
            UPDATE user_monthly_usage 
            SET 
                tokens_used = tokens_used + p_tokens,
                updated_at = NOW()
            WHERE user_id = p_user_id 
              AND year_month = p_year_month 
              AND tokens_used + p_tokens <= p_quota_limit
            RETURNING tokens_used INTO new_usage;
            
            -- If we updated a row, return success
            IF FOUND THEN
                RETURN QUERY SELECT true, p_quota_limit - new_usage;
                RETURN;
            END IF;
            
            -- Check if row exists but quota would be exceeded
            SELECT tokens_used INTO current_tokens
            FROM user_monthly_usage 
            WHERE user_id = p_user_id AND year_month = p_year_month;
            
            IF FOUND THEN
                -- Row exists but quota exceeded
                RETURN QUERY SELECT false, p_quota_limit - current_tokens;
                RETURN;
            END IF;
            
            -- No row exists, check if initial usage would exceed quota
            IF p_tokens > p_quota_limit THEN
                RETURN QUERY SELECT false, p_quota_limit;
                RETURN;
            END IF;
            
            -- Safe to create new row
            INSERT INTO user_monthly_usage (user_id, year_month, tokens_used, created_at, updated_at)
            VALUES (p_user_id, p_year_month, p_tokens, NOW(), NOW());
            
            RETURN QUERY SELECT true, p_quota_limit - p_tokens;
            RETURN; -- Success, exit the loop
            
        EXCEPTION 
            WHEN unique_violation THEN
                attempt_count := attempt_count + 1;
                IF attempt_count >= max_attempts THEN
                    -- Log the retry failure and return error
                    RAISE NOTICE 'Max retry attempts exceeded for token quota operation: user_id=%, attempts=%', p_user_id, max_attempts;
                    RETURN QUERY SELECT false, 0::bigint;
                    RETURN;
                END IF;
                -- Exponential backoff: wait longer between retries
                PERFORM pg_sleep(0.01 * attempt_count);
        END;
    END LOOP;
END;
$$;


ALTER FUNCTION "public"."atomic_token_check_and_increment_safe"("p_user_id" "uuid", "p_tokens" bigint, "p_year_month" "text", "p_quota_limit" bigint) OWNER TO "postgres";


COMMENT ON FUNCTION "public"."atomic_token_check_and_increment_safe"("p_user_id" "uuid", "p_tokens" bigint, "p_year_month" "text", "p_quota_limit" bigint) IS 'SECURITY: Only callable by service_role - prevents quota manipulation attacks';



CREATE OR REPLACE FUNCTION "public"."check_and_increment_webhook_rate_limit"("p_rate_key" "text", "p_max_requests" integer DEFAULT 100, "p_window_seconds" integer DEFAULT 300) RETURNS TABLE("allowed" boolean, "current_count" integer, "reset_time" timestamp with time zone)
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    rate_record record;
    window_start timestamp with time zone;
    expires_at timestamp with time zone;
    now_time timestamp with time zone;
BEGIN
    now_time := CLOCK_TIMESTAMP();
    window_start := now_time;
    expires_at := now_time + (p_window_seconds * interval '1 second');

    -- Try to get existing rate limit record
    SELECT * INTO rate_record
    FROM webhook_rate_limits
    WHERE rate_key = p_rate_key;

    -- If no record exists or record has expired, create/reset
    IF NOT FOUND OR rate_record.expires_at <= now_time THEN
        INSERT INTO webhook_rate_limits (rate_key, request_count, window_start, expires_at, updated_at)
        VALUES (p_rate_key, 1, window_start, expires_at, now_time)
        ON CONFLICT (rate_key)
        DO UPDATE SET
            request_count = 1,
            window_start = EXCLUDED.window_start,
            expires_at = EXCLUDED.expires_at,
            updated_at = EXCLUDED.updated_at;

        RETURN QUERY SELECT true, 1, expires_at;
        RETURN;
    END IF;

    -- Check if we can increment within the limit
    IF rate_record.request_count >= p_max_requests THEN
        -- Rate limit exceeded
        RETURN QUERY SELECT false, rate_record.request_count, rate_record.expires_at;
        RETURN;
    END IF;

    -- Increment the counter
    UPDATE webhook_rate_limits
    SET
        request_count = request_count + 1,
        updated_at = now_time
    WHERE rate_key = p_rate_key
    RETURNING request_count, expires_at INTO rate_record.request_count, rate_record.expires_at;

    RETURN QUERY SELECT true, rate_record.request_count, rate_record.expires_at;
END;
$$;


ALTER FUNCTION "public"."check_and_increment_webhook_rate_limit"("p_rate_key" "text", "p_max_requests" integer, "p_window_seconds" integer) OWNER TO "postgres";


COMMENT ON FUNCTION "public"."check_and_increment_webhook_rate_limit"("p_rate_key" "text", "p_max_requests" integer, "p_window_seconds" integer) IS 'Atomically checks and increments webhook rate limit counters';



CREATE OR REPLACE FUNCTION "public"."cleanup_expired_rate_limits"() RETURNS "void"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  DELETE FROM rate_limits WHERE reset_time < NOW();
END;
$$;


ALTER FUNCTION "public"."cleanup_expired_rate_limits"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."cleanup_expired_token_reservations"() RETURNS "void"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    -- Mark expired pending reservations as expired
    UPDATE token_reservations 
    SET status = 'expired', committed_at = NOW()
    WHERE status = 'pending' AND expires_at <= NOW();
    
    -- Clean up old records (keep for 24 hours for debugging)
    DELETE FROM token_reservations 
    WHERE expires_at <= NOW() - interval '24 hours';
END;
$$;


ALTER FUNCTION "public"."cleanup_expired_token_reservations"() OWNER TO "postgres";


COMMENT ON FUNCTION "public"."cleanup_expired_token_reservations"() IS 'Background job to clean up expired reservations';



CREATE OR REPLACE FUNCTION "public"."cleanup_expired_webhook_rate_limits"() RETURNS "void"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    DELETE FROM webhook_rate_limits
    WHERE expires_at <= CLOCK_TIMESTAMP() - interval '1 hour'; -- Keep records for 1 hour after expiry for debugging
END;
$$;


ALTER FUNCTION "public"."cleanup_expired_webhook_rate_limits"() OWNER TO "postgres";


COMMENT ON FUNCTION "public"."cleanup_expired_webhook_rate_limits"() IS 'Cleanup job for expired rate limit records';



CREATE OR REPLACE FUNCTION "public"."cleanup_monthly_purchase_tracking"() RETURNS "void"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    -- Zero out purchased amounts in monthly usage (already migrated to persistent balances)
    UPDATE user_monthly_usage 
    SET 
        tokens_purchased = 0,
        image_credits_purchased = 0,
        updated_at = NOW()
    WHERE tokens_purchased > 0 OR image_credits_purchased > 0;
    
    -- Also clean up negative usage values (legacy purchase tracking)
    UPDATE user_monthly_usage
    SET
        tokens_used = GREATEST(0, tokens_used),
        image_credits_used = GREATEST(0, image_credits_used),
        updated_at = NOW()
    WHERE tokens_used < 0 OR image_credits_used < 0;
END;
$$;


ALTER FUNCTION "public"."cleanup_monthly_purchase_tracking"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."commit_token_reservation"("p_reservation_id" "text", "p_actual_tokens" bigint DEFAULT NULL::bigint) RETURNS TABLE("success" boolean, "delta_tokens" bigint)
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    reservation_record record;
    token_delta bigint;
BEGIN
    -- Get the reservation
    SELECT * INTO reservation_record 
    FROM token_reservations 
    WHERE reservation_id = p_reservation_id;
    
    IF NOT FOUND THEN
        RETURN QUERY SELECT false, 0::bigint;
        RETURN;
    END IF;
    
    -- Check if already committed or expired
    IF reservation_record.status != 'pending' THEN
        RETURN QUERY SELECT false, 0::bigint;
        RETURN;
    END IF;
    
    -- Check if expired
    IF reservation_record.expires_at <= NOW() THEN
        UPDATE token_reservations 
        SET status = 'expired', committed_at = NOW()
        WHERE reservation_id = p_reservation_id;
        
        RETURN QUERY SELECT false, 0::bigint;
        RETURN;
    END IF;
    
    -- Calculate delta if actual tokens provided
    IF p_actual_tokens IS NOT NULL THEN
        token_delta := p_actual_tokens - reservation_record.estimated_tokens;
    ELSE
        token_delta := 0;
    END IF;
    
    -- Mark as committed
    UPDATE token_reservations 
    SET 
        status = 'committed',
        actual_tokens = COALESCE(p_actual_tokens, estimated_tokens),
        committed_at = NOW()
    WHERE reservation_id = p_reservation_id;
    
    RETURN QUERY SELECT true, token_delta;
END;
$$;


ALTER FUNCTION "public"."commit_token_reservation"("p_reservation_id" "text", "p_actual_tokens" bigint) OWNER TO "postgres";


COMMENT ON FUNCTION "public"."commit_token_reservation"("p_reservation_id" "text", "p_actual_tokens" bigint) IS 'Commits a reservation and calculates actual vs estimated token delta';



CREATE OR REPLACE FUNCTION "public"."create_token_reservation"("p_user_id" "uuid", "p_tokens" bigint, "p_model_id" "text", "p_reservation_ttl_minutes" integer DEFAULT 30) RETURNS TABLE("reservation_id" "text", "success" boolean)
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    new_reservation_id text;
    expires_at timestamp with time zone;
BEGIN
    -- Generate unique reservation ID
    new_reservation_id := 'reserve_' || extract(epoch from now())::bigint || '_' || p_user_id || '_' || p_tokens;
    expires_at := NOW() + (p_reservation_ttl_minutes * interval '1 minute');
    
    -- Insert reservation record
    INSERT INTO token_reservations (
        reservation_id, 
        user_id, 
        tokens_reserved, 
        model_id, 
        status, 
        estimated_tokens,
        expires_at
    ) VALUES (
        new_reservation_id, 
        p_user_id, 
        p_tokens, 
        p_model_id, 
        'pending', 
        p_tokens,
        expires_at
    );
    
    RETURN QUERY SELECT new_reservation_id, true;
END;
$$;


ALTER FUNCTION "public"."create_token_reservation"("p_user_id" "uuid", "p_tokens" bigint, "p_model_id" "text", "p_reservation_ttl_minutes" integer) OWNER TO "postgres";


COMMENT ON FUNCTION "public"."create_token_reservation"("p_user_id" "uuid", "p_tokens" bigint, "p_model_id" "text", "p_reservation_ttl_minutes" integer) IS 'Creates a persistent token reservation with expiry';



CREATE OR REPLACE FUNCTION "public"."decrement_daily_token_usage"("p_user_id" "text", "p_date" "text", "p_tokens" bigint) RETURNS "void"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    -- Input validation
    IF p_tokens <= 0 THEN
        RAISE EXCEPTION 'Token refund amount must be positive: %', p_tokens;
    END IF;
    
    -- Decrement token usage (ensure it doesn't go below 0)
    UPDATE user_daily_usage
    SET 
        tokens_used = GREATEST(0, COALESCE(tokens_used, 0) - p_tokens),
        updated_at = NOW()
    WHERE user_id = p_user_id AND date = p_date;
    
    -- If no row exists, don't create one (nothing to refund)
END;
$$;


ALTER FUNCTION "public"."decrement_daily_token_usage"("p_user_id" "text", "p_date" "text", "p_tokens" bigint) OWNER TO "postgres";


COMMENT ON FUNCTION "public"."decrement_daily_token_usage"("p_user_id" "text", "p_date" "text", "p_tokens" bigint) IS 'Refunds daily token usage for free tier users when LLM requests fail';



CREATE OR REPLACE FUNCTION "public"."decrement_daily_token_usage"("p_user_id" "uuid", "p_date" "date", "p_tokens" bigint) RETURNS "void"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    -- Input validation
    IF p_tokens <= 0 THEN
        RAISE EXCEPTION 'Token refund amount must be positive: %', p_tokens;
    END IF;
    
    -- Decrement token usage (ensure it doesn't go below 0)
    UPDATE user_daily_usage
    SET 
        tokens_used = GREATEST(0, COALESCE(tokens_used, 0) - p_tokens),
        updated_at = NOW()
    WHERE user_id = p_user_id AND date = p_date;
    
    -- If no row exists, don't create one (nothing to refund)
END;
$$;


ALTER FUNCTION "public"."decrement_daily_token_usage"("p_user_id" "uuid", "p_date" "date", "p_tokens" bigint) OWNER TO "postgres";


COMMENT ON FUNCTION "public"."decrement_daily_token_usage"("p_user_id" "uuid", "p_date" "date", "p_tokens" bigint) IS 'FIXED: Now uses proper UUID parameter type for efficient index usage';



CREATE OR REPLACE FUNCTION "public"."decrement_monthly_image_usage"("p_user_id" "uuid", "p_year_month" "text", "p_credits" integer) RETURNS "void"
    LANGUAGE "plpgsql"
    AS $$
  BEGIN
    UPDATE user_monthly_usage
    SET image_credits_used = GREATEST(0, COALESCE(image_credits_used, 0) - p_credits),
        updated_at = NOW()
    WHERE user_id = p_user_id AND year_month = p_year_month;
  END;
  $$;


ALTER FUNCTION "public"."decrement_monthly_image_usage"("p_user_id" "uuid", "p_year_month" "text", "p_credits" integer) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."decrement_monthly_token_usage"("p_user_id" "uuid", "p_year_month" "text", "p_tokens" bigint) RETURNS "void"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    -- Input validation
    IF p_tokens <= 0 THEN
        RAISE EXCEPTION 'Token refund amount must be positive: %', p_tokens;
    END IF;
    
    -- Decrement token usage (ensure it doesn't go below 0)
    UPDATE user_monthly_usage
    SET 
        tokens_used = GREATEST(0, COALESCE(tokens_used, 0) - p_tokens),
        updated_at = NOW()
    WHERE user_id = p_user_id AND year_month = p_year_month;
    
    -- If no row exists, don't create one (nothing to refund)
END;
$$;


ALTER FUNCTION "public"."decrement_monthly_token_usage"("p_user_id" "uuid", "p_year_month" "text", "p_tokens" bigint) OWNER TO "postgres";


COMMENT ON FUNCTION "public"."decrement_monthly_token_usage"("p_user_id" "uuid", "p_year_month" "text", "p_tokens" bigint) IS 'Refunds monthly token usage for paid tier subscription quota when LLM requests fail';



CREATE OR REPLACE FUNCTION "public"."decrement_user_comparison_count"("p_user_id" "text", "p_date" "text") RETURNS "void"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  UPDATE user_daily_usage 
  SET 
    comparison_count = GREATEST(0, COALESCE(comparison_count, 0) - 1),
    updated_at = NOW()
  WHERE user_id = p_user_id AND date = p_date::DATE;
  
  -- If no row exists, don't create one (nothing to decrement)
END;
$$;


ALTER FUNCTION "public"."decrement_user_comparison_count"("p_user_id" "text", "p_date" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."decrement_user_image_count"("p_user_id" "text", "p_date" "text") RETURNS "void"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  UPDATE user_daily_usage 
  SET 
    image_count = GREATEST(0, COALESCE(image_count, 0) - 1),
    updated_at = NOW()
  WHERE user_id = p_user_id::UUID AND date = p_date::DATE;
END;
$$;


ALTER FUNCTION "public"."decrement_user_image_count"("p_user_id" "text", "p_date" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_reservation_stats"() RETURNS TABLE("pending_count" bigint, "expired_count" bigint, "committed_count" bigint, "refunded_count" bigint, "total_tokens_reserved" bigint)
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) FILTER (WHERE status = 'pending') as pending_count,
        COUNT(*) FILTER (WHERE status = 'expired') as expired_count,
        COUNT(*) FILTER (WHERE status = 'committed') as committed_count,
        COUNT(*) FILTER (WHERE status = 'refunded') as refunded_count,
        COALESCE(SUM(tokens_reserved) FILTER (WHERE status = 'pending'), 0) as total_tokens_reserved
    FROM token_reservations
    WHERE created_at >= NOW() - interval '24 hours';
END;
$$;


ALTER FUNCTION "public"."get_reservation_stats"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_user_persistent_balance"("p_user_id" "uuid") RETURNS TABLE("tokens_available" bigint, "tokens_purchased_total" bigint, "tokens_used_total" bigint, "credits_available" integer, "credits_purchased_total" integer, "credits_used_total" integer)
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COALESCE(ub.tokens_purchased_total - ub.tokens_used_from_purchased, 0) as tokens_available,
        COALESCE(ub.tokens_purchased_total, 0) as tokens_purchased_total,
        COALESCE(ub.tokens_used_from_purchased, 0) as tokens_used_total,
        COALESCE(ub.credits_purchased_total - ub.credits_used_from_purchased, 0) as credits_available,
        COALESCE(ub.credits_purchased_total, 0) as credits_purchased_total,
        COALESCE(ub.credits_used_from_purchased, 0) as credits_used_total
    FROM user_balances ub
    WHERE ub.user_id = p_user_id;
    
    -- If no record exists, return zeros
    IF NOT FOUND THEN
        RETURN QUERY SELECT 0::bigint, 0::bigint, 0::bigint, 0::integer, 0::integer, 0::integer;
    END IF;
END;
$$;


ALTER FUNCTION "public"."get_user_persistent_balance"("p_user_id" "uuid") OWNER TO "postgres";


COMMENT ON FUNCTION "public"."get_user_persistent_balance"("p_user_id" "uuid") IS 'Returns user persistent balance (purchased tokens/credits that do not expire)';



CREATE OR REPLACE FUNCTION "public"."increment_comparison_count"("p_user_id" "uuid", "p_year_month" "text") RETURNS "void"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  INSERT INTO user_monthly_usage (user_id, year_month, comparison_count)
  VALUES (p_user_id, p_year_month, 1)
  ON CONFLICT (user_id, year_month)
  DO UPDATE SET 
    comparison_count = COALESCE(user_monthly_usage.comparison_count, 0) + 1,
    updated_at = NOW();
END;
$$;


ALTER FUNCTION "public"."increment_comparison_count"("p_user_id" "uuid", "p_year_month" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."increment_daily_token_usage"("p_user_id" "text", "p_date" "text", "p_tokens" integer) RETURNS TABLE("success" boolean)
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    result_success boolean;
    remaining_tokens integer;
BEGIN
    -- Use the new safe function with default 10K limit
    SELECT success INTO result_success
    FROM increment_daily_token_usage_with_limit(p_user_id, p_date, p_tokens, 10000);
    
    RETURN QUERY SELECT result_success;
END;
$$;


ALTER FUNCTION "public"."increment_daily_token_usage"("p_user_id" "text", "p_date" "text", "p_tokens" integer) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."increment_daily_token_usage"("p_user_id" "uuid", "p_date" "date", "p_tokens" integer) RETURNS TABLE("success" boolean)
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    result_success boolean;
BEGIN
    -- Use the new safe function with default 10K limit
    SELECT success INTO result_success
    FROM increment_daily_token_usage_with_limit(p_user_id, p_date, p_tokens, 10000);
    
    RETURN QUERY SELECT result_success;
END;
$$;


ALTER FUNCTION "public"."increment_daily_token_usage"("p_user_id" "uuid", "p_date" "date", "p_tokens" integer) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."increment_daily_token_usage_with_limit"("p_user_id" "text", "p_date" "text", "p_tokens" integer, "p_daily_limit" integer DEFAULT 10000) RETURNS TABLE("success" boolean, "remaining" integer)
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    current_tokens integer;
BEGIN
    -- Try to update existing row atomically with quota check
    UPDATE user_daily_usage 
    SET 
        tokens_used = tokens_used + p_tokens,
        updated_at = NOW()
    WHERE user_id = p_user_id::UUID 
      AND date = p_date::DATE
      AND tokens_used + p_tokens <= p_daily_limit
    RETURNING tokens_used INTO current_tokens;
    
    -- If we updated a row, return success
    IF FOUND THEN
        RETURN QUERY SELECT true, p_daily_limit - current_tokens;
        RETURN;
    END IF;
    
    -- Check if row exists but quota would be exceeded
    SELECT tokens_used INTO current_tokens
    FROM user_daily_usage 
    WHERE user_id = p_user_id::UUID AND date = p_date::DATE;
    
    IF FOUND THEN
        -- Row exists but quota exceeded
        RETURN QUERY SELECT false, p_daily_limit - current_tokens;
        RETURN;
    END IF;
    
    -- No row exists, check if initial usage would exceed quota
    IF p_tokens > p_daily_limit THEN
        RETURN QUERY SELECT false, p_daily_limit;
        RETURN;
    END IF;
    
    -- Safe to create new row
    INSERT INTO user_daily_usage (user_id, date, tokens_used, message_count, created_at, updated_at)
    VALUES (p_user_id::UUID, p_date::DATE, p_tokens, 0, NOW(), NOW());
    
    RETURN QUERY SELECT true, p_daily_limit - p_tokens;
END;
$$;


ALTER FUNCTION "public"."increment_daily_token_usage_with_limit"("p_user_id" "text", "p_date" "text", "p_tokens" integer, "p_daily_limit" integer) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."increment_daily_token_usage_with_limit"("p_user_id" "uuid", "p_date" "date", "p_tokens" integer, "p_daily_limit" integer DEFAULT 10000) RETURNS TABLE("success" boolean, "remaining" integer)
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    current_tokens integer;
BEGIN
    -- Try to update existing row atomically with quota check
    UPDATE user_daily_usage 
    SET 
        tokens_used = tokens_used + p_tokens,
        updated_at = NOW()
    WHERE user_id = p_user_id 
      AND date = p_date
      AND tokens_used + p_tokens <= p_daily_limit
    RETURNING tokens_used INTO current_tokens;
    
    -- If we updated a row, return success
    IF FOUND THEN
        RETURN QUERY SELECT true, p_daily_limit - current_tokens;
        RETURN;
    END IF;
    
    -- Check if row exists but quota would be exceeded
    SELECT tokens_used INTO current_tokens
    FROM user_daily_usage 
    WHERE user_id = p_user_id AND date = p_date;
    
    IF FOUND THEN
        -- Row exists but quota exceeded
        RETURN QUERY SELECT false, p_daily_limit - current_tokens;
        RETURN;
    END IF;
    
    -- No row exists, check if initial usage would exceed quota
    IF p_tokens > p_daily_limit THEN
        RETURN QUERY SELECT false, p_daily_limit;
        RETURN;
    END IF;
    
    -- Safe to create new row
    INSERT INTO user_daily_usage (user_id, date, tokens_used, message_count, created_at, updated_at)
    VALUES (p_user_id, p_date, p_tokens, 0, NOW(), NOW());
    
    RETURN QUERY SELECT true, p_daily_limit - p_tokens;
END;
$$;


ALTER FUNCTION "public"."increment_daily_token_usage_with_limit"("p_user_id" "uuid", "p_date" "date", "p_tokens" integer, "p_daily_limit" integer) OWNER TO "postgres";


COMMENT ON FUNCTION "public"."increment_daily_token_usage_with_limit"("p_user_id" "uuid", "p_date" "date", "p_tokens" integer, "p_daily_limit" integer) IS 'FIXED: Now uses proper UUID and DATE parameter types for efficient index usage';



CREATE OR REPLACE FUNCTION "public"."increment_monthly_token_usage"("p_user_id" "uuid", "p_year_month" "text", "p_tokens" bigint) RETURNS "void"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  INSERT INTO user_monthly_usage (user_id, year_month, tokens_used)
  VALUES (p_user_id, p_year_month, p_tokens)
  ON CONFLICT (user_id, year_month)
  DO UPDATE SET 
    tokens_used = user_monthly_usage.tokens_used + p_tokens,
    updated_at = NOW();
END;
$$;


ALTER FUNCTION "public"."increment_monthly_token_usage"("p_user_id" "uuid", "p_year_month" "text", "p_tokens" bigint) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."increment_user_comparison_count"("p_user_id" "uuid", "p_date" "text") RETURNS TABLE("success" boolean)
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  INSERT INTO user_daily_usage (user_id, date, message_count, image_count, comparison_count)
  VALUES (p_user_id, p_date::DATE, 0, 0, 1)
  ON CONFLICT (user_id, date)
  DO UPDATE SET
    comparison_count = COALESCE(user_daily_usage.comparison_count, 0) + 1,
    updated_at = NOW();

  RETURN QUERY SELECT TRUE;
END;
$$;


ALTER FUNCTION "public"."increment_user_comparison_count"("p_user_id" "uuid", "p_date" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."increment_user_image_count"("p_user_id" "text", "p_date" "text") RETURNS "void"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  INSERT INTO user_daily_usage (user_id, date, message_count, image_count)
  VALUES (p_user_id::UUID, p_date::DATE, 0, 1)
  ON CONFLICT (user_id, date)
  DO UPDATE SET 
    image_count = COALESCE(user_daily_usage.image_count, 0) + 1,
    updated_at = NOW();
END;
$$;


ALTER FUNCTION "public"."increment_user_image_count"("p_user_id" "text", "p_date" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."increment_user_message_count"("p_user_id" "text", "p_date" "text") RETURNS TABLE("success" boolean)
    LANGUAGE "plpgsql"
    SET "search_path" TO 'public'
    AS $$
BEGIN
  -- Insert or update the user's daily usage count
  INSERT INTO user_daily_usage (user_id, date, message_count, tokens_used, created_at, updated_at)
  VALUES (p_user_id, p_date, 1, 0, NOW(), NOW())
  ON CONFLICT (user_id, date)
  DO UPDATE SET 
    message_count = user_daily_usage.message_count + 1,
    updated_at = NOW();
    
  RETURN QUERY SELECT TRUE as success;
END;
$$;


ALTER FUNCTION "public"."increment_user_message_count"("p_user_id" "text", "p_date" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."is_valid_session"("session_id" "text") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO 'public'
    AS $_$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM anonymous_sessions
    WHERE session_id = $1
  );
END;
$_$;


ALTER FUNCTION "public"."is_valid_session"("session_id" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."match_chunks"("p_workspace" "uuid", "p_query" "public"."vector", "p_topk" integer DEFAULT 10, "p_min_sim" double precision DEFAULT 0.25) RETURNS TABLE("id" bigint, "chunk_text" "text", "similarity" double precision)
    LANGUAGE "sql" STABLE
    AS $$
  select distinct on (left(chunk_text,120))          -- deduplicate overlaps
         id,
         chunk_text,
         1 - (embedding <=> p_query) as similarity   -- cosine sim
  from   workspace_embeddings
  where  workspace_id = p_workspace
  order  by left(chunk_text,120),
           embedding <=> p_query
  limit  p_topk
$$;


ALTER FUNCTION "public"."match_chunks"("p_workspace" "uuid", "p_query" "public"."vector", "p_topk" integer, "p_min_sim" double precision) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."match_facts"("p_workspace" "uuid", "p_search" "text", "p_topk" integer DEFAULT 10) RETURNS TABLE("key" "text", "value" "jsonb")
    LANGUAGE "sql" STABLE
    AS $$with q as ( select websearch_to_tsquery(p_search) t )
  select f.key, f.value
  from   workspace_facts f, q
  where similarity(f.key,   p_search) > 0.2
   or similarity(f.value::text, p_search) > 0.2
order by greatest(similarity(f.key, p_search),
                  similarity(f.value::text, p_search)) desc
  limit  p_topk$$;


ALTER FUNCTION "public"."match_facts"("p_workspace" "uuid", "p_search" "text", "p_topk" integer) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."match_notes"("p_workspace" "uuid", "p_query" "public"."vector", "p_topk" integer DEFAULT 6) RETURNS TABLE("id" "uuid", "body" "text", "similarity" double precision)
    LANGUAGE "sql" STABLE
    AS $$
  select id,
         body,
         1 - (embedding <=> p_query) as similarity
  from   workspace_notes
  where  workspace_id = p_workspace
  order  by embedding <=> p_query
  limit  p_topk
$$;


ALTER FUNCTION "public"."match_notes"("p_workspace" "uuid", "p_query" "public"."vector", "p_topk" integer) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."process_stripe_webhook"("p_event_id" "text", "p_event_type" "text", "p_event_data" "jsonb" DEFAULT NULL::"jsonb") RETURNS TABLE("already_processed" boolean)
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  -- Try to insert the event, which will fail if it already exists due to unique constraint
  BEGIN
    INSERT INTO stripe_events (event_id, event_type, event_data, created_at)
    VALUES (p_event_id, p_event_type, p_event_data, NOW());
    
    -- If insert succeeds, event is new
    RETURN QUERY SELECT FALSE as already_processed;
    
  EXCEPTION WHEN unique_violation THEN
    -- If unique constraint violation, event already exists
    RETURN QUERY SELECT TRUE as already_processed;
  END;
END;
$$;


ALTER FUNCTION "public"."process_stripe_webhook"("p_event_id" "text", "p_event_type" "text", "p_event_data" "jsonb") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."refund_persistent_token_balance"("p_user_id" "uuid", "p_tokens" bigint) RETURNS TABLE("success" boolean, "refunded" bigint)
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    current_used bigint;
    refund_amount bigint;
BEGIN
    -- Input validation
    IF p_tokens <= 0 THEN
        RAISE EXCEPTION 'Token refund amount must be positive: %', p_tokens;
    END IF;
    
    -- Get current used amount from persistent balance
    SELECT COALESCE(tokens_used_total, 0) 
    INTO current_used
    FROM user_balances 
    WHERE user_id = p_user_id;
    
    -- If no balance record or nothing used, cannot refund
    IF current_used IS NULL OR current_used = 0 THEN
        RETURN QUERY SELECT false, 0::bigint;
        RETURN;
    END IF;
    
    -- Calculate how much we can actually refund (limited by what was used)
    refund_amount := LEAST(p_tokens, current_used);
    
    -- Refund by reducing the used amount
    UPDATE user_balances
    SET 
        tokens_used_total = GREATEST(0, tokens_used_total - refund_amount),
        updated_at = NOW()
    WHERE user_id = p_user_id;
    
    -- Return success and actual refunded amount
    RETURN QUERY SELECT true, refund_amount;
END;
$$;


ALTER FUNCTION "public"."refund_persistent_token_balance"("p_user_id" "uuid", "p_tokens" bigint) OWNER TO "postgres";


COMMENT ON FUNCTION "public"."refund_persistent_token_balance"("p_user_id" "uuid", "p_tokens" bigint) IS 'Refunds persistent token balance for paid tier purchased tokens when LLM requests fail';



CREATE OR REPLACE FUNCTION "public"."refund_token_reservation"("p_reservation_id" "text") RETURNS TABLE("success" boolean, "tokens_to_refund" bigint)
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    reservation_record record;
BEGIN
    -- Get the reservation
    SELECT * INTO reservation_record 
    FROM token_reservations 
    WHERE reservation_id = p_reservation_id;
    
    IF NOT FOUND THEN
        RETURN QUERY SELECT false, 0::bigint;
        RETURN;
    END IF;
    
    -- Only refund pending reservations
    IF reservation_record.status != 'pending' THEN
        RETURN QUERY SELECT false, 0::bigint;
        RETURN;
    END IF;
    
    -- Mark as refunded
    UPDATE token_reservations 
    SET 
        status = 'refunded',
        committed_at = NOW()
    WHERE reservation_id = p_reservation_id;
    
    RETURN QUERY SELECT true, reservation_record.tokens_reserved;
END;
$$;


ALTER FUNCTION "public"."refund_token_reservation"("p_reservation_id" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_rate_limit_updated_at"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_rate_limit_updated_at"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_user_balances_updated_at"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_user_balances_updated_at"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."use_image_credit_grant"("p_user_id" "uuid", "p_credits" integer) RETURNS boolean
    LANGUAGE "plpgsql"
    AS $$
DECLARE
  grant_record RECORD;
  remaining_credits INTEGER;
BEGIN
  -- Find oldest grant with available credits
  SELECT * INTO grant_record
  FROM image_credit_grants
  WHERE user_id = p_user_id 
    AND (expires_at IS NULL OR expires_at > NOW())
    AND credits > COALESCE(used_credits, 0)
  ORDER BY created_at ASC
  LIMIT 1;
  
  IF NOT FOUND THEN
    RETURN FALSE; -- No available grants
  END IF;
  
  remaining_credits := grant_record.credits - COALESCE(grant_record.used_credits, 0);
  
  IF remaining_credits >= p_credits THEN
    -- Update the grant usage
    UPDATE image_credit_grants
    SET used_credits = COALESCE(used_credits, 0) + p_credits
    WHERE id = grant_record.id;
    RETURN TRUE;
  ELSE
    -- Not enough credits in this grant
    RETURN FALSE;
  END IF;
END;
$$;


ALTER FUNCTION "public"."use_image_credit_grant"("p_user_id" "uuid", "p_credits" integer) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."use_persistent_credit_balance"("p_user_id" "uuid", "p_credits" integer) RETURNS TABLE("success" boolean, "remaining_balance" integer)
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    remaining_credits integer;
BEGIN
    -- Input validation
    IF p_credits <= 0 THEN
        RAISE EXCEPTION 'Credit usage must be positive: %', p_credits;
    END IF;
    
    -- Single-statement conditional update to prevent race conditions
    UPDATE user_balances
    SET 
        credits_used_from_purchased = credits_used_from_purchased + p_credits,
        updated_at = NOW()
    WHERE user_id = p_user_id
      AND (credits_purchased_total - credits_used_from_purchased) >= p_credits
    RETURNING (credits_purchased_total - credits_used_from_purchased) INTO remaining_credits;
    
    -- Check if update succeeded
    IF NOT FOUND THEN
        -- Either user doesn't exist or insufficient balance
        -- Get current balance to return accurate information
        SELECT COALESCE(credits_purchased_total - credits_used_from_purchased, 0)
        INTO remaining_credits
        FROM user_balances 
        WHERE user_id = p_user_id;
        
        RETURN QUERY SELECT false, COALESCE(remaining_credits, 0);
        RETURN;
    END IF;
    
    -- Return success and remaining balance
    RETURN QUERY SELECT true, remaining_credits;
END;
$$;


ALTER FUNCTION "public"."use_persistent_credit_balance"("p_user_id" "uuid", "p_credits" integer) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."use_persistent_token_balance"("p_user_id" "uuid", "p_tokens" bigint) RETURNS TABLE("success" boolean, "remaining_balance" bigint)
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    remaining_tokens bigint;
BEGIN
    -- Input validation
    IF p_tokens <= 0 THEN
        RAISE EXCEPTION 'Token usage must be positive: %', p_tokens;
    END IF;
    
    -- Single-statement conditional update to prevent race conditions
    UPDATE user_balances
    SET 
        tokens_used_from_purchased = tokens_used_from_purchased + p_tokens,
        updated_at = NOW()
    WHERE user_id = p_user_id
      AND (tokens_purchased_total - tokens_used_from_purchased) >= p_tokens
    RETURNING (tokens_purchased_total - tokens_used_from_purchased) INTO remaining_tokens;
    
    -- Check if update succeeded
    IF NOT FOUND THEN
        -- Either user doesn't exist or insufficient balance
        -- Get current balance to return accurate information
        SELECT COALESCE(tokens_purchased_total - tokens_used_from_purchased, 0)
        INTO remaining_tokens
        FROM user_balances 
        WHERE user_id = p_user_id;
        
        RETURN QUERY SELECT false, COALESCE(remaining_tokens, 0);
        RETURN;
    END IF;
    
    -- Return success and remaining balance
    RETURN QUERY SELECT true, remaining_tokens;
END;
$$;


ALTER FUNCTION "public"."use_persistent_token_balance"("p_user_id" "uuid", "p_tokens" bigint) OWNER TO "postgres";

SET default_tablespace = '';

SET default_table_access_method = "heap";


CREATE TABLE IF NOT EXISTS "public"."anonymous_sessions" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "session_id" "text" NOT NULL,
    "message_count" integer DEFAULT 0,
    "created_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."anonymous_sessions" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."conversations" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "user_id" "uuid",
    "title" "text",
    "is_active" boolean DEFAULT true,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "anonymous_session_id" "uuid",
    "group_conversation_id" "uuid" NOT NULL,
    "is_comparison" boolean DEFAULT false,
    "comparison_index" integer DEFAULT 0,
    "state" "public"."conversation_state" DEFAULT 'healthy'::"public"."conversation_state",
    "last_model_used" "uuid",
    "imported_from_share_id" "text",
    CONSTRAINT "user_or_anonymous" CHECK (((("user_id" IS NOT NULL) AND ("anonymous_session_id" IS NULL)) OR (("user_id" IS NULL) AND ("anonymous_session_id" IS NOT NULL))))
);


ALTER TABLE "public"."conversations" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."group_conversations" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone,
    "user_id" "uuid" DEFAULT "auth"."uid"(),
    "anonymous_session_id" "uuid",
    "title" "text",
    "is_comparison" boolean NOT NULL,
    "is_active" boolean DEFAULT true NOT NULL,
    "is_favorite" boolean DEFAULT false NOT NULL,
    "is_temporary" boolean DEFAULT false,
    "workspace_id" "uuid"
);


ALTER TABLE "public"."group_conversations" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."image_credit_grants" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "credits" integer NOT NULL,
    "reason" "text" NOT NULL,
    "expires_at" timestamp with time zone,
    "used_credits" integer DEFAULT 0,
    "created_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."image_credit_grants" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."images" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "message_id" "uuid",
    "prompt" "text",
    "url" "text",
    "width" integer,
    "height" integer,
    "provider_job_id" "text",
    "safety_score" double precision,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "user_id" "uuid" DEFAULT "auth"."uid"()
);


ALTER TABLE "public"."images" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."llm_models" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "provider_id" "uuid",
    "name" "text" NOT NULL,
    "display_name" "text" NOT NULL,
    "max_tokens" integer,
    "is_active" boolean DEFAULT true,
    "config" "jsonb" DEFAULT '{}'::"jsonb" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "priority" smallint,
    "tier" "public"."subscription_plan" DEFAULT 'free'::"public"."subscription_plan" NOT NULL,
    "allows_file_upload" boolean DEFAULT true,
    "allows_search" boolean DEFAULT false,
    "openrouter_name" "text",
    "is_visible_by_default" boolean DEFAULT false,
    "allows_tool_usage" boolean DEFAULT false,
    "description" "text",
    "context_length" integer,
    "capabilities" "jsonb" DEFAULT '{}'::"jsonb",
    "architecture" "jsonb" DEFAULT '{}'::"jsonb",
    "pricing" "jsonb" DEFAULT '{}'::"jsonb",
    "supported_parameters" "jsonb" DEFAULT '[]'::"jsonb",
    "provider_specific_data" "jsonb" DEFAULT '{}'::"jsonb",
    "last_synced" timestamp with time zone
);


ALTER TABLE "public"."llm_models" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."llm_providers" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "name" "text" NOT NULL,
    "is_active" boolean DEFAULT true,
    "config" "jsonb" DEFAULT '{}'::"jsonb" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "display_name" "text",
    "openrouter_name" "text",
    "supports_native" boolean DEFAULT false,
    "supports_openrouter" boolean DEFAULT true
);


ALTER TABLE "public"."llm_providers" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."messages" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "conversation_id" "uuid",
    "role" "text" NOT NULL,
    "content" "text" NOT NULL,
    "tokens_used" integer,
    "model_id" "uuid",
    "provider_id" "uuid",
    "metadata" "jsonb" DEFAULT '{}'::"jsonb" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "parent_message_id" "uuid",
    "attachments" "jsonb",
    "annotations" "jsonb",
    "file_annotations" "jsonb",
    CONSTRAINT "messages_role_check" CHECK (("role" = ANY (ARRAY['user'::"text", 'assistant'::"text", 'system'::"text"])))
);


ALTER TABLE "public"."messages" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."prompt_ratings" (
    "user_id" "uuid" NOT NULL,
    "prompt_id" "uuid" NOT NULL,
    "rating" integer NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."prompt_ratings" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."prompt_templates" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "workspace_id" "uuid",
    "name" character varying(80),
    "template" "text",
    "owner_id" "uuid" DEFAULT "auth"."uid"()
);


ALTER TABLE "public"."prompt_templates" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."prompt_usage" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "prompt_id" "uuid" NOT NULL,
    "inserted_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "sent" boolean DEFAULT false NOT NULL
);


ALTER TABLE "public"."prompt_usage" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."prompt_versions" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "prompt_id" "uuid" NOT NULL,
    "body_md" "text" NOT NULL,
    "version" integer NOT NULL,
    "edited_by" "uuid" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."prompt_versions" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."prompts" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "owner_id" "uuid" NOT NULL,
    "workspace_id" "uuid",
    "visibility" "text" NOT NULL,
    "title" "text" NOT NULL,
    "body_md" "text" NOT NULL,
    "default_model" "text",
    "tags" "text"[] DEFAULT '{}'::"text"[],
    "rating_sum" integer DEFAULT 0 NOT NULL,
    "rating_count" integer DEFAULT 0 NOT NULL,
    "version" integer DEFAULT 1 NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone,
    CONSTRAINT "prompts_visibility_check" CHECK (("visibility" = ANY (ARRAY['private'::"text", 'workspace'::"text", 'public'::"text"])))
);


ALTER TABLE "public"."prompts" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."rate_limits" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "key" "text" NOT NULL,
    "count" integer DEFAULT 0 NOT NULL,
    "reset_time" timestamp with time zone NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."rate_limits" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."shared_conversations" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "original_conversation_id" "uuid",
    "shared_by_user_id" "uuid",
    "last_original_message_id" "uuid",
    "title" "text",
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."shared_conversations" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."shared_messages" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "shared_conversation_id" "uuid" NOT NULL,
    "original_message_id" "uuid",
    "parent_shared_message_id" "uuid",
    "role" "text" NOT NULL,
    "content" "text",
    "created_at" timestamp with time zone NOT NULL
);


ALTER TABLE "public"."shared_messages" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."stripe_events" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "event_id" "text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "event_type" "text",
    "event_data" "jsonb"
);


ALTER TABLE "public"."stripe_events" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."subscription_plan_configs" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "plan_name" "text" NOT NULL,
    "version" integer NOT NULL,
    "config" "jsonb" NOT NULL,
    "is_active" boolean DEFAULT false,
    "created_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."subscription_plan_configs" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."subscriptions" (
    "id" "uuid" NOT NULL,
    "user_id" "uuid" DEFAULT "auth"."uid"() NOT NULL,
    "plan" "public"."subscription_plan" NOT NULL,
    "status" "public"."subscription_status" NOT NULL,
    "current_period_end" timestamp without time zone NOT NULL,
    "cancel_at_period_end" boolean NOT NULL,
    "created_at" timestamp without time zone DEFAULT "now"(),
    "updated_at" timestamp without time zone,
    "stripe_customer_id" "text",
    "stripe_subscription_id" "text"
);


ALTER TABLE "public"."subscriptions" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."supabase_migrations" (
    "id" integer NOT NULL,
    "version" "text" NOT NULL,
    "name" "text" NOT NULL,
    "hash" "text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."supabase_migrations" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."supabase_migrations_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."supabase_migrations_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."supabase_migrations_id_seq" OWNED BY "public"."supabase_migrations"."id";



CREATE TABLE IF NOT EXISTS "public"."token_reservations" (
    "reservation_id" "text" NOT NULL,
    "user_id" "uuid" NOT NULL,
    "tokens_reserved" bigint NOT NULL,
    "model_id" "text" NOT NULL,
    "status" "text" NOT NULL,
    "estimated_tokens" bigint NOT NULL,
    "actual_tokens" bigint,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "expires_at" timestamp with time zone NOT NULL,
    "committed_at" timestamp with time zone,
    "metadata" "jsonb" DEFAULT '{}'::"jsonb",
    CONSTRAINT "token_reservations_status_check" CHECK (("status" = ANY (ARRAY['pending'::"text", 'committed'::"text", 'refunded'::"text", 'expired'::"text"])))
);


ALTER TABLE "public"."token_reservations" OWNER TO "postgres";


COMMENT ON TABLE "public"."token_reservations" IS 'Crash-safe token reservations - prevents quota loss on server crashes';



CREATE TABLE IF NOT EXISTS "public"."uploaded_files" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "file_hash" "text" NOT NULL,
    "storage_path" "text" NOT NULL,
    "original_filename" "text",
    "mime_type" "text",
    "size_bytes" bigint,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "uploader_user_id" "uuid"
);


ALTER TABLE "public"."uploaded_files" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."usage_logs" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "user_id" "uuid",
    "provider_id" "uuid",
    "model_id" "uuid",
    "tokens_used" integer NOT NULL,
    "cost" numeric(10,6),
    "status" "text" NOT NULL,
    "metadata" "jsonb" DEFAULT '{}'::"jsonb" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "conversation_id" "uuid" DEFAULT "gen_random_uuid"(),
    "message_id" "uuid" DEFAULT "gen_random_uuid"(),
    "prompt_tokens" integer,
    "completion_tokens" integer
);


ALTER TABLE "public"."usage_logs" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."user_balances" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "tokens_purchased_total" bigint DEFAULT 0 NOT NULL,
    "tokens_used_from_purchased" bigint DEFAULT 0 NOT NULL,
    "credits_purchased_total" integer DEFAULT 0 NOT NULL,
    "credits_used_from_purchased" integer DEFAULT 0 NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    CONSTRAINT "user_balances_credits_purchased_positive" CHECK (("credits_purchased_total" >= 0)),
    CONSTRAINT "user_balances_credits_used_not_exceed_purchased" CHECK (("credits_used_from_purchased" <= "credits_purchased_total")),
    CONSTRAINT "user_balances_credits_used_positive" CHECK (("credits_used_from_purchased" >= 0)),
    CONSTRAINT "user_balances_tokens_purchased_positive" CHECK (("tokens_purchased_total" >= 0)),
    CONSTRAINT "user_balances_tokens_used_not_exceed_purchased" CHECK (("tokens_used_from_purchased" <= "tokens_purchased_total")),
    CONSTRAINT "user_balances_tokens_used_positive" CHECK (("tokens_used_from_purchased" >= 0))
);


ALTER TABLE "public"."user_balances" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."user_daily_usage" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid" DEFAULT "auth"."uid"() NOT NULL,
    "date" "date" NOT NULL,
    "message_count" integer NOT NULL,
    "created_at" timestamp without time zone DEFAULT "now"(),
    "updated_at" timestamp without time zone DEFAULT "now"(),
    "image_count" integer,
    "comparison_count" integer DEFAULT 0,
    "tokens_used" integer DEFAULT 0,
    CONSTRAINT "chk_comparison_count_positive" CHECK (("comparison_count" >= 0)),
    CONSTRAINT "chk_daily_tokens_used_positive" CHECK (("tokens_used" >= 0))
);


ALTER TABLE "public"."user_daily_usage" OWNER TO "postgres";


COMMENT ON COLUMN "public"."user_daily_usage"."comparison_count" IS 'Daily comparison count for premium users (50/day limit)';



CREATE TABLE IF NOT EXISTS "public"."user_monthly_usage" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "year_month" "text" NOT NULL,
    "tokens_used" bigint DEFAULT 0,
    "image_credits_used" integer DEFAULT 0,
    "comparison_count" integer DEFAULT 0,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    CONSTRAINT "chk_image_credits_used_positive" CHECK (("image_credits_used" >= 0)),
    CONSTRAINT "chk_tokens_used_positive" CHECK (("tokens_used" >= 0))
);


ALTER TABLE "public"."user_monthly_usage" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."user_preferences" (
    "id" bigint NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "default_model_id" "uuid" DEFAULT "gen_random_uuid"(),
    "updated_at" timestamp with time zone,
    "hidden_model_ids" "jsonb",
    "user_id" "uuid" DEFAULT "auth"."uid"() NOT NULL,
    "explicitly_hidden_model_ids" "jsonb" DEFAULT '[]'::"jsonb" NOT NULL,
    "explicitly_shown_model_ids" "jsonb" DEFAULT '[]'::"jsonb" NOT NULL
);


ALTER TABLE "public"."user_preferences" OWNER TO "postgres";


ALTER TABLE "public"."user_preferences" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."user_settings_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);



CREATE TABLE IF NOT EXISTS "public"."webhook_rate_limits" (
    "rate_key" "text" NOT NULL,
    "request_count" integer DEFAULT 0 NOT NULL,
    "window_start" timestamp with time zone DEFAULT "now"() NOT NULL,
    "expires_at" timestamp with time zone NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."webhook_rate_limits" OWNER TO "postgres";


COMMENT ON TABLE "public"."webhook_rate_limits" IS 'Distributed rate limiting for webhook endpoints';



CREATE TABLE IF NOT EXISTS "public"."workspace_embeddings" (
    "id" bigint NOT NULL,
    "workspace_id" "uuid",
    "file_id" "uuid",
    "chunk_index" integer,
    "chunk_text" "text",
    "embedding" "public"."vector"(1536)
);


ALTER TABLE "public"."workspace_embeddings" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."workspace_embeddings_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."workspace_embeddings_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."workspace_embeddings_id_seq" OWNED BY "public"."workspace_embeddings"."id";



CREATE TABLE IF NOT EXISTS "public"."workspace_facts" (
    "id" bigint NOT NULL,
    "workspace_id" "uuid",
    "file_id" "uuid",
    "key" "text",
    "value" "jsonb",
    "created_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."workspace_facts" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."workspace_facts_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."workspace_facts_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."workspace_facts_id_seq" OWNED BY "public"."workspace_facts"."id";



CREATE TABLE IF NOT EXISTS "public"."workspace_files" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "workspace_id" "uuid",
    "filename" "text",
    "mime_type" "text",
    "byte_size" integer,
    "text_content" "text",
    "status" "text" DEFAULT 'pending'::"text",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "file_path" "text"
);


ALTER TABLE "public"."workspace_files" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."workspace_notes" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "workspace_id" "uuid",
    "title" character varying(120),
    "body" "text",
    "embedding" "public"."vector"(1536),
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."workspace_notes" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."workspace_questions" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "workspace_id" "uuid",
    "question" "text"
);


ALTER TABLE "public"."workspace_questions" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."workspaces" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "owner_id" "uuid" DEFAULT "auth"."uid"(),
    "name" character varying(80) NOT NULL,
    "description" "text",
    "icon" character varying(24),
    "default_model" character varying(64) DEFAULT 'openai/gpt-4o'::character varying,
    "structured" "jsonb" DEFAULT '{}'::"jsonb",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "status" "public"."job_status" DEFAULT 'initiated'::"public"."job_status" NOT NULL,
    "is_favorite" boolean DEFAULT false,
    "default_model_id" "uuid"
);


ALTER TABLE "public"."workspaces" OWNER TO "postgres";


COMMENT ON COLUMN "public"."workspaces"."default_model_id" IS 'default model to use';



ALTER TABLE ONLY "public"."supabase_migrations" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."supabase_migrations_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."workspace_embeddings" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."workspace_embeddings_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."workspace_facts" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."workspace_facts_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."anonymous_sessions"
    ADD CONSTRAINT "anonymous_sessions_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."anonymous_sessions"
    ADD CONSTRAINT "anonymous_sessions_session_id_key" UNIQUE ("session_id");



ALTER TABLE ONLY "public"."conversations"
    ADD CONSTRAINT "conversations_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."group_conversations"
    ADD CONSTRAINT "group_conversations_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."image_credit_grants"
    ADD CONSTRAINT "image_credit_grants_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."images"
    ADD CONSTRAINT "images_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."llm_models"
    ADD CONSTRAINT "llm_models_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."llm_models"
    ADD CONSTRAINT "llm_models_provider_id_name_key" UNIQUE ("provider_id", "name");



ALTER TABLE ONLY "public"."llm_providers"
    ADD CONSTRAINT "llm_providers_name_key" UNIQUE ("name");



ALTER TABLE ONLY "public"."llm_providers"
    ADD CONSTRAINT "llm_providers_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."messages"
    ADD CONSTRAINT "messages_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."prompt_ratings"
    ADD CONSTRAINT "prompt_ratings_pkey" PRIMARY KEY ("user_id", "prompt_id");



ALTER TABLE ONLY "public"."prompt_templates"
    ADD CONSTRAINT "prompt_templates_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."prompt_usage"
    ADD CONSTRAINT "prompt_usage_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."prompt_versions"
    ADD CONSTRAINT "prompt_versions_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."prompts"
    ADD CONSTRAINT "prompts_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."rate_limits"
    ADD CONSTRAINT "rate_limits_key_key" UNIQUE ("key");



ALTER TABLE ONLY "public"."rate_limits"
    ADD CONSTRAINT "rate_limits_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."shared_conversations"
    ADD CONSTRAINT "shared_conversations_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."shared_messages"
    ADD CONSTRAINT "shared_messages_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."stripe_events"
    ADD CONSTRAINT "stripe_events_event_id_key" UNIQUE ("event_id");



ALTER TABLE ONLY "public"."stripe_events"
    ADD CONSTRAINT "stripe_events_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."subscription_plan_configs"
    ADD CONSTRAINT "subscription_plan_configs_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."subscription_plan_configs"
    ADD CONSTRAINT "subscription_plan_configs_plan_name_version_key" UNIQUE ("plan_name", "version");



ALTER TABLE ONLY "public"."subscriptions"
    ADD CONSTRAINT "subscriptions_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."supabase_migrations"
    ADD CONSTRAINT "supabase_migrations_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."token_reservations"
    ADD CONSTRAINT "token_reservations_pkey" PRIMARY KEY ("reservation_id");



ALTER TABLE ONLY "public"."uploaded_files"
    ADD CONSTRAINT "uploaded_files_file_hash_key" UNIQUE ("file_hash");



ALTER TABLE ONLY "public"."uploaded_files"
    ADD CONSTRAINT "uploaded_files_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."uploaded_files"
    ADD CONSTRAINT "uploaded_files_storage_path_key" UNIQUE ("storage_path");



ALTER TABLE ONLY "public"."usage_logs"
    ADD CONSTRAINT "usage_logs_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."user_balances"
    ADD CONSTRAINT "user_balances_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."user_balances"
    ADD CONSTRAINT "user_balances_user_id_key" UNIQUE ("user_id");



ALTER TABLE ONLY "public"."user_daily_usage"
    ADD CONSTRAINT "user_daily_usage_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."user_daily_usage"
    ADD CONSTRAINT "user_daily_usage_user_id_date_key" UNIQUE ("user_id", "date");



ALTER TABLE ONLY "public"."user_monthly_usage"
    ADD CONSTRAINT "user_monthly_usage_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."user_monthly_usage"
    ADD CONSTRAINT "user_monthly_usage_user_id_year_month_key" UNIQUE ("user_id", "year_month");



ALTER TABLE ONLY "public"."user_preferences"
    ADD CONSTRAINT "user_preferences_user_id_key" UNIQUE ("user_id");



ALTER TABLE ONLY "public"."user_preferences"
    ADD CONSTRAINT "user_settings_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."webhook_rate_limits"
    ADD CONSTRAINT "webhook_rate_limits_pkey" PRIMARY KEY ("rate_key");



ALTER TABLE ONLY "public"."workspace_embeddings"
    ADD CONSTRAINT "workspace_embeddings_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."workspace_facts"
    ADD CONSTRAINT "workspace_facts_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."workspace_facts"
    ADD CONSTRAINT "workspace_facts_workspace_id_key_value_key" UNIQUE ("workspace_id", "key", "value");



ALTER TABLE ONLY "public"."workspace_files"
    ADD CONSTRAINT "workspace_files_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."workspace_notes"
    ADD CONSTRAINT "workspace_notes_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."workspace_questions"
    ADD CONSTRAINT "workspace_questions_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."workspaces"
    ADD CONSTRAINT "workspaces_pkey" PRIMARY KEY ("id");



CREATE INDEX "facts_key_idx" ON "public"."workspace_facts" USING "gin" ("key" "public"."gin_trgm_ops");



CREATE INDEX "facts_value_idx" ON "public"."workspace_facts" USING "gin" ("value" "jsonb_path_ops");



CREATE INDEX "idx_conversations_last_model_id" ON "public"."conversations" USING "btree" ("last_model_used");



CREATE INDEX "idx_conversations_user_id" ON "public"."conversations" USING "btree" ("user_id");



CREATE INDEX "idx_group_conversations_user_favorite" ON "public"."group_conversations" USING "btree" ("user_id", "is_favorite", "updated_at");



CREATE INDEX "idx_image_credit_grants_user_expires" ON "public"."image_credit_grants" USING "btree" ("user_id", "expires_at") WHERE ("expires_at" IS NOT NULL);



CREATE INDEX "idx_llm_models_capabilities" ON "public"."llm_models" USING "gin" ("capabilities");



CREATE INDEX "idx_llm_models_provider_id" ON "public"."llm_models" USING "btree" ("provider_id");



CREATE INDEX "idx_llm_models_supported_parameters" ON "public"."llm_models" USING "gin" ("supported_parameters");



CREATE INDEX "idx_messages_conversation_id" ON "public"."messages" USING "btree" ("conversation_id");



CREATE INDEX "idx_messages_created_at" ON "public"."messages" USING "btree" ("created_at");



CREATE INDEX "idx_messages_parent_message_id" ON "public"."messages" USING "btree" ("parent_message_id");



CREATE INDEX "idx_rate_limits_key" ON "public"."rate_limits" USING "btree" ("key");



CREATE INDEX "idx_rate_limits_reset_time" ON "public"."rate_limits" USING "btree" ("reset_time");



CREATE INDEX "idx_shared_conversations_last_message_id" ON "public"."shared_conversations" USING "btree" ("last_original_message_id");



CREATE INDEX "idx_shared_conversations_original_convo_id" ON "public"."shared_conversations" USING "btree" ("original_conversation_id");



CREATE INDEX "idx_shared_conversations_user_id" ON "public"."shared_conversations" USING "btree" ("shared_by_user_id");



CREATE INDEX "idx_shared_messages_original_message_id" ON "public"."shared_messages" USING "btree" ("original_message_id");



CREATE INDEX "idx_shared_messages_parent_id" ON "public"."shared_messages" USING "btree" ("parent_shared_message_id");



CREATE INDEX "idx_shared_messages_shared_convo_id" ON "public"."shared_messages" USING "btree" ("shared_conversation_id");



CREATE INDEX "idx_stripe_events_created_at" ON "public"."stripe_events" USING "btree" ("created_at");



CREATE INDEX "idx_stripe_events_event_id" ON "public"."stripe_events" USING "btree" ("event_id");



CREATE INDEX "idx_subscription_plan_configs_active" ON "public"."subscription_plan_configs" USING "btree" ("plan_name", "is_active") WHERE ("is_active" = true);



CREATE INDEX "idx_subscriptions_period_end" ON "public"."subscriptions" USING "btree" ("current_period_end");



CREATE INDEX "idx_subscriptions_stripe_customer" ON "public"."subscriptions" USING "btree" ("stripe_customer_id");



COMMENT ON INDEX "public"."idx_subscriptions_stripe_customer" IS 'Critical for webhook processing performance';



CREATE INDEX "idx_subscriptions_user_status" ON "public"."subscriptions" USING "btree" ("user_id", "status");



CREATE INDEX "idx_token_reservations_expires" ON "public"."token_reservations" USING "btree" ("expires_at");



CREATE INDEX "idx_token_reservations_status" ON "public"."token_reservations" USING "btree" ("status");



CREATE INDEX "idx_token_reservations_user_status" ON "public"."token_reservations" USING "btree" ("user_id", "status");



CREATE INDEX "idx_uploaded_files_hash" ON "public"."uploaded_files" USING "btree" ("file_hash");



CREATE INDEX "idx_uploaded_files_uploader" ON "public"."uploaded_files" USING "btree" ("uploader_user_id");



CREATE INDEX "idx_usage_logs_user_id" ON "public"."usage_logs" USING "btree" ("user_id");



CREATE INDEX "idx_user_balances_credits_used" ON "public"."user_balances" USING "btree" ("credits_used_from_purchased") WHERE ("credits_used_from_purchased" > 0);



CREATE INDEX "idx_user_balances_tokens_used" ON "public"."user_balances" USING "btree" ("tokens_used_from_purchased") WHERE ("tokens_used_from_purchased" > 0);



CREATE INDEX "idx_user_balances_updated_at" ON "public"."user_balances" USING "btree" ("updated_at");



CREATE INDEX "idx_user_balances_user_id" ON "public"."user_balances" USING "btree" ("user_id");



COMMENT ON INDEX "public"."idx_user_balances_user_id" IS 'Critical for all balance queries - every quota check uses this';



CREATE INDEX "idx_user_daily_usage_date" ON "public"."user_daily_usage" USING "btree" ("date");



CREATE INDEX "idx_user_daily_usage_quota" ON "public"."user_daily_usage" USING "btree" ("user_id", "date", "tokens_used", "comparison_count");



CREATE INDEX "idx_user_daily_usage_user_date" ON "public"."user_daily_usage" USING "btree" ("user_id", "date");



COMMENT ON INDEX "public"."idx_user_daily_usage_user_date" IS 'Critical for daily quota checks on free plan';



CREATE INDEX "idx_user_monthly_usage_quota" ON "public"."user_monthly_usage" USING "btree" ("user_id", "year_month", "tokens_used", "image_credits_used");



CREATE INDEX "idx_user_monthly_usage_updated_at" ON "public"."user_monthly_usage" USING "btree" ("updated_at");



CREATE INDEX "idx_user_monthly_usage_user_month" ON "public"."user_monthly_usage" USING "btree" ("user_id", "year_month");



COMMENT ON INDEX "public"."idx_user_monthly_usage_user_month" IS 'Critical for monthly quota checks on paid plans';



CREATE INDEX "idx_webhook_rate_limits_expires" ON "public"."webhook_rate_limits" USING "btree" ("expires_at");



CREATE INDEX "we_ivfflat" ON "public"."workspace_embeddings" USING "ivfflat" ("embedding" "public"."vector_cosine_ops") WITH ("lists"='100');



CREATE OR REPLACE TRIGGER "embedario" AFTER INSERT ON "public"."workspace_files" FOR EACH ROW EXECUTE FUNCTION "supabase_functions"."http_request"('https://rpgzlnpnrnpgtegjweji.supabase.co/functions/v1/embed-file', 'POST', '{"Content-type":"application/json"}', '{}', '5000');



CREATE OR REPLACE TRIGGER "embedario-note" AFTER INSERT ON "public"."workspace_notes" FOR EACH ROW EXECUTE FUNCTION "supabase_functions"."http_request"('https://rpgzlnpnrnpgtegjweji.supabase.co/functions/v1/embed-note', 'POST', '{"Content-type":"application/json"}', '{}', '5000');



CREATE OR REPLACE TRIGGER "trigger_rate_limit_updated_at" BEFORE UPDATE ON "public"."rate_limits" FOR EACH ROW EXECUTE FUNCTION "public"."update_rate_limit_updated_at"();



CREATE OR REPLACE TRIGGER "trigger_user_balances_updated_at" BEFORE UPDATE ON "public"."user_balances" FOR EACH ROW EXECUTE FUNCTION "public"."update_user_balances_updated_at"();



ALTER TABLE ONLY "public"."conversations"
    ADD CONSTRAINT "conversations_anonymous_session_id_fkey" FOREIGN KEY ("anonymous_session_id") REFERENCES "public"."anonymous_sessions"("id");



ALTER TABLE ONLY "public"."conversations"
    ADD CONSTRAINT "conversations_group_conversation_id_fkey" FOREIGN KEY ("group_conversation_id") REFERENCES "public"."group_conversations"("id") ON UPDATE CASCADE ON DELETE CASCADE;



ALTER TABLE ONLY "public"."conversations"
    ADD CONSTRAINT "conversations_user_id_fkey1" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."conversations"
    ADD CONSTRAINT "fk_last_model_id" FOREIGN KEY ("last_model_used") REFERENCES "public"."llm_models"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."group_conversations"
    ADD CONSTRAINT "group_conversations_anonymous_session_id_fkey" FOREIGN KEY ("anonymous_session_id") REFERENCES "public"."anonymous_sessions"("id");



ALTER TABLE ONLY "public"."group_conversations"
    ADD CONSTRAINT "group_conversations_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."group_conversations"
    ADD CONSTRAINT "group_conversations_workspace_id_fkey" FOREIGN KEY ("workspace_id") REFERENCES "public"."workspaces"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."images"
    ADD CONSTRAINT "images_message_id_fkey" FOREIGN KEY ("message_id") REFERENCES "public"."messages"("id");



ALTER TABLE ONLY "public"."images"
    ADD CONSTRAINT "images_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."llm_models"
    ADD CONSTRAINT "llm_models_provider_id_fkey" FOREIGN KEY ("provider_id") REFERENCES "public"."llm_providers"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."messages"
    ADD CONSTRAINT "messages_conversation_id_fkey" FOREIGN KEY ("conversation_id") REFERENCES "public"."conversations"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."messages"
    ADD CONSTRAINT "messages_model_id_fkey" FOREIGN KEY ("model_id") REFERENCES "public"."llm_models"("id");



ALTER TABLE ONLY "public"."messages"
    ADD CONSTRAINT "messages_parent_message_id_fkey" FOREIGN KEY ("parent_message_id") REFERENCES "public"."messages"("id");



ALTER TABLE ONLY "public"."messages"
    ADD CONSTRAINT "messages_provider_id_fkey" FOREIGN KEY ("provider_id") REFERENCES "public"."llm_providers"("id");



ALTER TABLE ONLY "public"."prompt_ratings"
    ADD CONSTRAINT "prompt_ratings_prompt_id_fkey" FOREIGN KEY ("prompt_id") REFERENCES "public"."prompts"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."prompt_templates"
    ADD CONSTRAINT "prompt_templates_owner_id_fkey" FOREIGN KEY ("owner_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."prompt_templates"
    ADD CONSTRAINT "prompt_templates_workspace_id_fkey" FOREIGN KEY ("workspace_id") REFERENCES "public"."workspaces"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."prompt_usage"
    ADD CONSTRAINT "prompt_usage_prompt_id_fkey" FOREIGN KEY ("prompt_id") REFERENCES "public"."prompts"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."prompt_versions"
    ADD CONSTRAINT "prompt_versions_prompt_id_fkey" FOREIGN KEY ("prompt_id") REFERENCES "public"."prompts"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."prompts"
    ADD CONSTRAINT "prompts_workspace_id_fkey" FOREIGN KEY ("workspace_id") REFERENCES "public"."workspaces"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."shared_conversations"
    ADD CONSTRAINT "shared_conversations_last_original_message_id_fkey" FOREIGN KEY ("last_original_message_id") REFERENCES "public"."messages"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."shared_conversations"
    ADD CONSTRAINT "shared_conversations_original_conversation_id_fkey" FOREIGN KEY ("original_conversation_id") REFERENCES "public"."conversations"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."shared_conversations"
    ADD CONSTRAINT "shared_conversations_shared_by_user_id_fkey" FOREIGN KEY ("shared_by_user_id") REFERENCES "auth"."users"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."shared_messages"
    ADD CONSTRAINT "shared_messages_original_message_id_fkey" FOREIGN KEY ("original_message_id") REFERENCES "public"."messages"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."shared_messages"
    ADD CONSTRAINT "shared_messages_parent_shared_message_id_fkey" FOREIGN KEY ("parent_shared_message_id") REFERENCES "public"."shared_messages"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."shared_messages"
    ADD CONSTRAINT "shared_messages_shared_conversation_id_fkey" FOREIGN KEY ("shared_conversation_id") REFERENCES "public"."shared_conversations"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."subscriptions"
    ADD CONSTRAINT "subscriptions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."uploaded_files"
    ADD CONSTRAINT "uploaded_files_uploader_user_id_fkey" FOREIGN KEY ("uploader_user_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."usage_logs"
    ADD CONSTRAINT "usage_logs_conversation_id_fkey" FOREIGN KEY ("conversation_id") REFERENCES "public"."conversations"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."usage_logs"
    ADD CONSTRAINT "usage_logs_message_id_fkey" FOREIGN KEY ("message_id") REFERENCES "public"."messages"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."usage_logs"
    ADD CONSTRAINT "usage_logs_model_id_fkey" FOREIGN KEY ("model_id") REFERENCES "public"."llm_models"("id");



ALTER TABLE ONLY "public"."usage_logs"
    ADD CONSTRAINT "usage_logs_provider_id_fkey" FOREIGN KEY ("provider_id") REFERENCES "public"."llm_providers"("id");



ALTER TABLE ONLY "public"."usage_logs"
    ADD CONSTRAINT "usage_logs_user_id_fkey1" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."user_balances"
    ADD CONSTRAINT "user_balances_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."user_daily_usage"
    ADD CONSTRAINT "user_daily_usage_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."user_preferences"
    ADD CONSTRAINT "user_preferences_default_model_id_fkey" FOREIGN KEY ("default_model_id") REFERENCES "public"."llm_models"("id");



ALTER TABLE ONLY "public"."user_preferences"
    ADD CONSTRAINT "user_settings_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON UPDATE CASCADE ON DELETE CASCADE;



ALTER TABLE ONLY "public"."workspace_embeddings"
    ADD CONSTRAINT "workspace_embeddings_file_id_fkey" FOREIGN KEY ("file_id") REFERENCES "public"."workspace_files"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."workspace_embeddings"
    ADD CONSTRAINT "workspace_embeddings_workspace_id_fkey" FOREIGN KEY ("workspace_id") REFERENCES "public"."workspaces"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."workspace_facts"
    ADD CONSTRAINT "workspace_facts_file_id_fkey" FOREIGN KEY ("file_id") REFERENCES "public"."workspace_files"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."workspace_facts"
    ADD CONSTRAINT "workspace_facts_workspace_id_fkey" FOREIGN KEY ("workspace_id") REFERENCES "public"."workspaces"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."workspace_files"
    ADD CONSTRAINT "workspace_files_workspace_id_fkey" FOREIGN KEY ("workspace_id") REFERENCES "public"."workspaces"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."workspace_notes"
    ADD CONSTRAINT "workspace_notes_workspace_id_fkey" FOREIGN KEY ("workspace_id") REFERENCES "public"."workspaces"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."workspace_questions"
    ADD CONSTRAINT "workspace_questions_workspace_id_fkey" FOREIGN KEY ("workspace_id") REFERENCES "public"."workspaces"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."workspaces"
    ADD CONSTRAINT "workspaces_default_model_id_fkey" FOREIGN KEY ("default_model_id") REFERENCES "public"."llm_models"("id");



ALTER TABLE ONLY "public"."workspaces"
    ADD CONSTRAINT "workspaces_owner_id_fkey" FOREIGN KEY ("owner_id") REFERENCES "auth"."users"("id");



CREATE POLICY "Admins can view all usage logs" ON "public"."usage_logs" FOR SELECT TO "authenticated" USING ((("auth"."jwt"() ->> 'role'::"text") = 'admin'::"text"));



CREATE POLICY "Allow authenticated insert access" ON "public"."uploaded_files" FOR INSERT WITH CHECK (("auth"."role"() = 'authenticated'::"text"));



CREATE POLICY "Allow authenticated read access" ON "public"."uploaded_files" FOR SELECT USING (("auth"."role"() = 'authenticated'::"text"));



CREATE POLICY "Allow insert for server-side" ON "public"."stripe_events" FOR INSERT WITH CHECK (true);



CREATE POLICY "Allow select for server-side" ON "public"."stripe_events" FOR SELECT USING (true);



CREATE POLICY "Anonymous users can manage messages in their session conversati" ON "public"."messages" USING (("conversation_id" IN ( SELECT "conversations"."id"
   FROM "public"."conversations"
  WHERE ("conversations"."anonymous_session_id" IN ( SELECT "anonymous_sessions"."id"
           FROM "public"."anonymous_sessions"
          WHERE ("anonymous_sessions"."session_id" = (("current_setting"('request.headers'::"text"))::"json" ->> 'x-session-id'::"text"))))))) WITH CHECK (("conversation_id" IN ( SELECT "conversations"."id"
   FROM "public"."conversations"
  WHERE ("conversations"."anonymous_session_id" IN ( SELECT "anonymous_sessions"."id"
           FROM "public"."anonymous_sessions"
          WHERE ("anonymous_sessions"."session_id" = (("current_setting"('request.headers'::"text"))::"json" ->> 'x-session-id'::"text")))))));



CREATE POLICY "Anonymous users can manage their session conversations" ON "public"."conversations" USING (("anonymous_session_id" IN ( SELECT "anonymous_sessions"."id"
   FROM "public"."anonymous_sessions"
  WHERE ("anonymous_sessions"."session_id" = (("current_setting"('request.headers'::"text"))::"json" ->> 'x-session-id'::"text"))))) WITH CHECK (("anonymous_session_id" IN ( SELECT "anonymous_sessions"."id"
   FROM "public"."anonymous_sessions"
  WHERE ("anonymous_sessions"."session_id" = (("current_setting"('request.headers'::"text"))::"json" ->> 'x-session-id'::"text")))));



CREATE POLICY "Anonymous users can view usage logs for their session conversat" ON "public"."usage_logs" FOR SELECT USING (("conversation_id" IN ( SELECT "conversations"."id"
   FROM "public"."conversations"
  WHERE ("conversations"."anonymous_session_id" IN ( SELECT "anonymous_sessions"."id"
           FROM "public"."anonymous_sessions"
          WHERE ("anonymous_sessions"."session_id" = (("current_setting"('request.headers'::"text"))::"json" ->> 'x-session-id'::"text")))))));



CREATE POLICY "Anyone can create anonymous sessions" ON "public"."anonymous_sessions" FOR INSERT WITH CHECK (true);



CREATE POLICY "Anyone can view active LLM models" ON "public"."llm_models" FOR SELECT USING (("is_active" = true));



CREATE POLICY "Anyone can view active LLM providers" ON "public"."llm_providers" FOR SELECT USING (("is_active" = true));



CREATE POLICY "Authenticated users can manage messages in their conversations" ON "public"."messages" TO "authenticated" USING (("conversation_id" IN ( SELECT "conversations"."id"
   FROM "public"."conversations"
  WHERE ("conversations"."user_id" = "auth"."uid"())))) WITH CHECK (("conversation_id" IN ( SELECT "conversations"."id"
   FROM "public"."conversations"
  WHERE ("conversations"."user_id" = "auth"."uid"()))));



CREATE POLICY "Authenticated users can manage their own conversations" ON "public"."conversations" TO "authenticated" USING (("user_id" = "auth"."uid"())) WITH CHECK (("user_id" = "auth"."uid"()));



CREATE POLICY "Authenticated users can manage their own group_conversations" ON "public"."group_conversations" USING (("user_id" = "auth"."uid"())) WITH CHECK (("user_id" = "auth"."uid"()));



CREATE POLICY "Authenticated users can manage their own subscriptions" ON "public"."subscriptions" USING (("user_id" = "auth"."uid"())) WITH CHECK (("user_id" = "auth"."uid"()));



CREATE POLICY "Authenticated users can manage their own user_daily_usage" ON "public"."user_daily_usage" USING (("user_id" = "auth"."uid"())) WITH CHECK (("user_id" = "auth"."uid"()));



CREATE POLICY "Authenticated users can manage their own user_settings" ON "public"."user_preferences" TO "authenticated" USING (("user_id" = "auth"."uid"())) WITH CHECK (("user_id" = "auth"."uid"()));



CREATE POLICY "Authenticated users can view their own usage logs" ON "public"."usage_logs" FOR SELECT TO "authenticated" USING (("user_id" = "auth"."uid"()));



CREATE POLICY "Users can access embeddings from their own workspaces" ON "public"."workspace_embeddings" FOR SELECT USING ((EXISTS ( SELECT 1
   FROM "public"."workspaces"
  WHERE (("workspaces"."id" = "workspace_embeddings"."workspace_id") AND ("workspaces"."owner_id" = "auth"."uid"())))));



CREATE POLICY "Users can access files from their own workspaces" ON "public"."workspace_files" USING ((EXISTS ( SELECT 1
   FROM "public"."workspaces"
  WHERE (("workspaces"."id" = "workspace_files"."workspace_id") AND ("workspaces"."owner_id" = "auth"."uid"()))))) WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."workspaces"
  WHERE (("workspaces"."id" = "workspace_files"."workspace_id") AND ("workspaces"."owner_id" = "auth"."uid"())))));



CREATE POLICY "Users can access messages in their conversations" ON "public"."messages" USING (((( SELECT "conversations"."user_id"
   FROM "public"."conversations"
  WHERE ("conversations"."id" = "messages"."conversation_id")) = "auth"."uid"()) OR (( SELECT "conversations"."anonymous_session_id"
   FROM "public"."conversations"
  WHERE ("conversations"."id" = "messages"."conversation_id")) IN ( SELECT "anonymous_sessions"."id"
   FROM "public"."anonymous_sessions"
  WHERE ("anonymous_sessions"."session_id" = "current_setting"('app.anonymous_session_id'::"text", true))))));



CREATE POLICY "Users can access their own balances" ON "public"."user_balances" USING (("user_id" = "auth"."uid"())) WITH CHECK (("user_id" = "auth"."uid"()));



CREATE POLICY "Users can access their own images" ON "public"."images" USING (("auth"."uid"() = "user_id")) WITH CHECK (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can access their own prompt versions" ON "public"."prompt_versions" USING ((EXISTS ( SELECT 1
   FROM "public"."prompts"
  WHERE (("prompts"."id" = "prompt_versions"."prompt_id") AND ("prompts"."owner_id" = "auth"."uid"()))))) WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."prompts"
  WHERE (("prompts"."id" = "prompt_versions"."prompt_id") AND ("prompts"."owner_id" = "auth"."uid"())))));



CREATE POLICY "Users can access their own prompt_templates" ON "public"."prompt_templates" USING (("auth"."uid"() = "owner_id")) WITH CHECK (("auth"."uid"() = "owner_id"));



CREATE POLICY "Users can access their own prompts" ON "public"."prompts" USING (("auth"."uid"() = "owner_id")) WITH CHECK (("auth"."uid"() = "owner_id"));



CREATE POLICY "Users can access their own workspace facts" ON "public"."workspace_facts" USING ((EXISTS ( SELECT 1
   FROM "public"."workspaces"
  WHERE (("workspaces"."id" = "workspace_facts"."workspace_id") AND ("workspaces"."owner_id" = "auth"."uid"()))))) WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."workspaces"
  WHERE (("workspaces"."id" = "workspace_facts"."workspace_id") AND ("workspaces"."owner_id" = "auth"."uid"())))));



CREATE POLICY "Users can access their own workspace notes" ON "public"."workspace_notes" USING ((EXISTS ( SELECT 1
   FROM "public"."workspaces"
  WHERE (("workspaces"."id" = "workspace_notes"."workspace_id") AND ("workspaces"."owner_id" = "auth"."uid"()))))) WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."workspaces"
  WHERE (("workspaces"."id" = "workspace_notes"."workspace_id") AND ("workspaces"."owner_id" = "auth"."uid"())))));



CREATE POLICY "Users can access their own workspaces" ON "public"."workspaces" USING (("auth"."uid"() = "owner_id")) WITH CHECK (("auth"."uid"() = "owner_id"));



CREATE POLICY "Users can create usage logs for their own actions" ON "public"."usage_logs" FOR INSERT WITH CHECK (((("auth"."uid"() IS NOT NULL) AND ("user_id" = "auth"."uid"())) OR ("conversation_id" IN ( SELECT "conversations"."id"
   FROM "public"."conversations"
  WHERE ("conversations"."anonymous_session_id" IN ( SELECT "anonymous_sessions"."id"
           FROM "public"."anonymous_sessions"
          WHERE ("anonymous_sessions"."session_id" = (("current_setting"('request.headers'::"text"))::"json" ->> 'x-session-id'::"text"))))))));



CREATE POLICY "Users can manage questions for their own workspaces" ON "public"."workspace_questions" USING ((EXISTS ( SELECT 1
   FROM "public"."workspaces"
  WHERE (("workspaces"."id" = "workspace_questions"."workspace_id") AND ("workspaces"."owner_id" = "auth"."uid"()))))) WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."workspaces"
  WHERE (("workspaces"."id" = "workspace_questions"."workspace_id") AND ("workspaces"."owner_id" = "auth"."uid"())))));



CREATE POLICY "Users can read their own anonymous sessions" ON "public"."anonymous_sessions" FOR SELECT USING (("session_id" = (("current_setting"('request.headers'::"text"))::"json" ->> 'x-session-id'::"text")));



CREATE POLICY "Users can update their own anonymous sessions" ON "public"."anonymous_sessions" FOR UPDATE USING (("session_id" = (("current_setting"('request.headers'::"text"))::"json" ->> 'x-session-id'::"text"))) WITH CHECK (("session_id" = (("current_setting"('request.headers'::"text"))::"json" ->> 'x-session-id'::"text")));



ALTER TABLE "public"."anonymous_sessions" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."conversations" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."group_conversations" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."images" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."llm_models" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."llm_providers" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."messages" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."prompt_templates" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."prompt_versions" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."prompts" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."stripe_events" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."subscriptions" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."token_reservations" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."uploaded_files" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."usage_logs" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."user_balances" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."user_daily_usage" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."user_preferences" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."webhook_rate_limits" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."workspace_embeddings" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."workspace_facts" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."workspace_files" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."workspace_notes" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."workspace_questions" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."workspaces" ENABLE ROW LEVEL SECURITY;


GRANT USAGE ON SCHEMA "public" TO "postgres";
GRANT USAGE ON SCHEMA "public" TO "anon";
GRANT USAGE ON SCHEMA "public" TO "authenticated";
GRANT USAGE ON SCHEMA "public" TO "service_role";



GRANT ALL ON FUNCTION "public"."add_persistent_credit_balance"("p_user_id" "uuid", "p_credits" integer) TO "service_role";



GRANT ALL ON FUNCTION "public"."add_persistent_token_balance"("p_user_id" "uuid", "p_tokens" bigint) TO "service_role";



GRANT ALL ON FUNCTION "public"."atomic_comparison_check_and_increment"("p_user_id" "uuid", "p_date" "date", "p_daily_limit" integer) TO "anon";
GRANT ALL ON FUNCTION "public"."atomic_comparison_check_and_increment"("p_user_id" "uuid", "p_date" "date", "p_daily_limit" integer) TO "authenticated";
GRANT ALL ON FUNCTION "public"."atomic_comparison_check_and_increment"("p_user_id" "uuid", "p_date" "date", "p_daily_limit" integer) TO "service_role";



GRANT ALL ON FUNCTION "public"."atomic_comparison_check_and_increment_safe"("p_user_id" "uuid", "p_date" "text", "p_daily_limit" integer) TO "service_role";



GRANT ALL ON FUNCTION "public"."atomic_image_check_and_increment"("p_user_id" "uuid", "p_credits" integer, "p_year_month" "text", "p_quota_limit" integer) TO "service_role";



GRANT ALL ON FUNCTION "public"."atomic_image_check_and_increment_safe"("p_user_id" "uuid", "p_credits" integer, "p_year_month" "text", "p_quota_limit" integer) TO "service_role";



GRANT ALL ON FUNCTION "public"."atomic_token_check_and_increment"("p_user_id" "uuid", "p_tokens" bigint, "p_year_month" "text", "p_quota_limit" bigint) TO "service_role";



GRANT ALL ON FUNCTION "public"."atomic_token_check_and_increment_safe"("p_user_id" "uuid", "p_tokens" bigint, "p_year_month" "text", "p_quota_limit" bigint) TO "service_role";



GRANT ALL ON FUNCTION "public"."check_and_increment_webhook_rate_limit"("p_rate_key" "text", "p_max_requests" integer, "p_window_seconds" integer) TO "anon";
GRANT ALL ON FUNCTION "public"."check_and_increment_webhook_rate_limit"("p_rate_key" "text", "p_max_requests" integer, "p_window_seconds" integer) TO "authenticated";
GRANT ALL ON FUNCTION "public"."check_and_increment_webhook_rate_limit"("p_rate_key" "text", "p_max_requests" integer, "p_window_seconds" integer) TO "service_role";



GRANT ALL ON FUNCTION "public"."cleanup_expired_rate_limits"() TO "anon";
GRANT ALL ON FUNCTION "public"."cleanup_expired_rate_limits"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."cleanup_expired_rate_limits"() TO "service_role";



GRANT ALL ON FUNCTION "public"."cleanup_expired_token_reservations"() TO "anon";
GRANT ALL ON FUNCTION "public"."cleanup_expired_token_reservations"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."cleanup_expired_token_reservations"() TO "service_role";



GRANT ALL ON FUNCTION "public"."cleanup_expired_webhook_rate_limits"() TO "anon";
GRANT ALL ON FUNCTION "public"."cleanup_expired_webhook_rate_limits"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."cleanup_expired_webhook_rate_limits"() TO "service_role";



GRANT ALL ON FUNCTION "public"."cleanup_monthly_purchase_tracking"() TO "service_role";



GRANT ALL ON FUNCTION "public"."commit_token_reservation"("p_reservation_id" "text", "p_actual_tokens" bigint) TO "anon";
GRANT ALL ON FUNCTION "public"."commit_token_reservation"("p_reservation_id" "text", "p_actual_tokens" bigint) TO "authenticated";
GRANT ALL ON FUNCTION "public"."commit_token_reservation"("p_reservation_id" "text", "p_actual_tokens" bigint) TO "service_role";



GRANT ALL ON FUNCTION "public"."create_token_reservation"("p_user_id" "uuid", "p_tokens" bigint, "p_model_id" "text", "p_reservation_ttl_minutes" integer) TO "anon";
GRANT ALL ON FUNCTION "public"."create_token_reservation"("p_user_id" "uuid", "p_tokens" bigint, "p_model_id" "text", "p_reservation_ttl_minutes" integer) TO "authenticated";
GRANT ALL ON FUNCTION "public"."create_token_reservation"("p_user_id" "uuid", "p_tokens" bigint, "p_model_id" "text", "p_reservation_ttl_minutes" integer) TO "service_role";



GRANT ALL ON FUNCTION "public"."decrement_daily_token_usage"("p_user_id" "text", "p_date" "text", "p_tokens" bigint) TO "anon";
GRANT ALL ON FUNCTION "public"."decrement_daily_token_usage"("p_user_id" "text", "p_date" "text", "p_tokens" bigint) TO "authenticated";
GRANT ALL ON FUNCTION "public"."decrement_daily_token_usage"("p_user_id" "text", "p_date" "text", "p_tokens" bigint) TO "service_role";



GRANT ALL ON FUNCTION "public"."decrement_daily_token_usage"("p_user_id" "uuid", "p_date" "date", "p_tokens" bigint) TO "anon";
GRANT ALL ON FUNCTION "public"."decrement_daily_token_usage"("p_user_id" "uuid", "p_date" "date", "p_tokens" bigint) TO "authenticated";
GRANT ALL ON FUNCTION "public"."decrement_daily_token_usage"("p_user_id" "uuid", "p_date" "date", "p_tokens" bigint) TO "service_role";



GRANT ALL ON FUNCTION "public"."decrement_monthly_image_usage"("p_user_id" "uuid", "p_year_month" "text", "p_credits" integer) TO "service_role";



GRANT ALL ON FUNCTION "public"."decrement_monthly_token_usage"("p_user_id" "uuid", "p_year_month" "text", "p_tokens" bigint) TO "anon";
GRANT ALL ON FUNCTION "public"."decrement_monthly_token_usage"("p_user_id" "uuid", "p_year_month" "text", "p_tokens" bigint) TO "authenticated";
GRANT ALL ON FUNCTION "public"."decrement_monthly_token_usage"("p_user_id" "uuid", "p_year_month" "text", "p_tokens" bigint) TO "service_role";



GRANT ALL ON FUNCTION "public"."decrement_user_comparison_count"("p_user_id" "text", "p_date" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."decrement_user_image_count"("p_user_id" "text", "p_date" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_reservation_stats"() TO "anon";
GRANT ALL ON FUNCTION "public"."get_reservation_stats"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_reservation_stats"() TO "service_role";



GRANT ALL ON FUNCTION "public"."get_user_persistent_balance"("p_user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."increment_comparison_count"("p_user_id" "uuid", "p_year_month" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."increment_daily_token_usage"("p_user_id" "text", "p_date" "text", "p_tokens" integer) TO "service_role";



GRANT ALL ON FUNCTION "public"."increment_daily_token_usage"("p_user_id" "uuid", "p_date" "date", "p_tokens" integer) TO "anon";
GRANT ALL ON FUNCTION "public"."increment_daily_token_usage"("p_user_id" "uuid", "p_date" "date", "p_tokens" integer) TO "authenticated";
GRANT ALL ON FUNCTION "public"."increment_daily_token_usage"("p_user_id" "uuid", "p_date" "date", "p_tokens" integer) TO "service_role";



GRANT ALL ON FUNCTION "public"."increment_daily_token_usage_with_limit"("p_user_id" "text", "p_date" "text", "p_tokens" integer, "p_daily_limit" integer) TO "anon";
GRANT ALL ON FUNCTION "public"."increment_daily_token_usage_with_limit"("p_user_id" "text", "p_date" "text", "p_tokens" integer, "p_daily_limit" integer) TO "authenticated";
GRANT ALL ON FUNCTION "public"."increment_daily_token_usage_with_limit"("p_user_id" "text", "p_date" "text", "p_tokens" integer, "p_daily_limit" integer) TO "service_role";



GRANT ALL ON FUNCTION "public"."increment_daily_token_usage_with_limit"("p_user_id" "uuid", "p_date" "date", "p_tokens" integer, "p_daily_limit" integer) TO "service_role";



GRANT ALL ON FUNCTION "public"."increment_monthly_token_usage"("p_user_id" "uuid", "p_year_month" "text", "p_tokens" bigint) TO "service_role";



GRANT ALL ON FUNCTION "public"."increment_user_comparison_count"("p_user_id" "uuid", "p_date" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."increment_user_comparison_count"("p_user_id" "uuid", "p_date" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."increment_user_comparison_count"("p_user_id" "uuid", "p_date" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."increment_user_image_count"("p_user_id" "text", "p_date" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."increment_user_image_count"("p_user_id" "text", "p_date" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."increment_user_image_count"("p_user_id" "text", "p_date" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."increment_user_message_count"("p_user_id" "text", "p_date" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."increment_user_message_count"("p_user_id" "text", "p_date" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."increment_user_message_count"("p_user_id" "text", "p_date" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."is_valid_session"("session_id" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."is_valid_session"("session_id" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."is_valid_session"("session_id" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."match_chunks"("p_workspace" "uuid", "p_query" "public"."vector", "p_topk" integer, "p_min_sim" double precision) TO "anon";
GRANT ALL ON FUNCTION "public"."match_chunks"("p_workspace" "uuid", "p_query" "public"."vector", "p_topk" integer, "p_min_sim" double precision) TO "authenticated";
GRANT ALL ON FUNCTION "public"."match_chunks"("p_workspace" "uuid", "p_query" "public"."vector", "p_topk" integer, "p_min_sim" double precision) TO "service_role";



GRANT ALL ON FUNCTION "public"."match_facts"("p_workspace" "uuid", "p_search" "text", "p_topk" integer) TO "anon";
GRANT ALL ON FUNCTION "public"."match_facts"("p_workspace" "uuid", "p_search" "text", "p_topk" integer) TO "authenticated";
GRANT ALL ON FUNCTION "public"."match_facts"("p_workspace" "uuid", "p_search" "text", "p_topk" integer) TO "service_role";



GRANT ALL ON FUNCTION "public"."match_notes"("p_workspace" "uuid", "p_query" "public"."vector", "p_topk" integer) TO "anon";
GRANT ALL ON FUNCTION "public"."match_notes"("p_workspace" "uuid", "p_query" "public"."vector", "p_topk" integer) TO "authenticated";
GRANT ALL ON FUNCTION "public"."match_notes"("p_workspace" "uuid", "p_query" "public"."vector", "p_topk" integer) TO "service_role";



GRANT ALL ON FUNCTION "public"."process_stripe_webhook"("p_event_id" "text", "p_event_type" "text", "p_event_data" "jsonb") TO "anon";
GRANT ALL ON FUNCTION "public"."process_stripe_webhook"("p_event_id" "text", "p_event_type" "text", "p_event_data" "jsonb") TO "authenticated";
GRANT ALL ON FUNCTION "public"."process_stripe_webhook"("p_event_id" "text", "p_event_type" "text", "p_event_data" "jsonb") TO "service_role";



GRANT ALL ON FUNCTION "public"."refund_persistent_token_balance"("p_user_id" "uuid", "p_tokens" bigint) TO "anon";
GRANT ALL ON FUNCTION "public"."refund_persistent_token_balance"("p_user_id" "uuid", "p_tokens" bigint) TO "authenticated";
GRANT ALL ON FUNCTION "public"."refund_persistent_token_balance"("p_user_id" "uuid", "p_tokens" bigint) TO "service_role";



GRANT ALL ON FUNCTION "public"."refund_token_reservation"("p_reservation_id" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."refund_token_reservation"("p_reservation_id" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."refund_token_reservation"("p_reservation_id" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."update_rate_limit_updated_at"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_rate_limit_updated_at"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_rate_limit_updated_at"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_user_balances_updated_at"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_user_balances_updated_at"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_user_balances_updated_at"() TO "service_role";



GRANT ALL ON FUNCTION "public"."use_image_credit_grant"("p_user_id" "uuid", "p_credits" integer) TO "service_role";



GRANT ALL ON FUNCTION "public"."use_persistent_credit_balance"("p_user_id" "uuid", "p_credits" integer) TO "service_role";



GRANT ALL ON FUNCTION "public"."use_persistent_token_balance"("p_user_id" "uuid", "p_tokens" bigint) TO "service_role";



GRANT ALL ON TABLE "public"."anonymous_sessions" TO "anon";
GRANT ALL ON TABLE "public"."anonymous_sessions" TO "authenticated";
GRANT ALL ON TABLE "public"."anonymous_sessions" TO "service_role";



GRANT ALL ON TABLE "public"."conversations" TO "anon";
GRANT ALL ON TABLE "public"."conversations" TO "authenticated";
GRANT ALL ON TABLE "public"."conversations" TO "service_role";



GRANT ALL ON TABLE "public"."group_conversations" TO "anon";
GRANT ALL ON TABLE "public"."group_conversations" TO "authenticated";
GRANT ALL ON TABLE "public"."group_conversations" TO "service_role";



GRANT ALL ON TABLE "public"."image_credit_grants" TO "anon";
GRANT ALL ON TABLE "public"."image_credit_grants" TO "authenticated";
GRANT ALL ON TABLE "public"."image_credit_grants" TO "service_role";



GRANT ALL ON TABLE "public"."images" TO "anon";
GRANT ALL ON TABLE "public"."images" TO "authenticated";
GRANT ALL ON TABLE "public"."images" TO "service_role";



GRANT ALL ON TABLE "public"."llm_models" TO "anon";
GRANT ALL ON TABLE "public"."llm_models" TO "authenticated";
GRANT ALL ON TABLE "public"."llm_models" TO "service_role";



GRANT ALL ON TABLE "public"."llm_providers" TO "anon";
GRANT ALL ON TABLE "public"."llm_providers" TO "authenticated";
GRANT ALL ON TABLE "public"."llm_providers" TO "service_role";



GRANT ALL ON TABLE "public"."messages" TO "anon";
GRANT ALL ON TABLE "public"."messages" TO "authenticated";
GRANT ALL ON TABLE "public"."messages" TO "service_role";



GRANT ALL ON TABLE "public"."prompt_ratings" TO "anon";
GRANT ALL ON TABLE "public"."prompt_ratings" TO "authenticated";
GRANT ALL ON TABLE "public"."prompt_ratings" TO "service_role";



GRANT ALL ON TABLE "public"."prompt_templates" TO "anon";
GRANT ALL ON TABLE "public"."prompt_templates" TO "authenticated";
GRANT ALL ON TABLE "public"."prompt_templates" TO "service_role";



GRANT ALL ON TABLE "public"."prompt_usage" TO "anon";
GRANT ALL ON TABLE "public"."prompt_usage" TO "authenticated";
GRANT ALL ON TABLE "public"."prompt_usage" TO "service_role";



GRANT ALL ON TABLE "public"."prompt_versions" TO "anon";
GRANT ALL ON TABLE "public"."prompt_versions" TO "authenticated";
GRANT ALL ON TABLE "public"."prompt_versions" TO "service_role";



GRANT ALL ON TABLE "public"."prompts" TO "anon";
GRANT ALL ON TABLE "public"."prompts" TO "authenticated";
GRANT ALL ON TABLE "public"."prompts" TO "service_role";



GRANT ALL ON TABLE "public"."rate_limits" TO "anon";
GRANT ALL ON TABLE "public"."rate_limits" TO "authenticated";
GRANT ALL ON TABLE "public"."rate_limits" TO "service_role";



GRANT ALL ON TABLE "public"."shared_conversations" TO "anon";
GRANT ALL ON TABLE "public"."shared_conversations" TO "authenticated";
GRANT ALL ON TABLE "public"."shared_conversations" TO "service_role";



GRANT ALL ON TABLE "public"."shared_messages" TO "anon";
GRANT ALL ON TABLE "public"."shared_messages" TO "authenticated";
GRANT ALL ON TABLE "public"."shared_messages" TO "service_role";



GRANT ALL ON TABLE "public"."stripe_events" TO "anon";
GRANT ALL ON TABLE "public"."stripe_events" TO "authenticated";
GRANT ALL ON TABLE "public"."stripe_events" TO "service_role";



GRANT ALL ON TABLE "public"."subscription_plan_configs" TO "anon";
GRANT ALL ON TABLE "public"."subscription_plan_configs" TO "authenticated";
GRANT ALL ON TABLE "public"."subscription_plan_configs" TO "service_role";



GRANT ALL ON TABLE "public"."subscriptions" TO "anon";
GRANT ALL ON TABLE "public"."subscriptions" TO "authenticated";
GRANT ALL ON TABLE "public"."subscriptions" TO "service_role";



GRANT ALL ON TABLE "public"."supabase_migrations" TO "anon";
GRANT ALL ON TABLE "public"."supabase_migrations" TO "authenticated";
GRANT ALL ON TABLE "public"."supabase_migrations" TO "service_role";



GRANT ALL ON SEQUENCE "public"."supabase_migrations_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."supabase_migrations_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."supabase_migrations_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."token_reservations" TO "anon";
GRANT ALL ON TABLE "public"."token_reservations" TO "authenticated";
GRANT ALL ON TABLE "public"."token_reservations" TO "service_role";



GRANT ALL ON TABLE "public"."uploaded_files" TO "anon";
GRANT ALL ON TABLE "public"."uploaded_files" TO "authenticated";
GRANT ALL ON TABLE "public"."uploaded_files" TO "service_role";



GRANT ALL ON TABLE "public"."usage_logs" TO "anon";
GRANT ALL ON TABLE "public"."usage_logs" TO "authenticated";
GRANT ALL ON TABLE "public"."usage_logs" TO "service_role";



GRANT ALL ON TABLE "public"."user_balances" TO "anon";
GRANT ALL ON TABLE "public"."user_balances" TO "authenticated";
GRANT ALL ON TABLE "public"."user_balances" TO "service_role";



GRANT ALL ON TABLE "public"."user_daily_usage" TO "anon";
GRANT ALL ON TABLE "public"."user_daily_usage" TO "authenticated";
GRANT ALL ON TABLE "public"."user_daily_usage" TO "service_role";



GRANT ALL ON TABLE "public"."user_monthly_usage" TO "anon";
GRANT ALL ON TABLE "public"."user_monthly_usage" TO "authenticated";
GRANT ALL ON TABLE "public"."user_monthly_usage" TO "service_role";



GRANT ALL ON TABLE "public"."user_preferences" TO "anon";
GRANT ALL ON TABLE "public"."user_preferences" TO "authenticated";
GRANT ALL ON TABLE "public"."user_preferences" TO "service_role";



GRANT ALL ON SEQUENCE "public"."user_settings_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."user_settings_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."user_settings_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."webhook_rate_limits" TO "anon";
GRANT ALL ON TABLE "public"."webhook_rate_limits" TO "authenticated";
GRANT ALL ON TABLE "public"."webhook_rate_limits" TO "service_role";



GRANT ALL ON TABLE "public"."workspace_embeddings" TO "anon";
GRANT ALL ON TABLE "public"."workspace_embeddings" TO "authenticated";
GRANT ALL ON TABLE "public"."workspace_embeddings" TO "service_role";



GRANT ALL ON SEQUENCE "public"."workspace_embeddings_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."workspace_embeddings_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."workspace_embeddings_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."workspace_facts" TO "anon";
GRANT ALL ON TABLE "public"."workspace_facts" TO "authenticated";
GRANT ALL ON TABLE "public"."workspace_facts" TO "service_role";



GRANT ALL ON SEQUENCE "public"."workspace_facts_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."workspace_facts_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."workspace_facts_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."workspace_files" TO "anon";
GRANT ALL ON TABLE "public"."workspace_files" TO "authenticated";
GRANT ALL ON TABLE "public"."workspace_files" TO "service_role";



GRANT ALL ON TABLE "public"."workspace_notes" TO "anon";
GRANT ALL ON TABLE "public"."workspace_notes" TO "authenticated";
GRANT ALL ON TABLE "public"."workspace_notes" TO "service_role";



GRANT ALL ON TABLE "public"."workspace_questions" TO "anon";
GRANT ALL ON TABLE "public"."workspace_questions" TO "authenticated";
GRANT ALL ON TABLE "public"."workspace_questions" TO "service_role";



GRANT ALL ON TABLE "public"."workspaces" TO "anon";
GRANT ALL ON TABLE "public"."workspaces" TO "authenticated";
GRANT ALL ON TABLE "public"."workspaces" TO "service_role";



ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "service_role";






RESET ALL;
