-- Create feature flags system
-- This migration creates the feature_flags and feature_flag_user_overrides tables

-- Create feature_flags table
CREATE TABLE IF NOT EXISTS "public"."feature_flags" (
    "id" UUID DEFAULT gen_random_uuid() NOT NULL,
    "name" TEXT NOT NULL,
    "display_name" TEXT NOT NULL,
    "description" TEXT,
    "is_enabled" BOOLEAN DEFAULT false NOT NULL,
    "rollout_percentage" INTEGER DEFAULT 0 NOT NULL,
    "created_at" TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    "created_by" UUID NOT NULL,
    CONSTRAINT "feature_flags_pkey" PRIMARY KEY ("id"),
    CONSTRAINT "feature_flags_name_unique" UNIQUE ("name"),
    CONSTRAINT "feature_flags_rollout_percentage_check" CHECK (("rollout_percentage" >= 0 AND "rollout_percentage" <= 100)),
    CONSTRAINT "feature_flags_name_format_check" CHECK ("name" ~ '^[a-z0-9-]+$'),
    CONSTRAINT "feature_flags_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "auth"."users"("id") ON DELETE CASCADE
);

-- Create feature_flag_user_overrides table
CREATE TABLE IF NOT EXISTS "public"."feature_flag_user_overrides" (
    "id" UUID DEFAULT gen_random_uuid() NOT NULL,
    "feature_flag_id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "is_enabled" BOOLEAN NOT NULL,
    "created_at" TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    CONSTRAINT "feature_flag_user_overrides_pkey" PRIMARY KEY ("id"),
    CONSTRAINT "feature_flag_user_overrides_unique" UNIQUE ("feature_flag_id", "user_id"),
    CONSTRAINT "feature_flag_user_overrides_feature_flag_id_fkey" FOREIGN KEY ("feature_flag_id") REFERENCES "public"."feature_flags"("id") ON DELETE CASCADE,
    CONSTRAINT "feature_flag_user_overrides_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS "idx_feature_flags_name" ON "public"."feature_flags" ("name");
CREATE INDEX IF NOT EXISTS "idx_feature_flags_is_enabled" ON "public"."feature_flags" ("is_enabled");
CREATE INDEX IF NOT EXISTS "idx_feature_flags_created_by" ON "public"."feature_flags" ("created_by");
CREATE INDEX IF NOT EXISTS "idx_feature_flag_user_overrides_feature_flag_id" ON "public"."feature_flag_user_overrides" ("feature_flag_id");
CREATE INDEX IF NOT EXISTS "idx_feature_flag_user_overrides_user_id" ON "public"."feature_flag_user_overrides" ("user_id");

-- Create updated_at trigger for feature_flags
CREATE OR REPLACE FUNCTION "public"."update_feature_flags_updated_at"()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER "feature_flags_updated_at_trigger"
    BEFORE UPDATE ON "public"."feature_flags"
    FOR EACH ROW
    EXECUTE FUNCTION "public"."update_feature_flags_updated_at"();

-- Enable Row Level Security
ALTER TABLE "public"."feature_flags" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "public"."feature_flag_user_overrides" ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for feature_flags
-- Allow authenticated users to read feature flags (needed for evaluation)
CREATE POLICY "feature_flags_read_authenticated" ON "public"."feature_flags"
    FOR SELECT TO authenticated
    USING (true);

-- Allow service role full access (for admin operations)
CREATE POLICY "feature_flags_admin_all" ON "public"."feature_flags"
    FOR ALL TO service_role
    USING (true)
    WITH CHECK (true);

-- Create RLS policies for feature_flag_user_overrides
-- Allow users to read their own overrides
CREATE POLICY "feature_flag_user_overrides_read_own" ON "public"."feature_flag_user_overrides"
    FOR SELECT TO authenticated
    USING (user_id = auth.uid());

-- Allow service role full access (for admin operations)
CREATE POLICY "feature_flag_user_overrides_admin_all" ON "public"."feature_flag_user_overrides"
    FOR ALL TO service_role
    USING (true)
    WITH CHECK (true);

-- Grant necessary permissions
GRANT SELECT ON "public"."feature_flags" TO authenticated;
GRANT SELECT ON "public"."feature_flag_user_overrides" TO authenticated;
GRANT ALL ON "public"."feature_flags" TO service_role;
GRANT ALL ON "public"."feature_flag_user_overrides" TO service_role;

-- Add comments for documentation
COMMENT ON TABLE "public"."feature_flags" IS 'Feature flags for controlling application features';
COMMENT ON COLUMN "public"."feature_flags"."name" IS 'Kebab-case identifier for the feature flag (e.g., new-chat-ui)';
COMMENT ON COLUMN "public"."feature_flags"."display_name" IS 'Human-readable name for admin UI';
COMMENT ON COLUMN "public"."feature_flags"."description" IS 'Description of what the feature does';
COMMENT ON COLUMN "public"."feature_flags"."is_enabled" IS 'Global on/off switch for the feature';
COMMENT ON COLUMN "public"."feature_flags"."rollout_percentage" IS 'Percentage of users who see the feature (0-100)';
COMMENT ON COLUMN "public"."feature_flags"."created_by" IS 'User who created this feature flag';

COMMENT ON TABLE "public"."feature_flag_user_overrides" IS 'User-specific overrides for feature flags';
COMMENT ON COLUMN "public"."feature_flag_user_overrides"."is_enabled" IS 'Override value for this specific user';
