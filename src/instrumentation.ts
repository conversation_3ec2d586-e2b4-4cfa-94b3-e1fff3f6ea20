import * as Sentry from '@sentry/nextjs';

export async function register() {
  if (process.env.NEXT_RUNTIME === 'nodejs') {
    // Initialize error monitoring services for Node.js runtime
    try {
      await import('../sentry.server.config');
      console.log('Sentry server configuration loaded');
    } catch (error) {
      console.error('Failed to load Sentry server configuration:', error);
    }

    try {
      await import('pino');
    } catch (error) {
      console.error('Failed to load pino logger:', error);
    }

    // Initialize vendor-agnostic OpenTelemetry configuration
    // Works with <PERSON><PERSON>, DataDog, Jaeger, or any OTLP-compatible backend
    try {
      const { initializeTelemetry } = await import('./lib/otel');
      initializeTelemetry();
    } catch (error) {
      console.error('Failed to initialize OpenTelemetry:', error);
    }
  }

  if (process.env.NEXT_RUNTIME === 'edge') {
    // Initialize error monitoring services for edge runtime
    // Note: Only Sentry is compatible with edge runtime
    try {
      await import('../sentry.edge.config');
      console.log('Sentry edge configuration loaded');
    } catch (error) {
      console.error('Failed to load Sentry edge configuration:', error);
    }
  }
}

export const onRequestError = Sentry.captureRequestError;
