'use client';
import VerifyOtpPage from '@/components/auth/Verify';
import { useAuth } from '@/components/auth/useAuth';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { logger } from '@/lib/logger';
const log = logger.child({
  module: 'VerifyPage',
});

export default function VerifyPage() {
  const router = useRouter();
  const {
    isLoading,
    error,
    message,
    otp,
    setOtp,
    handleVerifyOtp,
    setError,
    resendEmail,
    // handleUseAnotherEmail is removed from hook, handled by router now
  } = useAuth();

  // Guard: Redirect if verification email is not in localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const storedEmail = localStorage.getItem('verificationEmail');
      if (!storedEmail) {
        log.info('No verification email found, redirecting to login.');
        router.replace('/auth/login'); // Use replace to avoid adding verify to history
      }
    }
  }, [router]);

  // Function to handle "Use Another Email"
  const handleGoToLogin = () => {
    // Clear local storage email if desired
    if (typeof window !== 'undefined') {
      localStorage.removeItem('verificationEmail');
    }
    router.push('/auth/login');
  };

  return (
    <VerifyOtpPage
      error={error}
      message={message}
      isLoading={isLoading}
      otp={otp}
      setOtp={setOtp}
      handleVerifyOtp={handleVerifyOtp}
      handleUseAnotherEmail={handleGoToLogin} // Pass the router-based function
      setError={setError}
      resendEmail={resendEmail}
    />
  );
}
