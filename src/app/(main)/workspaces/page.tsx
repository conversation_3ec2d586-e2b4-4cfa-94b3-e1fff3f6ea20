'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Plus,
  Briefcase,
  FolderIcon,
  Settings,
  Trash2,
  Search,
  Loader2,
  AlertTriangle,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';
import { Workspace } from '@/lib/supabase/types';
import { CreateWorkspaceModal } from '@/components/workspaces/CreateWorkspaceModal';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { createClient } from '@/utils/supabase/client';
import { RealtimePostgresChangesPayload } from '@supabase/supabase-js';

export default function WorkspacesPage() {
  const router = useRouter();
  const [workspaces, setWorkspaces] = useState<Workspace[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const supabase = createClient();
  const [isCreateWorkspaceModalOpen, setIsCreateWorkspaceModalOpen] = useState(false);

  useEffect(() => {
    fetchWorkspaces();

    // Set up real-time subscription for workspace status changes
    const channel = supabase
      .channel('workspace-status-changes')
      .on(
        'postgres_changes',
        {
          event: '*', // Listen for inserts, updates, and deletes
          schema: 'public',
          table: 'workspaces',
        },
        (payload: RealtimePostgresChangesPayload<Workspace>) => {
          // Handle different types of changes
          if (payload.eventType === 'UPDATE') {
            const updatedWorkspace = payload.new;

            // Update the workspace in the state
            setWorkspaces((current) =>
              current.map((workspace) =>
                workspace.id === updatedWorkspace.id
                  ? { ...workspace, ...updatedWorkspace }
                  : workspace
              )
            );

            // Show a toast for status changes if status was modified
            if (payload.old.status !== updatedWorkspace.status) {
              if (updatedWorkspace.status === 'completed') {
                toast.success(`Workspace "${updatedWorkspace.name}" processing completed`);
              } else if (updatedWorkspace.status === 'error') {
                toast.error(`Workspace "${updatedWorkspace.name}" encountered an error`);
              }
            }
          } else if (payload.eventType === 'INSERT') {
            // If the user created a new workspace from another tab/device
            fetchWorkspaces();
          } else if (payload.eventType === 'DELETE') {
            // If the workspace was deleted from another tab/device
            const deletedWorkspaceId = payload.old.id;
            setWorkspaces((current) =>
              current.filter((workspace) => workspace.id !== deletedWorkspaceId)
            );
          }
        }
      )
      .subscribe();

    // Clean up the subscription when component unmounts
    return () => {
      supabase.removeChannel(channel);
    };
  }, []);

  async function fetchWorkspaces() {
    setIsLoading(true);
    try {
      const response = await fetch('/api/workspaces');
      if (!response.ok) throw new Error('Failed to fetch workspaces');
      const data = await response.json();
      setWorkspaces(data.workspaces || []);
    } catch (error) {
      console.error('Error fetching workspaces:', error);
      toast.error('Failed to load workspaces');
    } finally {
      setIsLoading(false);
    }
  }

  async function deleteWorkspace(id: string) {
    try {
      const response = await fetch(`/api/workspaces/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) throw new Error('Failed to delete workspace');

      setWorkspaces(workspaces.filter((workspace) => workspace.id !== id));
      toast.success('Workspace deleted successfully');
    } catch (error) {
      console.error('Error deleting workspace:', error);
      toast.error('Failed to delete workspace');
    }
  }

  const handleWorkspaceCreated = (workspaceId: string) => {
    fetchWorkspaces();
    router.push(`/workspaces/${workspaceId}`);
  };

  const filteredWorkspaces = workspaces.filter(
    (workspace) =>
      workspace.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (workspace.description || '')
        .toLowerCase()
        .includes(searchTerm.toLowerCase())
  );

  return (
    <div className='space-y-6'>
      <div className='flex flex-col gap-4'>
        <div className='flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4'>
          <h2 className='text-2xl sm:text-3xl font-bold tracking-tight'>Your Workspaces</h2>
          <div className='flex flex-col sm:flex-row gap-2 items-stretch sm:items-center'>
            <div className='relative flex-1 sm:w-64 sm:flex-none'>
              <Search className='absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground' />
              <Input
                placeholder='Search workspaces...'
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className='pl-9'
              />
            </div>

            <Button onClick={() => setIsCreateWorkspaceModalOpen(true)} className='w-full sm:w-auto'>
              <Plus className='mr-2 h-4 w-4' /> New Workspace
            </Button>
          </div>
        </div>
      </div>

      {isLoading ? (
        <div className='grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-4 sm:gap-6 mt-6'>
          {[...Array(3)].map((_, i) => (
            <Card
              key={i}
              className='overflow-hidden border border-border/40 shadow-sm hover:shadow-md transition-all'
            >
              <CardContent className='p-0'>
                <div className='h-28 bg-muted/30 flex items-center justify-center'>
                  <Skeleton className='h-12 w-12 rounded-full' />
                </div>
                <div className='p-5'>
                  <Skeleton className='h-5 w-3/4 mb-2' />
                  <Skeleton className='h-4 w-full' />
                </div>
              </CardContent>
              <CardFooter className='border-t p-3 bg-muted/10 flex justify-between'>
                <Skeleton className='h-8 w-20' />
              </CardFooter>
            </Card>
          ))}
        </div>
      ) : filteredWorkspaces.length === 0 && searchTerm ? (
        <div className='flex flex-col items-center justify-center py-12 px-4 border border-dashed rounded-lg bg-muted/10'>
          <p className='text-center text-muted-foreground mb-4'>
            No workspaces found matching &quot;{searchTerm}&quot;
          </p>
          <Button variant='outline' onClick={() => setSearchTerm('')}>
            Clear search
          </Button>
        </div>
      ) : workspaces.length === 0 ? (
        <div className='flex flex-col items-center justify-center py-12 px-4 border border-dashed rounded-lg bg-muted/10'>
          <Briefcase className='h-12 w-12 text-muted-foreground mb-4' />
          <h3 className='text-xl font-medium mb-2'>No workspaces yet</h3>
          <p className='text-muted-foreground text-center max-w-md mb-6'>
            Create your first workspace to store context, files, and notes for
            your conversations.
          </p>
          <Button onClick={() => setIsCreateWorkspaceModalOpen(true)}>
            <Plus className='mr-2 h-4 w-4' /> Create Workspace
          </Button>
        </div>
      ) : (
        <div className='grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-4 sm:gap-6 mt-6'>
          {filteredWorkspaces.map((workspace) => {
            const isProcessing =
              workspace.status === 'initiated' ||
              workspace.status === 'pending';
            const hasError = workspace.status === 'error';

            return (
              <Card
                key={workspace.id}
                className='overflow-hidden cursor-pointer border-b border-border shadow-sm hover:shadow-md transition-all duration-200'
                onClick={() => router.push(`/workspaces/${workspace.id}`)}
              >
                <CardContent className='p-0'>
                  <div className='h-28 bg-primary/5 flex items-center justify-center relative'>
                    <div className='h-14 w-14 rounded-full bg-primary/10 text-primary flex items-center justify-center text-2xl font-semibold'>
                      {workspace.icon || workspace.name.charAt(0).toUpperCase()}
                    </div>

                    {/* Status indicators */}
                    {isProcessing && (
                      <div className='absolute top-2 right-2'>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <div className='flex items-center gap-1 bg-amber-500/10 text-amber-500 px-2 py-1 rounded-full'>
                                <Loader2 className='h-3.5 w-3.5 animate-spin' />
                                <span className='text-xs font-medium'>
                                  Processing
                                </span>
                              </div>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p className='text-xs'>
                                Files are being processed and embedded
                              </p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                    )}

                    {hasError && (
                      <div className='absolute top-2 right-2'>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <div className='flex items-center gap-1 bg-destructive/10 text-destructive px-2 py-1 rounded-full'>
                                <AlertTriangle className='h-3.5 w-3.5' />
                                <span className='text-xs font-medium'>
                                  Error
                                </span>
                              </div>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p className='text-xs'>
                                There was an error processing the workspace
                              </p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                    )}
                  </div>
                  <div className='p-5'>
                    <h3 className='text-lg font-semibold mb-1.5'>
                      {workspace.name}
                    </h3>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <p className='text-muted-foreground text-sm line-clamp-2 min-h-[2.5rem] cursor-default'>
                            {workspace.description || 'No description provided'}
                          </p>
                        </TooltipTrigger>
                        {workspace.description && workspace.description.length > 100 && (
                          <TooltipContent side="bottom" className='max-w-xs'>
                            <p className='text-xs whitespace-pre-wrap'>
                              {workspace.description}
                            </p>
                          </TooltipContent>
                        )}
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                </CardContent>
                <CardFooter className='border-t p-3 bg-muted/5 flex justify-between items-center'>
                  <div className='text-xs text-muted-foreground flex items-center gap-1.5'>
                    <FolderIcon className='h-3 w-3' />
                    Files & Notes
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger
                      asChild
                      onClick={(e) => e.stopPropagation()}
                    >
                      <Button
                        variant='ghost'
                        size='icon'
                        className='h-8 w-8 rounded-full'
                      >
                        <Settings className='h-4 w-4' />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align='end'>
                      <DropdownMenuItem
                        className='text-destructive cursor-pointer'
                        onClick={(e) => {
                          e.stopPropagation();
                          deleteWorkspace(workspace.id);
                        }}
                      >
                        <Trash2 className='mr-2 h-4 w-4' />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </CardFooter>
              </Card>
            );
          })}
        </div>
      )}
      <CreateWorkspaceModal
        onSuccess={handleWorkspaceCreated}
        open={isCreateWorkspaceModalOpen}
        onOpenChange={setIsCreateWorkspaceModalOpen}
      />
    </div>
  );
}
