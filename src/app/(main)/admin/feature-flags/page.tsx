'use client';

import { useState, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { toast } from 'sonner';
import {
  Search,
  Plus,
  Settings,
  Trash2,
  Users,
  Loader2,
} from 'lucide-react';
import { FeatureFlag } from '@/lib/feature-flags/types';
import { FeatureFlagForm } from '@/components/admin/feature-flags/FeatureFlagForm';
import { FeatureFlagToggle } from '@/components/admin/feature-flags/FeatureFlagToggle';
import { UserOverrideManager } from '@/components/admin/feature-flags/UserOverrideManager';

export default function FeatureFlagsAdminPage() {
  const [flags, setFlags] = useState<FeatureFlag[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [selectedFlag, setSelectedFlag] = useState<FeatureFlag | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isOverrideModalOpen, setIsOverrideModalOpen] = useState(false);

  // Fetch feature flags
  const fetchFlags = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/feature-flags');

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.error) {
        throw new Error(data.error);
      }

      setFlags(data.data || []);
    } catch (error) {
      console.error('Error fetching feature flags:', error);
      toast.error('Failed to load feature flags');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchFlags();
  }, [fetchFlags]);

  // Filter flags based on search query
  const filteredFlags = flags.filter(flag =>
    flag.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    flag.display_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    flag.description?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Handle flag deletion
  const handleDeleteFlag = async (flagName: string) => {
    if (!confirm(`Are you sure you want to delete the feature flag "${flagName}"?`)) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/feature-flags/${encodeURIComponent(flagName)}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.error) {
        throw new Error(data.error);
      }

      toast.success('Feature flag deleted successfully');
      fetchFlags(); // Refresh the list
    } catch (error) {
      console.error('Error deleting feature flag:', error);
      toast.error('Failed to delete feature flag');
    }
  };

  // Handle flag toggle
  const handleToggleFlag = async (flag: FeatureFlag) => {
    try {
      const response = await fetch(`/api/admin/feature-flags/${encodeURIComponent(flag.name)}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          is_enabled: !flag.is_enabled,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.error) {
        throw new Error(data.error);
      }

      toast.success(`Feature flag ${flag.is_enabled ? 'disabled' : 'enabled'}`);
      fetchFlags(); // Refresh the list
    } catch (error) {
      console.error('Error toggling feature flag:', error);
      toast.error('Failed to toggle feature flag');
    }
  };

  const handleCreateSuccess = () => {
    setIsCreateModalOpen(false);
    fetchFlags();
    toast.success('Feature flag created successfully');
  };

  const handleEditSuccess = () => {
    setIsEditModalOpen(false);
    setSelectedFlag(null);
    fetchFlags();
    toast.success('Feature flag updated successfully');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Feature Flags</h1>
          <p className="text-muted-foreground">
            Manage feature flags and user overrides
          </p>
        </div>

        <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create Flag
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Create Feature Flag</DialogTitle>
              <DialogDescription>
                Create a new feature flag to control application features.
              </DialogDescription>
            </DialogHeader>
            <FeatureFlagForm
              mode="create"
              onSuccess={handleCreateSuccess}
              onCancel={() => setIsCreateModalOpen(false)}
            />
          </DialogContent>
        </Dialog>
      </div>

      {/* Search and Stats */}
      <div className="flex items-center gap-4">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search feature flags..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        <div className="flex gap-2">
          <Badge variant="secondary">
            Total: {flags.length}
          </Badge>
          <Badge variant="secondary">
            Enabled: {flags.filter(f => f.is_enabled).length}
          </Badge>
        </div>
      </div>

      {/* Feature Flags Table */}
      <div className="border rounded-lg">
        <ScrollArea className="h-[600px]">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Display Name</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Rollout</TableHead>
                <TableHead>Created</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredFlags.map((flag) => (
                <TableRow key={flag.id}>
                  <TableCell className="font-mono text-sm">
                    {flag.name}
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">{flag.display_name}</div>
                      {flag.description && (
                        <div className="text-sm text-muted-foreground">
                          {flag.description}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <FeatureFlagToggle
                      flag={flag}
                      onToggle={() => handleToggleFlag(flag)}
                    />
                  </TableCell>
                  <TableCell>
                    <Badge variant={flag.rollout_percentage > 0 ? 'default' : 'secondary'}>
                      {flag.rollout_percentage}%
                    </Badge>
                  </TableCell>
                  <TableCell className="text-sm text-muted-foreground">
                    {new Date(flag.created_at).toLocaleDateString()}
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex items-center justify-end gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setSelectedFlag(flag);
                          setIsOverrideModalOpen(true);
                        }}
                      >
                        <Users className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setSelectedFlag(flag);
                          setIsEditModalOpen(true);
                        }}
                      >
                        <Settings className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteFlag(flag.name)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </ScrollArea>
      </div>

      {/* Edit Modal */}
      <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Feature Flag</DialogTitle>
            <DialogDescription>
              Update the feature flag settings.
            </DialogDescription>
          </DialogHeader>
          {selectedFlag && (
            <FeatureFlagForm
              mode="edit"
              flag={selectedFlag}
              onSuccess={handleEditSuccess}
              onCancel={() => {
                setIsEditModalOpen(false);
                setSelectedFlag(null);
              }}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* User Override Modal */}
      <Dialog open={isOverrideModalOpen} onOpenChange={setIsOverrideModalOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>User Overrides</DialogTitle>
            <DialogDescription>
              Manage user-specific overrides for {selectedFlag?.display_name}.
            </DialogDescription>
          </DialogHeader>
          {selectedFlag && (
            <UserOverrideManager
              flag={selectedFlag}
              onClose={() => {
                setIsOverrideModalOpen(false);
                setSelectedFlag(null);
              }}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
