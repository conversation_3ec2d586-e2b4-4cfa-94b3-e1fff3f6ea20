'use client';

import { useFeatureFlag } from '@/hooks/useFeatureFlag';
import { IS_ADMIN_FLAG } from '@/lib/chatApi';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

interface AdminLayoutProps {
  children: React.ReactNode;
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  const { isEnabled: isAdmin, isLoading } = useFeatureFlag(IS_ADMIN_FLAG);
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && !isAdmin) {
      // Redirect to home if user doesn't have admin access
      router.push('/');
    }
  }, [isAdmin, isLoading, router]);

  // Show loading while checking permissions
  if (isLoading) {
    return (
      <div className='flex items-center justify-center min-h-screen'>
        <div className='animate-spin rounded-full h-32 w-32 border-b-2 border-primary'></div>
      </div>
    );
  }

  // Don't render admin content if user doesn't have access
  if (!isAdmin) {
    return (
      <div className='flex items-center justify-center min-h-screen'>
        <div className='text-center'>
          <h1 className='text-2xl font-bold text-red-600 mb-2'>
            Access Denied
          </h1>
          <p className='text-muted-foreground'>
            You don&apos;t have permission to access this page.
          </p>
        </div>
      </div>
    );
  }

  return <div className='min-h-screen bg-background'>{children}</div>;
}
