'use client';

import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { ArrowRightIcon, Zap, CreditCard } from 'lucide-react';
import { SubscriptionPlan, SUBSCRIPTION_PLANS } from '@/lib/supabase/types';
import { toast } from 'sonner';
import { useSubscription } from '@/providers/SubscriptionProvider';
import { usePurchaseVerification } from '@/hooks/usePurchaseVerification';
import { VerificationIndicator } from '@/components/ui/verification-indicator';

interface UsageData {
  tokens?: {
    used: number;
    remaining: number;
    total: number;
  };
  images?: {
    used: number;
    remaining: number;
    total: number;
  };
  comparisons?: {
    used: number;
    remaining: number;
    total: number;
  };
}

export default function BillingSettings() {
  const { subscription } = useSubscription();
  const searchParams = useSearchParams();
  const [actionLoading, setActionLoading] = useState(false);
  const [usageData, setUsageData] = useState<UsageData>({});
  const [addonPurchasing, setAddonPurchasing] = React.useState<string | null>(null);
  const [billingPeriod, setBillingPeriod] = useState<'monthly' | 'yearly'>(
    'monthly'
  );

  // Purchase verification hook
  const verification = usePurchaseVerification();

  useEffect(() => {
    fetchUsageData();
  }, [subscription]);

  // Handle purchase verification when returning from payment
  useEffect(() => {
    const success = searchParams.get('success');
    const sessionId = searchParams.get('session_id');

    if (
      success &&
      (success === 'tokens' || success === 'images') &&
      sessionId
    ) {
      // Start verification process
      verification.startVerification(sessionId, success as 'tokens' | 'images');
    }
  }, [searchParams, verification.startVerification]);

  // Update usage data when verification completes successfully
  useEffect(() => {
    if (verification.state.verificationComplete && !verification.state.error) {
      fetchUsageData();
    }
  }, [verification.state.verificationComplete, verification.state.error]);

  // Handle URL parameters for success/error notifications
  useEffect(() => {
    const success = searchParams.get('success');
    const canceled = searchParams.get('canceled');
    const quantity = searchParams.get('quantity');

    if (success) {
      if (success === 'tokens') {
        toast.success(
          `Successfully purchased ${quantity || '1'} token pack${quantity === '1' ? '' : 's'}! Verifying purchase...`
        );
      } else if (success === 'images') {
        toast.success(
          `Successfully purchased ${quantity || '1'} image credit pack${quantity === '1' ? '' : 's'}! Verifying purchase...`
        );
      } else if (success === 'true') {
        toast.success('Purchase completed successfully! Verifying...');
      }

      // Clear the URL parameters after showing the notification
      const url = new URL(window.location.href);
      url.searchParams.delete('success');
      url.searchParams.delete('quantity');
      url.searchParams.delete('session_id');
      window.history.replaceState({}, '', url.toString());
    }

    if (canceled === 'true') {
      toast.info('Purchase was canceled.');

      // Clear the URL parameters after showing the notification
      const url = new URL(window.location.href);
      url.searchParams.delete('canceled');
      window.history.replaceState({}, '', url.toString());
    }
  }, [searchParams]);

  const fetchUsageData = async () => {
    try {
      // Fetch current usage data
      const response = await fetch('/api/me/quota');
      if (response.ok) {
        const data = await response.json();
        setUsageData(data);
      }
    } catch (error) {
      console.error('Failed to fetch usage data:', error);
    }
  };

  const purchaseAddon = async (
    addonType: 'tokens' | 'images',
    quantity = 1
  ) => {
    try {
      setAddonPurchasing(addonType);
      const response = await fetch('/api/purchases/addons', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ addonType, quantity }),
      });

      const { url, error } = await response.json();

      if (error) {
        throw new Error(error);
      }

      if (url) {
        window.location.href = url;
      }
    } catch (error: unknown) {
      toast.error(
        error instanceof Error ? error.message : 'Failed to purchase add-on'
      );
    } finally {
      setAddonPurchasing(null);
    }
  };

  const upgradeSubscription = async (plan: SubscriptionPlan) => {
    if (!subscription) return;

    try {
      setActionLoading(true);
      const response = await fetch('/api/subscriptions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          plan,
          billingPeriod,
          returnUrl: window.location.href,
        }),
      });

      const { data, error } = await response.json();

      if (error) {
        throw new Error(error);
      }

      if (data.url) {
        window.location.href = data.url;
      } else if (data.message) {
        toast.info(data.message);
      }
    } catch (error: unknown) {
      toast.error(
        error instanceof Error
          ? error.message
          : 'Failed to upgrade subscription'
      );
    } finally {
      setActionLoading(false);
    }
  };

  const manageSubscription = async () => {
    if (!subscription) return;

    try {
      setActionLoading(true);
      const response = await fetch('/api/subscriptions', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'manage',
          returnUrl: window.location.href,
        }),
      });

      const { data, error } = await response.json();

      if (error) {
        throw new Error(error);
      }

      if (data.url) {
        window.location.href = data.url;
      }
    } catch (error: unknown) {
      toast.error(
        error instanceof Error ? error.message : 'Failed to manage subscription'
      );
    } finally {
      setActionLoading(false);
    }
  };

  const renderPlanCard = (plan: SubscriptionPlan) => {
    const planDetails = SUBSCRIPTION_PLANS[plan];
    const isCurrentPlan = subscription?.plan === plan;
    const isActive =
      subscription?.status === 'active' || subscription?.status === 'trialing';

    // Calculate pricing based on billing period
    const getDisplayPrice = () => {
      if (plan === 'free') return 'Free';

      const monthlyPrice = plan === 'starter' ? 12 : 24;

      if (billingPeriod === 'yearly') {
        const yearlyPrice = monthlyPrice * 10; // 2 months free (12 -> 120, 24 -> 240)
        const monthlySavings = monthlyPrice * 2;
        return (
          <div>
            <span className='text-2xl font-bold'>${yearlyPrice}</span>
            <span className='text-sm text-muted-foreground'>/year</span>
            <div className='text-xs text-green-600 font-medium'>
              Save ${monthlySavings} (2 months free)
            </div>
          </div>
        );
      }

      return `$${monthlyPrice}/month`;
    };

    return (
      <Card className='flex flex-col' key={plan}>
        <CardHeader>
          <div className='flex items-start justify-between gap-2'>
            <CardTitle className='flex-1 min-w-0'>{planDetails.name}</CardTitle>
            <div className='flex flex-wrap gap-1 shrink-0'>
              {isCurrentPlan && isActive && (
                <Badge
                  variant='outline'
                  className='text-green-600 bg-green-50 dark:bg-green-950 border-green-200 dark:border-green-800 text-xs'
                >
                  Active
                </Badge>
              )}
              {isCurrentPlan && subscription?.cancel_at_period_end && (
                <Badge
                  variant='outline'
                  className='text-orange-600 bg-orange-50 dark:bg-orange-950 border-orange-200 dark:border-orange-800 text-xs'
                >
                  Cancelling
                </Badge>
              )}
            </div>
          </div>
          <CardDescription>{getDisplayPrice()}</CardDescription>
        </CardHeader>
        <CardContent className='flex-grow'>
          <ul className='space-y-2'>
            {planDetails.features.map((feature, index) => (
              <li key={index} className='flex items-center gap-2'>
                <span className='h-1.5 w-1.5 rounded-full bg-primary'></span>
                {feature}
              </li>
            ))}
          </ul>
        </CardContent>
        <CardFooter>
          {isCurrentPlan ? (
            <Button
              variant='outline'
              disabled={actionLoading || plan === 'free'}
              onClick={manageSubscription}
              className='w-full'
            >
              Manage Subscription
            </Button>
          ) : (
            <Button
              onClick={() => upgradeSubscription(plan)}
              disabled={actionLoading}
              className='w-full'
            >
              {subscription && subscription.plan === 'free' && plan !== 'free'
                ? 'Upgrade'
                : subscription &&
                    subscription.plan !== 'free' &&
                    plan === 'free'
                  ? 'Downgrade'
                  : 'Switch Plan'}
              <ArrowRightIcon className='ml-2 h-4 w-4' />
            </Button>
          )}
        </CardFooter>
      </Card>
    );
  };

  // if (isLoading) {
  //   return (
  //     <div className='space-y-6'>
  //       <div>
  //         <h2 className='text-2xl font-bold'>Billing & Subscription</h2>
  //         <p className='text-muted-foreground'>
  //           Loading subscription information...
  //         </p>
  //       </div>
  //     </div>
  //   );
  // }

  return (
    <div className='space-y-6'>
      <div>
        <h2 className='text-2xl font-bold'>Billing & Subscription</h2>
        <p className='text-muted-foreground'>
          Manage your subscription plan and payment methods
        </p>
      </div>

      <Separator />

      {/* Purchase Verification Status */}
      {(verification.state.isVerifying ||
        verification.state.verificationComplete) && (
        <VerificationIndicator
          isVerifying={verification.state.isVerifying}
          verificationComplete={verification.state.verificationComplete}
          error={verification.state.error}
          confidence={verification.state.confidence}
          purchaseType={
            searchParams.get('success') === 'images' ? 'images' : 'tokens'
          }
        />
      )}

      <div className='space-y-8'>
        {/* Usage Dashboard */}
        {subscription && subscription.plan !== 'free' && (
          <div className='space-y-4'>
            <h3 className='text-lg font-medium'>Usage Overview</h3>
            <div className='grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-4'>
              {/* Token Usage */}
              {usageData.tokens && (
                <Card>
                  <CardHeader className='pb-3'>
                    <CardTitle className='text-sm font-medium'>
                      Token Usage
                    </CardTitle>
                  </CardHeader>
                  <CardContent className='space-y-2'>
                    <div className='flex justify-between text-sm'>
                      <span>Used</span>
                      <span className='font-mono text-right min-w-0 truncate'>
                        {usageData.tokens.used.toLocaleString()} /{' '}
                        {usageData.tokens.total.toLocaleString()}
                      </span>
                    </div>
                    <Progress
                      value={
                        usageData.tokens.total > 0
                          ? (usageData.tokens.used / usageData.tokens.total) *
                            100
                          : 0
                      }
                      className='h-2'
                    />
                    <p className='text-xs text-muted-foreground break-words'>
                      {usageData.tokens.remaining.toLocaleString()} tokens
                      remaining
                    </p>
                  </CardContent>
                  {subscription.plan === 'premium' && (
                    <CardFooter className='pt-0'>
                      <Button
                        size='sm'
                        variant='outline'
                        onClick={() => purchaseAddon('tokens')}
                        disabled={addonPurchasing === 'tokens'}
                        className='w-full text-xs sm:text-sm break-words min-h-0 h-auto py-2'
                      >
                        <Zap className='w-3 h-3 mr-1 shrink-0' />
                        <span className='truncate'>Buy 100K Tokens ($5)</span>
                      </Button>
                    </CardFooter>
                  )}
                </Card>
              )}

              {/* Image Credits */}
              {usageData.images && (
                <Card>
                  <CardHeader className='pb-3'>
                    <CardTitle className='text-sm font-medium'>
                      Image Credits
                    </CardTitle>
                  </CardHeader>
                  <CardContent className='space-y-2'>
                    <div className='flex justify-between text-sm'>
                      <span>Used</span>
                      <span className='font-mono text-right min-w-0 truncate'>
                        {usageData.images.used} / {usageData.images.total}
                      </span>
                    </div>
                    <Progress
                      value={
                        usageData.images.total > 0
                          ? (usageData.images.used / usageData.images.total) *
                            100
                          : 0
                      }
                      className='h-2'
                    />
                    <p className='text-xs text-muted-foreground break-words'>
                      {usageData.images.remaining} credits remaining
                    </p>
                  </CardContent>
                  <CardFooter className='pt-0'>
                    <Button
                      size='sm'
                      variant='outline'
                      onClick={() => purchaseAddon('images')}
                      disabled={addonPurchasing === 'images'}
                      className='w-full text-xs sm:text-sm break-words min-h-0 h-auto py-2'
                    >
                      <CreditCard className='w-3 h-3 mr-1 shrink-0' />
                      <span className='truncate'>Buy 1000 Credits ($10)</span>
                    </Button>
                  </CardFooter>
                </Card>
              )}

              {/* Comparison Usage (Premium only) */}
              {subscription.plan === 'premium' && usageData.comparisons && (
                <Card>
                  <CardHeader className='pb-3'>
                    <CardTitle className='text-sm font-medium'>
                      Model Comparisons
                    </CardTitle>
                  </CardHeader>
                  <CardContent className='space-y-2'>
                    <div className='flex justify-between text-sm'>
                      <span>Used Today</span>
                      <span className='font-mono text-right min-w-0 truncate'>
                        {usageData.comparisons.used} /{' '}
                        {usageData.comparisons.total}
                      </span>
                    </div>
                    <Progress
                      value={
                        usageData.comparisons.total > 0
                          ? (usageData.comparisons.used /
                              usageData.comparisons.total) *
                            100
                          : 0
                      }
                      className='h-2'
                    />
                    <p className='text-xs text-muted-foreground break-words'>
                      {usageData.comparisons.remaining} comparisons remaining
                      today
                    </p>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        )}

        {/* Subscription Plans */}
        <div className='space-y-4'>
          <div className='flex items-center justify-between'>
            <h3 className='text-lg font-medium'>Plans</h3>
            <div className='flex items-center space-x-2 bg-muted p-1 rounded-lg'>
              <Button
                variant={billingPeriod === 'monthly' ? 'default' : 'ghost'}
                size='sm'
                onClick={() => setBillingPeriod('monthly')}
                className='h-8'
              >
                Monthly
              </Button>
              <Button
                variant={billingPeriod === 'yearly' ? 'default' : 'ghost'}
                size='sm'
                onClick={() => setBillingPeriod('yearly')}
                className='h-8'
              >
                Yearly
                <Badge variant='secondary' className='ml-2 text-xs'>
                  Save 17%
                </Badge>
              </Button>
            </div>
          </div>
          <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6'>
            {Object.keys(SUBSCRIPTION_PLANS).map((plan) =>
              renderPlanCard(plan as SubscriptionPlan)
            )}
          </div>
        </div>

        {/* Current Subscription Details */}
        {subscription && subscription.plan !== 'free' && (
          <Card className='mt-8'>
            <CardHeader>
              <CardTitle>Current Subscription</CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='flex flex-col sm:flex-row justify-between gap-4'>
                <div>
                  <div className='flex items-center gap-2 mb-1'>
                    <h4 className='font-semibold'>
                      {SUBSCRIPTION_PLANS[subscription.plan].name} Plan
                    </h4>
                    <Badge
                      variant='outline'
                      className={
                        subscription.status === 'active' ||
                        subscription.status === 'trialing'
                          ? 'text-green-600 bg-green-50 dark:bg-green-950 border-green-200 dark:border-green-800'
                          : 'text-red-600 bg-red-50 dark:bg-red-950 border-red-200 dark:border-red-800'
                      }
                    >
                      {subscription.status === 'active'
                        ? 'Active'
                        : subscription.status === 'trialing'
                          ? 'Trialing'
                          : subscription.status === 'past_due'
                            ? 'Past Due'
                            : subscription.status === 'canceled'
                              ? 'Canceled'
                              : subscription.status}
                    </Badge>
                  </div>
                  <p className='text-sm text-muted-foreground'>
                    {
                      subscription.cancel_at_period_end &&
                      typeof subscription.current_period_end === 'number'
                        ? `Your plan will be cancelled on ${new Date(
                            subscription.current_period_end * 1000
                          ).toLocaleDateString()}.`
                        : !subscription.cancel_at_period_end &&
                            typeof subscription.current_period_end === 'number'
                          ? `Your plan renews on ${new Date(
                              subscription.current_period_end * 1000
                            ).toLocaleDateString()}.`
                          : 'Renewal date information is unavailable.' // Handle cases where renewal date isn't available or not a number
                    }
                  </p>
                </div>
                <Button
                  variant='outline'
                  disabled={actionLoading}
                  onClick={manageSubscription}
                  className='shrink-0'
                >
                  Manage Subscription
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
