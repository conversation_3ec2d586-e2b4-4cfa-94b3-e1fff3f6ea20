import { createClient } from '@/utils/supabase/server';
import {
  DatabaseService,
  Conversation,
  Message,
  Model,
} from '@/lib/supabase/db';
import { LLMService } from '@/services/llm/service';
import {
  TokenizerService,
  SupportedModel,
} from '@/services/tokenizer/tokenizer';
import { logger } from '@/lib/logger';
import { AppError, ErrorCode } from '@/lib/error';
import { User } from '@supabase/supabase-js';
import {
  Message as LLMMessage,
  MessageContentPart,
} from '@/services/llm/types';
import {
  canUserAccessModel,
  canUserSendMessage,
  incrementUserDailyMessageCount,
} from '@/lib/supabase/subscription';
import type {
  AttachmentMetadata,
  UrlCitationAnnotation,
  StreamMetadataClientData,
  StreamReceivedUsage,
  AssistantMessageCustomMetadata,
  VersionedAssistantMessageMetadata,
} from '@/lib/supabase/types';
import { ASSISTANT_MESSAGE_METADATA_VERSION } from '@/lib/supabase/types';
import type { Json } from '@/types/database.types';
import { WorkspaceContextService } from '@/services/workspace/context';
import { QuotaService } from '@/services/quota';
import { getCachedModel } from '@/services/cache/modelCache';
import { ImageHandler } from '@/services/imageHandler';

export const dynamic = 'force-dynamic';

interface ChatRequestPayload {
  conversationId?: string;
  // Message content is required for new messages but omitted for plain retries.
  message?: string;
  provider: string;
  model: string;
  groupConversationId?: string;
  comparisonIndex?: number;
  isComparison?: boolean;
  isTestMode: boolean;
  parentMessageId?: string;
  attachments?: Array<AttachmentMetadata>;
  useWebSearch?: boolean;
  useImageGeneration?: boolean;
  isTemporary?: boolean;
  messageId?: string;
  newContent?: string;
  isEdit?: boolean;
  workspaceId?: string;
}

function isAttachmentMetadata(obj: unknown): obj is AttachmentMetadata {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    typeof (obj as Record<string, unknown>).name === 'string' &&
    typeof (obj as Record<string, unknown>).type === 'string' &&
    typeof (obj as Record<string, unknown>).url === 'string'
  );
}

function formatMessage(message: Message): LLMMessage {
  const log = logger.child({ messageId: message.id });
  if (!['user', 'assistant', 'system'].includes(message.role)) {
    throw new AppError(
      `Invalid message role: ${message.role}`,
      ErrorCode.INVALID_REQUEST
    );
  }

  const msgWithAttachments = message as Message & {
    attachments?: AttachmentMetadata[] | null;
  };

  if (
    msgWithAttachments.role === 'user' &&
    msgWithAttachments.attachments &&
    Array.isArray(msgWithAttachments.attachments) &&
    msgWithAttachments.attachments.length > 0
  ) {
    const contentParts: MessageContentPart[] = [];

    if (msgWithAttachments.content?.trim()) {
      contentParts.push({ type: 'text', text: msgWithAttachments.content });
    }

    const validAttachments: AttachmentMetadata[] = [];
    for (const att of msgWithAttachments.attachments) {
      if (isAttachmentMetadata(att)) {
        validAttachments.push(att);
      } else {
        log.warn(
          'Invalid attachment format found in message:',
          msgWithAttachments.id,
          att
        );
      }
    }

    validAttachments.forEach((att) => {
      if (att.type.startsWith('image/')) {
        contentParts.push({
          type: 'image_url',
          image_url: { url: att.url, detail: 'auto' },
        });
      } else {
        log.info(`Adding file attachment reference: ${att.name} (${att.type})`);
        contentParts.push({
          type: 'file',
          filename: att.name,
          mime_type: att.type,
          url: att.url,
        });
      }
    });

    const finalContentParts = contentParts.filter((part) => part !== null);

    if (finalContentParts.length > 0) {
      return {
        role: msgWithAttachments.role as 'user',
        content: finalContentParts,
        id: msgWithAttachments.id,
        file_annotations: msgWithAttachments.file_annotations,
      };
    }
  }

  return {
    role: message.role as 'user' | 'assistant' | 'system',
    content:
      typeof message.content === 'string'
        ? message.content
        : JSON.stringify(message.content),
    id: message.id,
    file_annotations: message.file_annotations,
  };
}

async function buildMessageHistory(
  db: DatabaseService,
  conversationId: string,
  endMessageId?: string,
  limit: number = 40 // using a high enough limit, after which it's safe to miss a few messages
): Promise<LLMMessage[]> {
  const messages = await db.getConversationMessagesLimited(
    conversationId,
    limit
  );
  const messageMap = new Map(messages.map((msg) => [msg.id, msg]));

  const history: Message[] = [];
  let current = endMessageId ? messageMap.get(endMessageId) : undefined;
  let messageCount = 0;

  while (current && messageCount < limit) {
    history.unshift(current);
    current = current.parent_message_id
      ? messageMap.get(current.parent_message_id)
      : undefined;
    messageCount++;
  }

  return history.map(formatMessage);
}

async function generateGroupConversationTitle(
  db: DatabaseService,
  groupConversationId: string,
  message: string,
  isTestMode: boolean
) {
  const log = logger.child({ groupConversationId, message, isTestMode });
  try {
    const llm = LLMService.getInstance();
    const groupConversation =
      await db.getGroupConversation(groupConversationId);
    if (!groupConversation) {
      log.warn('Group conversation not found');
      return;
    }

    const title = await llm.generateGroupConversationTitle(message, isTestMode);

    if (title) {
      db.updateGroupConversation(groupConversationId, {
        title,
      });
    }
  } catch (error) {
    log.error({ err: error }, 'Error generating group conversation title');
  }
}

async function trackUsage(
  db: DatabaseService,
  user: User,
  llmModel: Model,
  conversation: Conversation,
  prompt: string,
  completion: string,
  messageId: string,
  turnNumber: number,
  tokenReservationId?: string | null,
  promptTokensOverride?: number,
  completionTokensOverride?: number
) {
  const tokenizer = TokenizerService.getInstance();
  let promptTokens: number;
  let completionTokens: number;
  let tokenSource: 'stream' | 'calculated' = 'calculated';

  if (
    typeof promptTokensOverride === 'number' &&
    typeof completionTokensOverride === 'number'
  ) {
    promptTokens = promptTokensOverride;
    completionTokens = completionTokensOverride;
    tokenSource = 'stream';
  } else {
    promptTokens = tokenizer.countTokens(
      prompt,
      llmModel.name as SupportedModel
    );
    completionTokens = tokenizer.countTokens(
      completion,
      llmModel.name as SupportedModel
    );
  }

  await db.createUsageLog({
    id: crypto.randomUUID(),
    user_id: user.id,
    provider_id: llmModel.provider_id,
    model_id: llmModel.id,
    conversation_id: conversation.id,
    tokens_used: promptTokens + completionTokens,
    prompt_tokens: promptTokens,
    completion_tokens: completionTokens,
    status: 'completed',
    metadata: {
      conversation_turn: Math.ceil(turnNumber / 2),
      source: tokenSource,
    } as unknown as Json,
    message_id: messageId,
    cost: null,
    created_at: null,
  });

  const quotaService = QuotaService.getInstance();
  try {
    if (tokenReservationId) {
      await quotaService.commitTokenReservation(
        tokenReservationId,
        promptTokens + completionTokens
      );
    } else {
      await quotaService.trackTokenUsage(
        user.id,
        promptTokens + completionTokens,
        llmModel.id
      );
    }
  } catch (error) {
    const log = logger.child({ userId: user.id, modelId: llmModel.id });
    log.error({ err: error }, 'Failed to commit token reservation');
  }
}

function createErrorEvent(message: string) {
  return `event: error\ndata: ${JSON.stringify({
    type: 'error',
    error: { message },
  })}\n\n`;
}

// Warm LLM client on cold start (Edge Runtime optimization)
if (typeof process !== 'undefined' && process.env.VERCEL) {
  void LLMService.getInstance();
}

export async function POST(req: Request): Promise<Response> {
  const requestId = crypto.randomUUID();
  const log = logger.child({ requestId });

  let tokenReservationId: string | null = null;
  let comparisonIncremented = false;
  let currentUserId: string | null = null;

  const encoder = new TextEncoder();

  const stream = new ReadableStream({
    async start(controller) {
      let isControllerClosed = false;

      const safeClose = () => {
        if (!isControllerClosed) {
          controller.close();
          isControllerClosed = true;
        }
      };

      try {
        // 1. Immediate header flush with keep-alive
        controller.enqueue(encoder.encode('retry: 10000\n\n:keepalive\n\n'));

        // 2. Parse request payload
        const payload: ChatRequestPayload = await req.json();
        const {
          conversationId,
          message,
          model,
          groupConversationId,
          comparisonIndex,
          isComparison,
          isTestMode,
          parentMessageId,
          attachments,
          useWebSearch,
          useImageGeneration,
          isTemporary,
          workspaceId,
          messageId,
          newContent,
          isEdit,
        } = payload;

        // Introduce a variable to hold the original user message content for plain retries
        let retryUserMessageContent: string | undefined;

        // Early validation: For brand-new messages (no messageId), the `message` field is required
        if (!messageId && (!message || message.trim() === '')) {
          controller.enqueue(
            encoder.encode(
              createErrorEvent(
                'A non-empty "message" field is required when creating a new chat message'
              )
            )
          );
          safeClose();
          return;
        }

        // New validation: For edit operations, `newContent` must be provided and non-empty
        if (isEdit) {
          if (!newContent || newContent.trim() === '') {
            controller.enqueue(
              encoder.encode(
                createErrorEvent(
                  'A non-empty "newContent" field is required when editing a chat message'
                )
              )
            );
            safeClose();
            return;
          }
        }

        // 3. Initialize services
        const supabase = await createClient();
        const db = DatabaseService.getInstance(supabase);
        const llm = LLMService.getInstance();
        const quotaService = QuotaService.getInstance();

        // If this is a plain retry (messageId present and not an edit), fetch the original message content
        if (messageId && !isEdit) {
          try {
            const prevMessage = await db.getMessage(messageId);
            if (prevMessage) {
              retryUserMessageContent =
                typeof prevMessage.content === 'string'
                  ? prevMessage.content
                  : JSON.stringify(prevMessage.content);
            }
          } catch (prevErr) {
            log.warn(
              { err: prevErr, messageId },
              'Failed to fetch previous user message for retry'
            );
          }
        }

        // New validation: A retry must have retrievable, non-empty original content
        if (
          messageId &&
          !isEdit &&
          (!retryUserMessageContent || retryUserMessageContent.trim() === '')
        ) {
          controller.enqueue(
            encoder.encode(
              createErrorEvent(
                'Failed to retrieve original message content for retry'
              )
            )
          );
          safeClose();
          return;
        }

        // 4. Parallel user and model validation
        const [user, llmModel] = await Promise.all([
          db.getCurrentUser(),
          getCachedModel(db, model),
        ]);

        currentUserId = user?.id || null;
        if (!user) {
          controller.enqueue(encoder.encode(createErrorEvent('Unauthorized')));
          safeClose();
          return;
        }

        if (!llmModel) {
          controller.enqueue(
            encoder.encode(createErrorEvent(`Invalid model: ${model}`))
          );
          safeClose();
          return;
        }

        // 5. Parallel permission and quota checks
        const [canAccessModel, msgQuota, tokenQuota] = await Promise.all([
          canUserAccessModel(user.id, llmModel.id),
          canUserSendMessage(user.id),
          quotaService.checkTokenQuota(user.id),
        ]);

        if (!canAccessModel) {
          controller.enqueue(
            encoder.encode(
              createErrorEvent('This model requires a premium subscription')
            )
          );
          safeClose();
          return;
        }

        if (useWebSearch && !llmModel.allows_search) {
          controller.enqueue(
            encoder.encode(
              createErrorEvent(
                `Web search is not supported by the selected model (${llmModel.display_name}).`
              )
            )
          );
          safeClose();
          return;
        }

        const { allowed, reason } = msgQuota;
        if (!allowed) {
          controller.enqueue(
            encoder.encode(
              createErrorEvent(
                reason || 'You have reached your daily message limit'
              )
            )
          );
          safeClose();
          return;
        }

        // 6. Token quota and reservation (simplified)
        if (tokenQuota.tokenQuota !== null) {
          if (!tokenQuota.canUse) {
            const fallback = await quotaService.handleTokenOverage(user.id);
            if (fallback.reason === 'quota_exceeded' && fallback.modelId) {
              controller.enqueue(
                encoder.encode(
                  createErrorEvent(
                    `Token quota exceeded. Please use ${fallback.modelId} or upgrade your plan.`
                  )
                )
              );
            } else {
              controller.enqueue(
                encoder.encode(
                  createErrorEvent(
                    'Token quota exceeded. Please upgrade your plan.'
                  )
                )
              );
            }
            safeClose();
            return;
          }

          try {
            // Ensure we use the correct text when estimating tokens, especially during retries
            const textForEstimation =
              newContent ?? message ?? retryUserMessageContent ?? '';
            const estimatedTokens = Math.min(
              4000,
              textForEstimation.length * 4
            );
            const reservation = await quotaService.reserveTokenUsage(
              user.id,
              estimatedTokens,
              llmModel.id
            );

            if (!reservation.success) {
              controller.enqueue(
                encoder.encode(
                  createErrorEvent('Token quota exceeded for this message')
                )
              );
              safeClose();
              return;
            }

            tokenReservationId = reservation.reservationId;
          } catch (tokenError) {
            log.warn('Failed to reserve tokens', { error: tokenError });
          }
        }

        // 7. Comparison quota check and increment
        if (isComparison) {
          await quotaService.incrementComparisonUsage(user.id);
          comparisonIncremented = true;
        }

        // 8. Handle different scenarios: new message, retry, or edit
        let finalConversationId: string;
        let finalGroupConversationId: string;
        let userMessageId: string;
        let assistantMessageId: string;
        let isNewConversation: boolean;

        if (messageId) {
          // This is a retry or edit scenario
          if (!conversationId) {
            controller.enqueue(
              encoder.encode(
                createErrorEvent('Conversation ID required for retry/edit')
              )
            );
            safeClose();
            return;
          }

          finalConversationId = conversationId;

          // Get the conversation to find group conversation ID
          const conversation = await db.getConversation(conversationId);
          if (!conversation) {
            controller.enqueue(
              encoder.encode(createErrorEvent('Conversation not found'))
            );
            safeClose();
            return;
          }

          finalGroupConversationId = conversation.group_conversation_id;
          isNewConversation = false;

          if (isEdit) {
            // For edits: get the original message to find its parent, then create sibling
            const originalMessage = await db.getMessage(messageId);
            if (!originalMessage) {
              controller.enqueue(
                encoder.encode(
                  createErrorEvent('Original message not found for edit')
                )
              );
              safeClose();
              return;
            }

            const userMessage = await db.createMessage({
              conversationId: finalConversationId,
              content: newContent!,
              role: 'user',
              parentMessageId: originalMessage.parent_message_id || undefined, // Use same parent as original (creates sibling)
              attachments,
            });
            userMessageId = userMessage.id;
          } else {
            // For retries: use the existing user message
            userMessageId = messageId;
          }

          // Always create a new assistant placeholder
          const assistantMessage = await db.createMessage({
            conversationId: finalConversationId,
            content: '',
            role: 'assistant',
            parentMessageId: userMessageId,
            modelId: llmModel.id,
            providerId: llmModel.provider_id as string,
          });
          assistantMessageId = assistantMessage.id;
        } else {
          // This is a new message - use the existing atomic RPC
          const chatSession = await db.initializeChatSession({
            userId: user.id,
            // message is guaranteed to be defined in the new-message flow
            messageContent: message!,
            modelId: llmModel.id,
            providerId: llmModel.provider_id as string,
            conversationId,
            groupConversationId,
            parentMessageId,
            attachments,
            workspaceId,
            isComparison,
            isTemporary,
            comparisonIndex,
          });

          ({
            conversation_id: finalConversationId,
            group_conversation_id: finalGroupConversationId,
            user_message_id: userMessageId,
            assistant_message_id: assistantMessageId,
            is_new_conversation: isNewConversation,
          } = chatSession);
        }

        // ------------------------------------------------------------------
        // Send INIT event so client can swap placeholder IDs immediately
        // ------------------------------------------------------------------
        let eventId = Date.now();
        controller.enqueue(
          encoder.encode(
            `event: init\nid: ${eventId}\ndata: ${JSON.stringify({
              conversationId: finalConversationId,
              groupConversationId: finalGroupConversationId,
              assistantMessageId,
              userMessageId,
              isNewConversation,
              isTemporary: !!isTemporary,
              tokenReservationId,
            })}\n\n`
          )
        );

        // Create minimal objects for existing API compatibility
        const conversation = {
          id: finalConversationId,
          group_conversation_id: finalGroupConversationId,
          user_id: user.id,
          title: '',
          created_at: '',
          updated_at: '',
          last_model_used: llmModel.id,
          comparison_index: comparisonIndex || null,
          anonymous_session_id: null,
          imported_from_share_id: null,
          system_prompt: null,
          state: 'healthy' as const,
          metadata: null,
        } as unknown as Conversation;

        // Get the correct user message content based on scenario
        let userMessageContent: string;
        if (messageId && isEdit) {
          userMessageContent = newContent!;
        } else if (messageId && !isEdit) {
          // Plain retry – fall back to the previously fetched content
          userMessageContent = retryUserMessageContent || '';
        } else {
          userMessageContent = message || '';
        }

        const userMessage = {
          id: userMessageId,
          content: userMessageContent,
          conversation_id: finalConversationId,
        };

        // 9. Generate title asynchronously for new conversations
        if (isNewConversation && finalGroupConversationId) {
          void generateGroupConversationTitle(
            db,
            finalGroupConversationId,
            message!,
            isTestMode
          );
        }

        // 10. Build message history (optimized to 20 messages)
        const baseHistory = await buildMessageHistory(
          db,
          finalConversationId,
          userMessageId
        );

        // 11. Build workspace context (if needed)
        let contextMessages: LLMMessage[] = [];
        if (workspaceId) {
          const workspace = await db.getWorkspace(workspaceId);
          if (workspace) {
            contextMessages = await WorkspaceContextService.buildContext(
              supabase,
              workspace,
              typeof userMessage.content === 'string'
                ? userMessage.content
                : JSON.stringify(userMessage.content)
            );
          }
        }

        const messageHistory = [...contextMessages, ...baseHistory];

        // 12. Start LLM streaming
        const providerNameLower = llmModel.provider?.name?.toLowerCase();
        if (!providerNameLower) {
          controller.enqueue(
            encoder.encode(
              createErrorEvent(`Provider not found for model: ${llmModel.name}`)
            )
          );
          safeClose();
          return;
        }

        const llmStream = await llm.generateStreamingCompletion(
          providerNameLower,
          messageHistory,
          {
            model: llmModel,
            useWebSearch: useWebSearch,
            useImageGeneration: useImageGeneration,
          },
          isTestMode
        );

        // Track image generation promise (if any) so we can await it before finalizing
        let imageGenerationPromise: Promise<unknown> | null = null;

        // 13. Stream processing
        let assistantContent = '';
        const assistantUrlAnnotations: UrlCitationAnnotation[] = [];
        const assistantFileAnnotations: unknown[] = [];
        let buffer = '';
        eventId = Date.now();
        let latestStreamUsage: StreamReceivedUsage | undefined = undefined;
        const messageMetadata: Omit<StreamMetadataClientData, 'usage'> = {};

        const reader = llmStream.getReader();
        const decoder = new TextDecoder();

        try {
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            const decodedChunk = decoder.decode(value, { stream: true });
            buffer += decodedChunk;
            let newlineIndex;

            while ((newlineIndex = buffer.indexOf('\n')) >= 0) {
              const line = buffer.slice(0, newlineIndex).trim();
              buffer = buffer.slice(newlineIndex + 1);

              if (line && line !== '[DONE]') {
                try {
                  const parsed = JSON.parse(line);

                  if (parsed.type === 'delta' && parsed.content) {
                    eventId++;
                    assistantContent += parsed.content;
                    controller.enqueue(
                      encoder.encode(
                        `event: delta\nid: ${eventId}\ndata: ${JSON.stringify({
                          type: 'delta',
                          content: parsed.content,
                        })}\n\n`
                      )
                    );
                  } else if (
                    parsed.type === 'tool' &&
                    parsed.name === 'generate_image'
                  ) {
                    // Image generation tool call handling
                    const { prompt, size } = parsed.args || {};

                    eventId++;

                    // Send placeholder to indicate image generation is in progress
                    assistantContent = 'Generating image...';
                    controller.enqueue(
                      encoder.encode(
                        `event: delta\nid: ${eventId}\ndata: ${JSON.stringify({
                          type: 'delta',
                          content: 'Generating image...',
                          replace: true,
                        })}\n\n`
                      )
                    );

                    const imageHandler = ImageHandler.getInstance(supabase);

                    imageGenerationPromise = imageHandler
                      .handleImageToolCall(
                        user,
                        assistantMessageId,
                        prompt,
                        (size as '512x512' | '768x768' | '1024x1024') ||
                          '768x768',
                        (deltaData) => {
                          try {
                            eventId++;
                            assistantContent = deltaData.content;
                            controller.enqueue(
                              encoder.encode(
                                `event: delta\nid: ${eventId}\ndata: ${JSON.stringify(
                                  {
                                    type: 'delta',
                                    content: deltaData.content,
                                    replace: deltaData.replace,
                                    attachments: deltaData.attachments,
                                  }
                                )}\n\n`
                              )
                            );
                            log.info(
                              'Successfully streamed image result to client'
                            );
                          } catch (enqueueError) {
                            log.warn(
                              'Failed to enqueue image result:',
                              enqueueError
                            );
                          }
                        }
                      )
                      .catch((error) => {
                        log.error('Error during image generation', error);
                        // Send fallback error delta
                        try {
                          eventId++;
                          assistantContent =
                            'Sorry, an error occurred while generating the image.';
                          controller.enqueue(
                            encoder.encode(
                              `event: delta\nid: ${eventId}\ndata: ${JSON.stringify(
                                {
                                  type: 'delta',
                                  content: assistantContent,
                                  replace: true,
                                  attachments: null,
                                }
                              )}\n\n`
                            )
                          );
                        } catch (enqueueErr) {
                          log.warn(
                            'Failed to enqueue image error message:',
                            enqueueErr
                          );
                        }
                      });
                  } else if (
                    parsed.type === 'annotation' &&
                    parsed.annotation
                  ) {
                    if (parsed.annotation.type === 'url_citation') {
                      eventId++;
                      assistantUrlAnnotations.push(parsed.annotation);
                      controller.enqueue(
                        encoder.encode(
                          `event: annotation\nid: ${eventId}\ndata: ${JSON.stringify(
                            {
                              type: 'annotation',
                              annotation: parsed.annotation,
                            }
                          )}\n\n`
                        )
                      );
                    } else {
                      assistantFileAnnotations.push(parsed.annotation);
                    }
                  } else if (parsed.type === 'metadata' && parsed.data) {
                    const metadata = parsed.data as StreamMetadataClientData;
                    if (metadata.usage) {
                      latestStreamUsage = metadata.usage;
                    }
                    if (Object.keys(messageMetadata).length === 0) {
                      if (metadata.provider)
                        messageMetadata.provider = metadata.provider;
                      if (metadata.model)
                        messageMetadata.model = metadata.model;
                      if (metadata.id) messageMetadata.id = metadata.id;
                      if (metadata.object_type)
                        messageMetadata.object_type = metadata.object_type;
                      if (metadata.created_timestamp)
                        messageMetadata.created_timestamp =
                          metadata.created_timestamp;

                      controller.enqueue(
                        encoder.encode(
                          `event: metadata\nid: ${eventId}\ndata: ${JSON.stringify(
                            {
                              type: 'metadata',
                              metadata: {
                                version: ASSISTANT_MESSAGE_METADATA_VERSION,
                                data: metadata,
                              },
                            }
                          )}\n\n`
                        )
                      );
                    }
                  } else if (parsed.type === 'error') {
                    eventId++;
                    controller.enqueue(
                      encoder.encode(
                        `event: error\nid: ${eventId}\ndata: ${JSON.stringify({
                          type: 'error',
                          error: parsed.error,
                        })}\n\n`
                      )
                    );
                  }
                } catch (e) {
                  log.error('Error parsing JSON chunk:', e, line);
                }
              }
            }
          }

          // Await image generation (with timeout) before finalizing
          if (imageGenerationPromise) {
            try {
              await Promise.race([
                imageGenerationPromise,
                new Promise((resolve) => setTimeout(resolve, 30_000)), // 30s cap
              ]);
            } catch (imgErr) {
              log.warn(
                'Image generation promise rejected or timed out',
                imgErr
              );
            }
          }

          // Process any remaining buffer
          if (buffer.trim()) {
            const line = buffer.trim();
            if (line && line !== '[DONE]') {
              try {
                const parsed = JSON.parse(line);
                if (parsed.type === 'delta' && parsed.content) {
                  assistantContent += parsed.content;
                } else if (parsed.type === 'annotation' && parsed.annotation) {
                  if (parsed.annotation.type === 'url_citation') {
                    assistantUrlAnnotations.push(parsed.annotation);
                  } else {
                    assistantFileAnnotations.push(parsed.annotation);
                  }
                }
              } catch (e) {
                log.error('Error parsing final JSON chunk:', e, line);
              }
            }
          }

          // 14. Save final message and track usage
          const rawAssistantMetadata: AssistantMessageCustomMetadata = {
            ...messageMetadata,
          };
          if (latestStreamUsage) {
            rawAssistantMetadata.usage = latestStreamUsage;
          }

          let finalMetadataForDb: VersionedAssistantMessageMetadata | null =
            null;
          if (
            Object.keys(rawAssistantMetadata).length > 0 ||
            latestStreamUsage
          ) {
            finalMetadataForDb = {
              version: ASSISTANT_MESSAGE_METADATA_VERSION,
              data: rawAssistantMetadata,
            };
          }

          const messageUpdates: Partial<Message> = {
            content: assistantContent.trim(),
            annotations:
              assistantUrlAnnotations.length > 0
                ? assistantUrlAnnotations
                : null,
            file_annotations:
              assistantFileAnnotations.length > 0
                ? assistantFileAnnotations
                : null,
          };

          if (finalMetadataForDb) {
            messageUpdates.metadata = finalMetadataForDb;
          }

          await db.updateMessage(assistantMessageId, messageUpdates);

          // Track usage
          await trackUsage(
            db,
            user,
            llmModel,
            conversation,
            typeof userMessage.content === 'string'
              ? userMessage.content
              : '[User message with attachments]',
            assistantContent,
            assistantMessageId,
            messageHistory.length,
            tokenReservationId,
            latestStreamUsage?.prompt_tokens,
            latestStreamUsage?.completion_tokens
          );

          // Defer non-critical quota writes
          void incrementUserDailyMessageCount(user.id);

          // Send done event
          eventId++;
          controller.enqueue(
            encoder.encode(`event: done\nid: ${eventId}\ndata: {}\n\n`)
          );
        } finally {
          reader.releaseLock();
        }
      } catch (error) {
        // Error handling and cleanup
        const cleanupPromises: Promise<unknown>[] = [];

        try {
          const quotaService = QuotaService.getInstance();

          // Clean up token reservation - don't let failure block other cleanups
          if (tokenReservationId) {
            cleanupPromises.push(
              quotaService
                .cancelTokenReservation(tokenReservationId)
                .catch((cleanupError) => {
                  log.error(
                    { err: cleanupError, reservationId: tokenReservationId },
                    'Failed to cancel token reservation'
                  );
                })
            );
          }

          // Clean up comparison usage - don't let failure block other cleanups
          if (comparisonIncremented && currentUserId) {
            cleanupPromises.push(
              quotaService
                .decrementComparisonUsage(currentUserId)
                .catch((cleanupError) => {
                  log.error(
                    { err: cleanupError, userId: currentUserId },
                    'Failed to decrement comparison usage'
                  );
                })
            );
          }

          // Wait for all cleanup operations (with timeout)
          await Promise.allSettled(cleanupPromises);
        } catch (overallCleanupError) {
          log.error(
            { err: overallCleanupError },
            'Failed to roll back quota changes'
          );
        }

        log.error({ err: error, requestId }, 'Error in unified stream');

        // Create a user-friendly error message that doesn't expose internal details
        let userMessage =
          'An error occurred while processing your request. Please try again.';

        if (error instanceof AppError) {
          // For AppErrors, check if the message is user-friendly
          if (
            error.code === 'QUOTA_EXCEEDED' ||
            error.code === 'AI_RATE_LIMIT_ERROR' ||
            error.code === 'AUTH_INVALID_CREDENTIALS'
          ) {
            userMessage = error.message;
          } else if (error.code === 'AI_STREAMING_ERROR') {
            userMessage =
              'There was an issue with the AI response. Please try again.';
          }
        }

        controller.enqueue(encoder.encode(createErrorEvent(userMessage)));
      } finally {
        safeClose();
      }
    },
  });

  return new Response(stream, {
    status: 200,
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache, no-transform',
      Connection: 'keep-alive',
      'X-Accel-Buffering': 'no',
    },
  });
}
