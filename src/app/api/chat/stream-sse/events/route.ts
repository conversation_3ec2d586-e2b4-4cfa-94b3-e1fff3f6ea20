import { createClient } from '@/utils/supabase/server';
import { SupabaseClient } from '@supabase/supabase-js';
import {
  DatabaseService,
  Conversation,
  Message,
  Model,
} from '@/lib/supabase/db';
import { LLMService } from '@/services/llm/service';
import {
  TokenizerService,
  SupportedModel,
} from '@/services/tokenizer/tokenizer';
import { logger } from '@/lib/logger';
import { AppError, ErrorCode, handleError } from '@/lib/error';
import { User } from '@supabase/supabase-js';
import {
  Message as LLMMessage,
  MessageContentPart,
} from '@/services/llm/types';
import {
  canUserAccessModel,
  hasUserAccessToFeature,
} from '@/lib/supabase/subscription';
import type {
  AttachmentMetadata,
  UrlCitationAnnotation,
  StreamMetadataClientData,
  StreamReceivedUsage,
  AssistantMessageCustomMetadata,
  VersionedAssistantMessageMetadata,
} from '@/lib/supabase/types';
import { ASSISTANT_MESSAGE_METADATA_VERSION } from '@/lib/supabase/types';
import type { Json } from '@/types/database.types';
import { WorkspaceContextService } from '@/services/workspace/context';
import { ImageHandler } from '@/services/imageHandler';
import { QuotaService } from '@/services/quota';
import { getCachedModel } from '@/services/cache/modelCache';
import { sseEncoder, sseDecoder } from '@/lib/sse-utils';

export const dynamic = 'force-dynamic';

function isAttachmentMetadata(obj: unknown): obj is AttachmentMetadata {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    typeof (obj as Record<string, unknown>).name === 'string' &&
    typeof (obj as Record<string, unknown>).type === 'string' &&
    typeof (obj as Record<string, unknown>).url === 'string'
  );
}

function formatMessage(message: Message): LLMMessage {
  const log = logger.child({ messageId: message.id });
  if (!['user', 'assistant', 'system'].includes(message.role)) {
    throw new AppError(
      `Invalid message role: ${message.role}`,
      ErrorCode.INVALID_REQUEST
    );
  }

  const msgWithAttachments = message as Message & {
    attachments?: AttachmentMetadata[] | null;
  };

  if (
    msgWithAttachments.role === 'user' &&
    msgWithAttachments.attachments &&
    Array.isArray(msgWithAttachments.attachments) &&
    msgWithAttachments.attachments.length > 0
  ) {
    const contentParts: MessageContentPart[] = [];

    if (msgWithAttachments.content?.trim()) {
      contentParts.push({ type: 'text', text: msgWithAttachments.content });
    }

    const validAttachments: AttachmentMetadata[] = [];
    for (const att of msgWithAttachments.attachments) {
      if (isAttachmentMetadata(att)) {
        validAttachments.push(att);
      } else {
        log.warn(
          'Invalid attachment format found in message:',
          msgWithAttachments.id,
          att
        );
      }
    }

    validAttachments.forEach((att) => {
      if (att.type.startsWith('image/')) {
        contentParts.push({
          type: 'image_url',
          image_url: { url: att.url, detail: 'auto' },
        });
      } else {
        log.info(`Adding file attachment reference: ${att.name} (${att.type})`);
        contentParts.push({
          type: 'file',
          filename: att.name,
          mime_type: att.type,
          url: att.url,
        });
      }
    });

    const finalContentParts = contentParts.filter((part) => part !== null);

    if (finalContentParts.length > 0) {
      return {
        role: msgWithAttachments.role as 'user',
        content: finalContentParts,
        id: msgWithAttachments.id,
        file_annotations: msgWithAttachments.file_annotations,
      };
    }
  }

  return {
    role: message.role as 'user' | 'assistant' | 'system',
    content:
      typeof message.content === 'string'
        ? message.content
        : JSON.stringify(message.content),
    id: message.id,
    file_annotations: message.file_annotations,
  };
}

async function buildMessageHistory(
  db: DatabaseService,
  conversationId: string,
  endMessageId?: string,
  limit: number = 200 // Optimize: limit history to last 20 messages. Safe to ignore anything afterawards
): Promise<LLMMessage[]> {
  // Retrieve messages up to the endMessageId if provided.
  const messages = await db.getConversationMessagesLimited(
    conversationId,
    limit
  );
  const messageMap = new Map(messages.map((msg) => [msg.id, msg]));

  const history: Message[] = [];
  let current = endMessageId ? messageMap.get(endMessageId) : undefined;
  let messageCount = 0;

  while (current && messageCount < limit) {
    history.unshift(current);
    current = current.parent_message_id
      ? messageMap.get(current.parent_message_id)
      : undefined;
    messageCount++;
  }

  return history.map(formatMessage);
}

async function trackUsage(
  db: DatabaseService,
  user: User,
  llmModel: Model,
  conversation: Conversation,
  prompt: string,
  completion: string,
  messageId: string,
  turnNumber: number,
  tokenReservationId?: string | null,
  promptTokensOverride?: number,
  completionTokensOverride?: number
) {
  const tokenizer = TokenizerService.getInstance();
  let promptTokens: number;
  let completionTokens: number;
  let tokenSource: 'stream' | 'calculated' = 'calculated';

  if (
    typeof promptTokensOverride === 'number' &&
    typeof completionTokensOverride === 'number'
  ) {
    promptTokens = promptTokensOverride;
    completionTokens = completionTokensOverride;
    tokenSource = 'stream';
  } else {
    promptTokens = tokenizer.countTokens(
      prompt,
      llmModel.name as SupportedModel
    );
    completionTokens = tokenizer.countTokens(
      completion,
      llmModel.name as SupportedModel
    );
  }

  await db.createUsageLog({
    id: crypto.randomUUID(),
    user_id: user.id,
    provider_id: llmModel.provider_id,
    model_id: llmModel.id,
    conversation_id: conversation.id,
    tokens_used: promptTokens + completionTokens,
    prompt_tokens: promptTokens,
    completion_tokens: completionTokens,
    status: 'completed',
    metadata: {
      conversation_turn: Math.ceil(turnNumber / 2),
      source: tokenSource,
    } as unknown as Json,
    message_id: messageId,
    cost: null,
    created_at: null,
  });

  // FIXED: Commit token reservation (tokens already reserved in init)
  // No need to track again - tokens were already charged during reservation
  const quotaService = QuotaService.getInstance();
  try {
    if (tokenReservationId) {
      // Commit the original reservation created during the init request
      await quotaService.commitTokenReservation(
        tokenReservationId,
        promptTokens + completionTokens
      );
    } else {
      // No reservation was made (legacy flow) – directly track usage
      await quotaService.trackTokenUsage(
        user.id,
        promptTokens + completionTokens,
        llmModel.id
      );
    }
  } catch (error) {
    const log = logger.child({ userId: user.id, modelId: llmModel.id });
          log.error({ err: error }, 'Failed to commit token reservation');
    // Don't fail the request if commit fails
  }
}

function createErrorEvent(message: string) {
  return `event: error\ndata: ${JSON.stringify({
    type: 'error',
    error: { message },
  })}\n\n`;
}

export async function GET(request: Request): Promise<Response> {
  const requestId = crypto.randomUUID();
  const log = logger.child({ requestId, type: 'sse' });

  // Declare variables outside try block for access in catch
  let tokenReservationId: string | null = null;

  try {
    // Parse query parameters
    const url = new URL(request.url);
    const conversationId = url.searchParams.get('conversationId');
    const assistantMessageId = url.searchParams.get('assistantMessageId');
    const model = url.searchParams.get('model');
    const isTestMode = url.searchParams.get('isTestMode') === 'true';
    const useWebSearch = url.searchParams.get('useWebSearch') === 'true';
    const useImageGeneration =
      url.searchParams.get('useImageGeneration') === 'true';
    const workspaceId = url.searchParams.get('workspaceId');
    tokenReservationId = url.searchParams.get('tokenReservationId');

    if (!conversationId || !assistantMessageId || !model) {
      return new Response(createErrorEvent('Missing required parameters'), {
        status: 400,
        headers: {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache, no-transform',
          Connection: 'keep-alive',
        },
      });
    }

    // Initialize database and LLM services
    const supabase = await createClient();
    const db = DatabaseService.getInstance(supabase);
    const llm = LLMService.getInstance();

    // Parallelize independent database calls for better performance
    const [user, conversation, llmModel] = await Promise.all([
      db.getCurrentUser(),
      db.getConversation(conversationId),
      getCachedModel(db, model),
    ]);

    if (!user) {
      return new Response(createErrorEvent('Unauthorized'), {
        status: 401,
        headers: {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache, no-transform',
          Connection: 'keep-alive',
        },
      });
    }

    if (!conversation || conversation.user_id !== user.id) {
      return new Response(
        createErrorEvent('Conversation not found or unauthorized'),
        {
          status: 404,
          headers: {
            'Content-Type': 'text/event-stream',
            'Cache-Control': 'no-cache, no-transform',
            Connection: 'keep-alive',
          },
        }
      );
    }

    if (!llmModel) {
      return new Response(createErrorEvent(`Invalid model: ${model}`), {
        status: 400,
        headers: {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache, no-transform',
          Connection: 'keep-alive',
        },
      });
    }

    // Now run dependent checks in parallel
    const [canAccessModel, hasWebSearchAccess] = await Promise.all([
      canUserAccessModel(user.id, llmModel.id),
      hasUserAccessToFeature(user.id, 'web_search'),
    ]);

    if (!canAccessModel) {
      return new Response(
        createErrorEvent('This model requires a premium subscription'),
        {
          status: 403,
          headers: {
            'Content-Type': 'text/event-stream',
            'Cache-Control': 'no-cache, no-transform',
            Connection: 'keep-alive',
          },
        }
      );
    }

    if (useWebSearch && !hasWebSearchAccess) {
      return new Response(
        createErrorEvent(
          'Web search is not supported by your current subscription plan.'
        ),
        {
          status: 400,
          headers: {
            'Content-Type': 'text/event-stream',
            'Cache-Control': 'no-cache, no-transform',
            Connection: 'keep-alive',
          },
        }
      );
    }

    // Parallelize message and workspace fetching
    const [assistantMessage, workspace] = await Promise.all([
      db.getMessage(assistantMessageId),
      workspaceId ? db.getWorkspace(workspaceId) : Promise.resolve(null),
    ]);

    if (!assistantMessage) {
      return new Response(createErrorEvent('Assistant message not found'), {
        status: 404,
        headers: {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache, no-transform',
          Connection: 'keep-alive',
        },
      });
    }

    // Get the user message (parent of the assistant message)
    const userMessageId = assistantMessage.parent_message_id;
    if (!userMessageId) {
      return new Response(createErrorEvent('Invalid message structure'), {
        status: 400,
        headers: {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache, no-transform',
          Connection: 'keep-alive',
        },
      });
    }

    const userMessage = await db.getMessage(userMessageId);
    if (!userMessage) {
      return new Response(createErrorEvent('User message not found'), {
        status: 404,
        headers: {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache, no-transform',
          Connection: 'keep-alive',
        },
      });
    }

    if (workspaceId && !workspace) {
      return new Response(createErrorEvent('Workspace not found'), {
        status: 404,
      });
    }
    // Parallelize workspace context and message history building for better performance
    const [contextMessages, baseHistory] = await Promise.all([
      workspace
        ? (async () => {
            const userContent =
              typeof userMessage.content === 'string'
                ? userMessage.content
                : JSON.stringify(userMessage.content);
            return await WorkspaceContextService.buildContext(
              supabase,
              workspace,
              userContent
            );
          })()
        : Promise.resolve([]),
      buildMessageHistory(db, conversation.id, userMessageId),
    ]);

    // Combine context messages with conversation history
    const messageHistory = [...contextMessages, ...baseHistory];

    // Stream the response with injected context
    return streamSSEResponse({
      llm,
      db,
      conversation,
      messageHistory,
      llmModel,
      user,
      userMessage,
      assistantMessageId,
      isTestMode,
      useWebSearch,
      useImageGeneration,
      supabase,
      tokenReservationId,
      request,
    });
  } catch (error) {
    log.error({ err: error }, 'Error processing SSE request');

    // Cancel any pending token reservation (no tokens were charged yet)
    try {
      if (tokenReservationId) {
        const quotaService = QuotaService.getInstance();
        await quotaService.cancelTokenReservation(tokenReservationId);
        log.info('Token reservation cancelled due to LLM failure', {
          reservationId: tokenReservationId,
        });
      }
    } catch (cancelError) {
      log.error('Failed to cancel token reservation after LLM failure', {
        cancelError,
        originalError: error,
      });
      // Swallow error – cancellation failure shouldn't affect response
    }

    const appError = error instanceof AppError ? error : handleError(error);
    return new Response(createErrorEvent(appError.message), {
      status: appError.statusCode || 500,
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache, no-transform',
        Connection: 'keep-alive',
      },
    });
  }
}

async function streamSSEResponse({
  llm,
  db,
  conversation,
  messageHistory,
  llmModel,
  user,
  userMessage,
  assistantMessageId,
  isTestMode,
  useWebSearch,
  useImageGeneration,
  supabase,
  tokenReservationId,
  request,
}: {
  llm: LLMService;
  db: DatabaseService;
  conversation: Conversation;
  messageHistory: LLMMessage[];
  llmModel: Model;
  user: User;
  userMessage: Message;
  assistantMessageId: string;
  isTestMode: boolean;
  useWebSearch?: boolean;
  useImageGeneration?: boolean;
  supabase: SupabaseClient;
  tokenReservationId?: string | null;
  request: Request;
}) {
  const log = logger.child({ conversationId: conversation.id });
  let assistantContent = '';
  const assistantUrlAnnotations: UrlCitationAnnotation[] = [];
  const assistantFileAnnotations: unknown[] = [];
  let buffer = '';
  // Start IDs at current epoch millis so that it can request "events since X" without bumping into duplicate IDs across
  // separate SSE connections for the same conversation.
  let eventId = Date.now();
  let latestStreamUsage: StreamReceivedUsage | undefined = undefined;
  const messageMetadata: Omit<StreamMetadataClientData, 'usage'> = {};

  const providerNameLower = llmModel.provider?.name?.toLowerCase();
  if (!providerNameLower) {
    throw new AppError(
      `Provider not found for model: ${llmModel.name}`,
      ErrorCode.INVALID_REQUEST
    );
  }

  const isProviderJsonStream = true;

  if (useWebSearch && !llmModel.allows_search) {
    throw new AppError(
      `Web search is not supported by the selected model (${llmModel.display_name}).`,
      ErrorCode.INVALID_REQUEST,
      400
    );
  }

  // --- Handle client disconnect / back-pressure ----------------------------------
  // Declare the stream variable up-front so that it is always defined (avoids TDZ)
  // It will be assigned after the LLM client returns. Initializing to `null`
  // ensures we stay out of the TDZ while still allowing strict null checks.
  let stream: ReadableStream<Uint8Array> | null = null;
  if (request.signal) {
    const quotaService = QuotaService.getInstance();
    const abortHandler = async () => {
      log.info('Client aborted connection – cancelling LLM stream');
      // Only attempt to cancel if the stream has been created
      if (stream?.cancel) {
        try {
          stream.cancel('client aborted');
        } catch (e) {
          log.warn('Failed to cancel upstream stream', e);
        }
      }

      if (tokenReservationId) {
        try {
          await quotaService.cancelTokenReservation(tokenReservationId);
          log.info('Token reservation cancelled after client abort', {
            reservationId: tokenReservationId,
          });
        } catch (cancelErr) {
          log.error(
            'Failed to cancel token reservation after abort',
            cancelErr
          );
        }
      }
    };

    // If already aborted (rare but possible)
    if (request.signal.aborted) void abortHandler();
    else request.signal.addEventListener('abort', abortHandler, { once: true });
  }

  // Now create the LLM stream (assign to the previously declared variable)
  stream = await llm.generateStreamingCompletion(
    providerNameLower,
    messageHistory,
    {
      model: llmModel,
      useWebSearch: useWebSearch,
      useImageGeneration: useImageGeneration,
    },
    isTestMode
  );

  // Track image generation promise (if any)
  let imageGenerationPromise: Promise<void> | null = null;

  // Create a transform stream for SSE events
  const transformStream = new TransformStream({
    async start(controller) {
      // Send a retry directive and keep-alive comment to start
      controller.enqueue(sseEncoder.encode('retry: 10000\n\n:keepalive\n\n'));

      // Send a dummy delta to initialize the connection
      eventId++;
      // mark conversation at streaming
      // controller.enqueue(
      //   sseEncoder.encode(
      //     `event: delta\nid: ${eventId}\ndata: ${JSON.stringify({
      //       type: 'delta',
      //       content: '',
      //     })}\n\n`
      //   )
      // );
    },

    transform(chunk, controller) {
      if (isProviderJsonStream) {
        const decodedChunk = sseDecoder.decode(chunk, { stream: true });
        buffer += decodedChunk;
        let newlineIndex;

        while ((newlineIndex = buffer.indexOf('\n')) >= 0) {
          const line = buffer.slice(0, newlineIndex).trim();
          buffer = buffer.slice(newlineIndex + 1);

          if (line && line !== '[DONE]') {
            try {
              const parsed = JSON.parse(line);

              if (parsed.type === 'delta' && parsed.content) {
                eventId++;
                assistantContent += parsed.content;
                controller.enqueue(
                  sseEncoder.encode(
                    `event: delta\nid: ${eventId}\ndata: ${JSON.stringify({
                      type: 'delta',
                      content: parsed.content,
                    })}\n\n`
                  )
                );
              } else if (
                parsed.type === 'tool' &&
                parsed.name === 'generate_image'
              ) {
                // Handle image generation tool call
                const { prompt, size } = parsed.args;

                eventId++;
                // Kick off image generation and store promise so we can await in flush
                const imageHandler = ImageHandler.getInstance(supabase);

                imageGenerationPromise = imageHandler
                  .handleImageToolCall(
                    user,
                    assistantMessageId,
                    prompt,
                    size || '768x768',
                    (deltaData) => {
                      try {
                        eventId++;
                        assistantContent = deltaData.content;
                        controller.enqueue(
                          sseEncoder.encode(
                            `event: delta\nid: ${eventId}\ndata: ${JSON.stringify(
                              {
                                type: 'delta',
                                content: deltaData.content,
                                replace: true,
                                attachments: deltaData.attachments,
                              }
                            )}\n\n`
                          )
                        );
                        log.info(
                          'Successfully streamed image result to client'
                        );
                      } catch (enqueueError) {
                        log.warn(
                          'Failed to enqueue image result (stream closed):',
                          enqueueError
                        );
                      }
                    }
                  )
                  .then(() => {
                    /* no-op – handled above */
                  })
                  .catch((error) => {
                    log.error(
                      'Error handling image tool call in stream',
                      error
                    );

                    // Send error message
                    try {
                      eventId++;
                      assistantContent =
                        'Sorry, an error occurred while generating the image.';
                      controller.enqueue(
                        sseEncoder.encode(
                          `event: delta\nid: ${eventId}\ndata: ${JSON.stringify(
                            {
                              type: 'delta',
                              content: assistantContent,
                              replace: true,
                              attachments: null,
                            }
                          )}\n\n`
                        )
                      );
                    } catch (enqueueError) {
                      log.warn(
                        'Failed to enqueue error message:',
                        enqueueError
                      );
                    }
                  });
              } else if (parsed.type === 'annotation' && parsed.annotation) {
                if (parsed.annotation.type === 'url_citation') {
                  eventId++;
                  assistantUrlAnnotations.push(parsed.annotation);
                  controller.enqueue(
                    sseEncoder.encode(
                      `event: annotation\nid: ${eventId}\ndata: ${JSON.stringify(
                        { type: 'annotation', annotation: parsed.annotation }
                      )}\n\n`
                    )
                  );
                } else {
                  assistantFileAnnotations.push(parsed.annotation);
                }
              } else if (parsed.type === 'error') {
                eventId++;
                log.error('Received error event from stream:', parsed.error);
                controller.enqueue(
                  sseEncoder.encode(
                    `event: error\nid: ${eventId}\ndata: ${JSON.stringify({
                      type: 'error',
                      error: parsed.error,
                    })}\n\n`
                  )
                );
              } else if (parsed.type === 'metadata' && parsed.data) {
                // Handle new metadata event
                const metadata = parsed.data as StreamMetadataClientData;

                // Store latest usage if present
                if (metadata.usage) {
                  latestStreamUsage = metadata.usage;
                }

                // Store one-time info for logging if not already captured
                if (Object.keys(messageMetadata).length === 0) {
                  if (metadata.provider)
                    messageMetadata.provider = metadata.provider;
                  if (metadata.model) messageMetadata.model = metadata.model;
                  if (metadata.id) messageMetadata.id = metadata.id;
                  if (metadata.object_type)
                    messageMetadata.object_type = metadata.object_type;
                  if (metadata.created_timestamp)
                    messageMetadata.created_timestamp =
                      metadata.created_timestamp;

                  // send metadata to the client
                  controller.enqueue(
                    sseEncoder.encode(
                      `event: metadata\nid: ${eventId}\ndata: ${JSON.stringify({
                        type: 'metadata',
                        metadata: {
                          version: ASSISTANT_MESSAGE_METADATA_VERSION,
                          data: metadata,
                        },
                      })}\n\n`
                    )
                  );
                }
              }
            } catch (e) {
              log.error('Error parsing JSON chunk in SSE transform:', e, line);
            }
          }

          if (buffer.length > 1_000_000) {
            log.error('JSON buffer exceeded 1 MB, aborting');
            stream?.cancel('Buffer overflow');
            controller.error(new Error('Stream buffer overflow'));
            return;
          }
        }
      } else {
        // For plain text streams, convert to delta events
        const decoded = sseDecoder.decode(chunk, { stream: true });
        assistantContent += decoded;
        eventId++;
        controller.enqueue(
          sseEncoder.encode(
            `event: delta\nid: ${eventId}\ndata: ${JSON.stringify({
              type: 'delta',
              content: decoded,
            })}\n\n`
          )
        );
      }
    },

    async flush(controller) {
      // Await image generation if it was triggered
      if (imageGenerationPromise) {
        try {
          await Promise.race([
            imageGenerationPromise,
            new Promise((resolve) => setTimeout(resolve, 30_000)), // 30-sec cap
          ]);
        } catch (imgErr) {
          log.warn('Image generation promise rejected or timed out', imgErr);
        }
      }
      try {
        if (isProviderJsonStream && buffer.trim()) {
          const line = buffer.trim();
          if (line && line !== '[DONE]') {
            try {
              const parsed = JSON.parse(line);
              if (parsed.type === 'delta' && parsed.content) {
                assistantContent += parsed.content;
              } else if (parsed.type === 'annotation' && parsed.annotation) {
                if (parsed.annotation.type === 'url_citation') {
                  assistantUrlAnnotations.push(parsed.annotation);
                } else {
                  assistantFileAnnotations.push(parsed.annotation);
                }
              }
            } catch (e) {
              log.error(
                'Error parsing final JSON chunk in SSE transform flush:',
                e,
                line
              );
            }
          }
        }

        // Combine collected stream metadata for the message
        const rawAssistantMetadata: AssistantMessageCustomMetadata = {
          ...messageMetadata,
        };
        if (latestStreamUsage) {
          rawAssistantMetadata.usage = latestStreamUsage;
        }

        let finalMetadataForDb: VersionedAssistantMessageMetadata | null = null;
        if (Object.keys(rawAssistantMetadata).length > 0 || latestStreamUsage) {
          finalMetadataForDb = {
            version: ASSISTANT_MESSAGE_METADATA_VERSION,
            data: rawAssistantMetadata,
          };
        }

        // Check if message was already updated by image handler
        const currentMessage = await db.getMessage(assistantMessageId);
        const hasImageAttachment =
          currentMessage?.attachments &&
          Array.isArray(currentMessage.attachments) &&
          currentMessage.attachments.some(
            (att: unknown) =>
              typeof att === 'object' &&
              att !== null &&
              (att as { type?: string }).type === 'image_url'
          );

        // Only update if not already updated by image handler
        if (!hasImageAttachment) {
          // Build DB update payload, ensuring we never set metadata to null (column is NOT NULL)
          const messageUpdates: Partial<Message> = {
            content: assistantContent.trim(),
            annotations:
              assistantUrlAnnotations.length > 0
                ? assistantUrlAnnotations
                : null,
            file_annotations:
              assistantFileAnnotations.length > 0
                ? assistantFileAnnotations
                : null,
          };

          if (finalMetadataForDb) {
            messageUpdates.metadata = finalMetadataForDb;
          }

          await db.updateMessage(assistantMessageId, messageUpdates);
        } else {
          // Only update metadata for image messages if we actually have metadata to save
          if (finalMetadataForDb) {
            await db.updateMessage(assistantMessageId, {
              metadata: finalMetadataForDb,
            });
          }
        }
        // mark conversation as done

        // Track usage, passing token counts if available from stream
        await trackUsage(
          db,
          user,
          llmModel,
          conversation,
          typeof userMessage.content === 'string'
            ? userMessage.content
            : '[User message with attachments]', // This prompt text is for tokenizer fallback
          assistantContent,
          assistantMessageId,
          messageHistory.length,
          tokenReservationId,
          latestStreamUsage?.prompt_tokens,
          latestStreamUsage?.completion_tokens
        );

        // Send done event
        eventId++;
        controller.enqueue(
          sseEncoder.encode(`event: done\nid: ${eventId}\ndata: {}\n\n`)
        );
      } catch (error) {
        log.error(
          { err: error },
          'Error during stream flush (DB save or tracking)'
        );

        // Note: Don't refund tokens here since this error happens AFTER
        // successful LLM completion. The failure is in DB save/tracking,
        // not in the LLM request itself. Tokens were legitimately used.

        eventId++;
        controller.enqueue(
          sseEncoder.encode(
            `event: error\nid: ${eventId}\ndata: ${JSON.stringify({
              type: 'error',
              error: { message: 'Failed to save response to database' },
            })}\n\n`
          )
        );
      }
    },
  });

  return new Response(stream!.pipeThrough(transformStream), {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache, no-transform',
      Connection: 'keep-alive',
      'X-Accel-Buffering': 'no', // Prevents proxy buffering
    },
  });
}
