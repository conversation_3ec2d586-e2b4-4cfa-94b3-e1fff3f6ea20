import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { DatabaseService, Conversation, Message } from '@/lib/supabase/db';
import { logger } from '@/lib/logger';
import { AppError, ErrorCode, handleError } from '@/lib/error';
import {
  canUserSendMessage,
  canUserAccessModel,
  incrementUserDailyMessageCount,
} from '@/lib/supabase/subscription';
import { QuotaService } from '@/services/quota';
import {
  TokenizerService,
  SupportedModel,
} from '@/services/tokenizer/tokenizer';
import { AttachmentMetadata, GroupConversation } from '@/lib/supabase/types';
import { LLMService } from '@/services/llm/service';
import { getCachedModel } from '@/services/cache/modelCache';

// Model caching is now centralized – see `src/services/cache/modelCache.ts`.

interface ChatRequestPayload {
  conversationId?: string;
  message: string;
  provider: string;
  model: string;
  groupConversationId?: string;
  comparisonIndex?: number;
  isComparison?: boolean;
  isTestMode: boolean;
  parentMessageId?: string;
  attachments?: Array<AttachmentMetadata>;
  useWebSearch?: boolean;
  useImageGeneration?: boolean;
  isTemporary?: boolean;
  messageId?: string;
  newContent?: string;
  isEdit?: boolean;
  workspaceId?: string;
}

function createErrorResponse(message: string, status = 400) {
  return NextResponse.json({ error: message }, { status });
}

async function generateGroupConversationTitle(
  db: DatabaseService,
  groupConversationId: string,
  message: string,
  isTestMode: boolean
) {
  const log = logger.child({ groupConversationId, message, isTestMode });
  try {
    const llm = LLMService.getInstance();
    const groupConversation =
      await db.getGroupConversation(groupConversationId);
    if (!groupConversation) {
      log.warn('Group conversation not found');
      return;
    }

    const title = await llm.generateGroupConversationTitle(message, isTestMode);

    if (title) {
      db.updateGroupConversation(groupConversationId, {
        title,
      });
    }
  } catch (error) {
    log.error({ err: error }, 'Error generating group conversation title');
  }
}

async function manageConversation(
  db: DatabaseService,
  {
    conversationId,
    userId,
    llmModel,
    groupConversationId,
    comparisonIndex,
    isComparison,
    isTemporary,
    workspaceId,
  }: {
    conversationId?: string;
    userId: string;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    llmModel: any; // Already fetched model object
    groupConversationId?: string;
    comparisonIndex?: number | null;
    isComparison?: boolean;
    isTemporary?: boolean;
    workspaceId?: string;
  }
): Promise<Conversation | null> {
  // Model is already validated and passed in

  if (conversationId) {
    // Existing conversation
    const conversation = await db.getConversation(conversationId);
    if (!conversation || conversation.user_id !== userId) {
      return null; // Unauthorized or not found
    }

    // Update the last model used if it changed
    if (conversation.last_model_used !== llmModel.id) {
      await db.updateConversation(conversationId, {
        last_model_used: llmModel.id,
      });
    }

    return conversation;
  } else {
    // New conversation
    const newGroupId = groupConversationId || crypto.randomUUID();
    let groupConversation: GroupConversation | null = null;

    // Only create/update a group conversation if needed
    if (!groupConversationId) {
      // Brand new conversation, create a new group
      groupConversation = await db.upsertGroupConversation(userId, {
        id: newGroupId,
        title: 'New Conversation',
        is_comparison: isComparison || false,
        is_temporary: isTemporary || false,
        workspace_id: workspaceId,
      });
    } else {
      // Using an existing group ID, verify it exists and belongs to user
      groupConversation = await db.getGroupConversation(groupConversationId);
      if (!groupConversation || groupConversation.user_id !== userId) {
        // If it doesn't exist or doesn't belong to user, create it
        groupConversation = await db.upsertGroupConversation(userId, {
          id: newGroupId,
          title: 'New Conversation',
          is_comparison: isComparison || false,
          is_temporary: isTemporary || false,
          workspace_id: workspaceId,
        });
      }
    }

    // Create the conversation
    return db.createConversation(userId, {
      group_conversation_id: groupConversation?.id || newGroupId,
      last_model_used: llmModel.id,
      comparison_index: comparisonIndex !== undefined ? comparisonIndex : null,
    });
  }
}

// Common code for both POST and PUT handlers
async function handleStreamInit(
  req: Request,
  isUpdate: boolean = false
): Promise<Response> {
  const requestId = crypto.randomUUID();
  const log = logger.child({ requestId, update: isUpdate });

  // Track quota-related side-effects so we can roll them back on failure
  let tokenReservationId: string | null = null;
  let comparisonIncremented = false;
  let currentUserId: string | null = null;

  try {
    const supabase = await createClient();
    const db = DatabaseService.getInstance(supabase);

    const payload: ChatRequestPayload = await req.json();

    if (isUpdate) {
      // Update (edit/retry) path
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { messageId, model, isTestMode, newContent, isEdit, useWebSearch } =
        payload;

      if (!messageId) {
        log.warn('Message ID is required');
        return createErrorResponse('Message ID is required', 400);
      }

      const user = await db.getCurrentUser();
      // Capture user ID for potential quota rollback in case of errors later
      currentUserId = user?.id || null;
      if (!user) {
        log.warn('Unauthorized access attempt');
        return createErrorResponse('Unauthorized', 401);
      }

      const llmModel = await getCachedModel(db, model);
      if (!llmModel) {
        throw new AppError(
          `Invalid model: ${model}`,
          ErrorCode.INVALID_REQUEST,
          400
        );
      }

      const canAccessModel = await canUserAccessModel(user.id, llmModel.id);
      if (!canAccessModel) {
        throw new AppError(
          'This model requires a premium subscription',
          ErrorCode.API_UNAUTHORIZED,
          403
        );
      }

      if (useWebSearch && !llmModel.allows_search) {
        throw new AppError(
          `Web search is not supported by the selected model (${llmModel.display_name}).`,
          ErrorCode.INVALID_REQUEST,
          400
        );
      }

      const message = await db.getMessage(messageId);
      if (!message) {
        log.warn('Message not found');
        return createErrorResponse('Message not found', 404);
      }

      const conversationId = message.conversation_id;
      if (!conversationId) {
        return createErrorResponse('Invalid message structure', 400);
      }

      // Update model if needed
      if (llmModel.id !== message.conversation?.last_model_used) {
        await db.updateConversation(conversationId, {
          last_model_used: llmModel.id,
        });
      }

      let userMessage: Message;

      // For edits, create a new user message with the updated content
      if (isEdit && newContent) {
        userMessage = await db.createMessage({
          conversationId: conversationId,
          content: newContent,
          role: 'user',
          parentMessageId: message.parent_message_id as string,
          modelId: llmModel.id,
          providerId: llmModel.provider_id as string,
        });
      } else {
        // For retries, use the existing message
        userMessage = message;
      }

      // Create placeholder assistant message
      const assistantMessageId = crypto.randomUUID();
      await db.createMessage({
        id: assistantMessageId,
        conversationId: conversationId,
        content: '', // Empty content initially
        role: 'assistant',
        parentMessageId: userMessage.id,
        modelId: llmModel.id,
        providerId: llmModel.provider_id as string,
      });

      return NextResponse.json({
        conversationId,
        assistantMessageId,
        userMessageId: userMessage.id,
      });
    } else {
      // New message path (original POST implementation)
      const {
        conversationId,
        message,
        model,
        groupConversationId,
        comparisonIndex,
        isComparison,
        isTestMode,
        parentMessageId,
        attachments,
        useWebSearch,
        isTemporary,
        workspaceId,
      } = payload;

      const isNewConversation = !conversationId;

      // Parallelize independent database/quota calls for better performance
      const quotaService = QuotaService.getInstance();
      const [user, llmModel] = await Promise.all([
        db.getCurrentUser(),
        getCachedModel(db, model),
      ]);

      // Capture user ID for potential quota rollback in case of errors later
      currentUserId = user?.id || null;
      if (!user) {
        log.warn('Unauthorized access attempt');
        return createErrorResponse('Unauthorized', 401);
      }

      if (!llmModel) {
        throw new AppError(
          `Invalid model: ${model}`,
          ErrorCode.INVALID_REQUEST,
          400
        );
      }

      // Now run dependent checks in parallel
      const [canAccessModel, msgQuota, tokenQuota] = await Promise.all([
        canUserAccessModel(user.id, llmModel.id),
        canUserSendMessage(user.id),
        quotaService.checkTokenQuota(user.id),
      ]);

      if (!canAccessModel) {
        throw new AppError(
          'This model requires a premium subscription',
          ErrorCode.API_UNAUTHORIZED,
          403
        );
      }

      if (useWebSearch && !llmModel.allows_search) {
        throw new AppError(
          `Web search is not supported by the selected model (${llmModel.display_name}).`,
          ErrorCode.INVALID_REQUEST,
          400
        );
      }

      // Check quota limits (message limits for free tier, token limits for paid tiers)
      const { allowed, reason } = msgQuota;
      if (!allowed) {
        throw new AppError(
          reason || 'You have reached your daily message limit',
          ErrorCode.API_RESOURCE_EXHAUSTED,
          403
        );
      }

      if (tokenQuota.tokenQuota !== null && !tokenQuota.canUse) {
        // User is on a paid tier and over quota
        const fallback = await quotaService.handleTokenOverage(user.id);

        if (fallback.reason === 'quota_exceeded' && fallback.modelId) {
          // Redirect to fallback model for starter tier
          throw new AppError(
            `Token quota exceeded. Please use ${fallback.modelId} or upgrade your plan.`,
            ErrorCode.API_RESOURCE_EXHAUSTED,
            403
          );
        } else if (fallback.reason === 'overage_available') {
          // Premium tier - offer overage options
          throw new AppError(
            'Token quota exceeded. You can purchase additional tokens or upgrade your plan.',
            ErrorCode.API_RESOURCE_EXHAUSTED,
            403
          );
        } else {
          throw new AppError(
            'Token quota exceeded. Please upgrade your plan.',
            ErrorCode.API_RESOURCE_EXHAUSTED,
            403
          );
        }
      }

      // FIXED: Reserve tokens upfront (reserve-and-commit pattern)
      if (tokenQuota.tokenQuota !== null) {
        try {
          const tokenizerService = TokenizerService.getInstance();
          const estimatedTokens = await tokenizerService.countTokens(
            message,
            llmModel.name as SupportedModel
          );

          // Reserve tokens upfront to enable refunds on failure
          const reservation = await quotaService.reserveTokenUsage(
            user.id,
            estimatedTokens,
            llmModel.id
          );

          if (!reservation.success) {
            // Reservation failed - quota exceeded
            const fallback = await quotaService.handleTokenOverage(user.id);

            if (fallback.reason === 'quota_exceeded' && fallback.modelId) {
              throw new AppError(
                `This message would exceed your token quota. Please use ${fallback.modelId} or upgrade your plan.`,
                ErrorCode.API_RESOURCE_EXHAUSTED,
                403
              );
            } else if (fallback.reason === 'overage_available') {
              throw new AppError(
                'This message would exceed your token quota. You can purchase additional tokens or upgrade your plan.',
                ErrorCode.API_RESOURCE_EXHAUSTED,
                403
              );
            } else {
              throw new AppError(
                'Token quota exceeded. Please upgrade your plan.',
                ErrorCode.API_RESOURCE_EXHAUSTED,
                403
              );
            }
          }

          tokenReservationId = reservation.reservationId;

          // Store reservation ID in headers for the streaming endpoint
          // This allows proper cleanup if streaming fails
          log.info('Tokens reserved for chat request', {
            userId: user.id,
            estimatedTokens,
            reservationId: tokenReservationId,
          });
        } catch (tokenError) {
          log.warn('Failed to estimate and reserve tokens', {
            error: tokenError,
          });
          // Continue if token estimation fails, but without reservation
        }
      }

      // FIXED: Atomic comparison quota check and increment (prevents race conditions)
      if (isComparison) {
        // incrementComparisonUsage now atomically checks and increments
        // This eliminates the race condition between check and increment
        await quotaService.incrementComparisonUsage(user.id);
        comparisonIncremented = true;
      }

      // Validate group conversation ID if provided
      let validatedGroupConversationId = groupConversationId;
      if (groupConversationId) {
        const existingGroup =
          await db.getGroupConversation(groupConversationId);
        if (!existingGroup || existingGroup.user_id !== user.id) {
          // If group doesn't exist or doesn't belong to user, generate a new one
          validatedGroupConversationId = undefined;
        }
      }

      const conversation = await manageConversation(db, {
        conversationId,
        userId: user.id,
        llmModel,
        groupConversationId: validatedGroupConversationId,
        comparisonIndex,
        isComparison,
        isTemporary,
        workspaceId,
      });

      if (isNewConversation && conversation?.group_conversation_id) {
        generateGroupConversationTitle(
          db,
          conversation.group_conversation_id,
          message,
          isTestMode
        );
      }

      if (!conversation) {
        log.warn('Conversation not found or unauthorized');
        return createErrorResponse(
          'Conversation not found or unauthorized',
          404
        );
      }

      // Create user & assistant placeholder messages in **one** DB round-trip
      const userMessageId = crypto.randomUUID();
      const assistantMessageId = crypto.randomUUID();

      const createdMessages = await db.createMessages([
        {
          id: userMessageId,
          conversationId: conversation.id,
          content: message,
          role: 'user',
          parentMessageId,
          attachments,
        },
        {
          id: assistantMessageId,
          conversationId: conversation.id,
          content: '', // Empty content initially
          role: 'assistant',
          parentMessageId: userMessageId,
          modelId: llmModel.id,
          providerId: llmModel.provider_id as string,
        },
      ]);

      // Grab the userMessage object for later use
      const userMessage = createdMessages.find((m) => m.id === userMessageId)!;

      // Get the group conversation to check if it's temporary
      const groupConversation = await db.getGroupConversation(
        conversation.group_conversation_id
      );
      const isTemp = groupConversation?.is_temporary || false;

      const response = NextResponse.json({
        conversationId: conversation.id,
        groupConversationId: conversation.group_conversation_id,
        assistantMessageId,
        userMessageId: userMessage.id,
        isNewConversation,
        isTemporary: isTemp,
        tokenReservationId: tokenReservationId, // Pass reservation ID to stream route
      });

      // Defer non-critical quota writes after response
      void incrementUserDailyMessageCount(user.id);

      return response;
    }
  } catch (error) {
    // Roll back any quota-side effects if they were applied
    try {
      const quotaService = QuotaService.getInstance();

      if (tokenReservationId) {
        await quotaService.cancelTokenReservation(tokenReservationId);
      }

      if (comparisonIncremented && currentUserId) {
        await quotaService.decrementComparisonUsage(currentUserId);
      }
    } catch (cleanupError) {
      // Log cleanup failures but do not override the original error response
      log.error(
        { cleanupError },
        'Failed to roll back quota changes after chat init failure'
      );
    }

    log.error({ err: error }, 'Error processing chat request');
    const appError = error instanceof AppError ? error : handleError(error);
    return createErrorResponse(appError.message, appError.statusCode);
  }
}

export async function POST(req: Request): Promise<Response> {
  return handleStreamInit(req, false);
}

export async function PUT(req: Request): Promise<Response> {
  return handleStreamInit(req, true);
}
