import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { logger } from '@/lib/logger';
import {
  addHTTPAttributes,
  addDatabaseAttributes,
  getTracer
} from '@/lib/otel';

const log = logger.child({ api: '/api/health' });

interface HealthCheckResult {
  status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: string;
  uptime: number;
  version: string;
  environment: string;
  checks: {
    database: {
      status: 'healthy' | 'unhealthy';
      latency?: number;
      error?: string;
    };
    telemetry: {
      status: 'healthy' | 'unhealthy';
      otel_configured: boolean;
      sentry_configured: boolean;
    };
    memory: {
      status: 'healthy' | 'unhealthy' | 'degraded';
      usage_mb: number;
      usage_percent: number;
    };
    llm_services: {
      status: 'healthy' | 'unhealthy' | 'degraded';
      available_providers: string[];
    };
  };
}

const MEMORY_WARNING_THRESHOLD = 500; // MB
const MEMORY_CRITICAL_THRESHOLD = 700; // MB

async function checkDatabase(): Promise<
  HealthCheckResult['checks']['database']
> {
  const tracer = getTracer('health-check');
  const span = tracer.startSpan('health_check_database');

  try {
    const startTime = performance.now();
    const supabase = await createClient();

    // Simple query to check database connectivity
    const { error } = await supabase
      .from('llm_models')
      .select('id')
      .limit(1)
      .single();

    const latency = performance.now() - startTime;

    addDatabaseAttributes(span, 'SELECT', 'llm_models', 'health_check');
    span.setAttribute('db.health_check.latency_ms', latency);

    if (error) {
      span.recordException(error);
      return {
        status: 'unhealthy',
        latency,
        error: error.message,
      };
    }

    span.setAttribute('db.health_check.success', true);
    return {
      status: 'healthy',
      latency,
    };
  } catch (error) {
    span.recordException(error as Error);
    return {
      status: 'unhealthy',
      error: (error as Error).message,
    };
  } finally {
    span.end();
  }
}

function checkTelemetry(): HealthCheckResult['checks']['telemetry'] {
  const tracer = getTracer('health-check');
  const span = tracer.startSpan('health_check_telemetry');

  try {
    const otel_configured = !!(
      process.env.HONEYCOMB_API_KEY ||
      process.env.DATADOG_API_KEY ||
      process.env.OTEL_EXPORTER_OTLP_ENDPOINT
    );

    const sentry_configured = !!(
      process.env.SENTRY_DSN || process.env.NEXT_PUBLIC_SENTRY_DSN
    );

    span.setAttributes({
      'telemetry.otel_configured': otel_configured,
      'telemetry.sentry_configured': sentry_configured,
    });

    return {
      status: otel_configured && sentry_configured ? 'healthy' : 'unhealthy',
      otel_configured,
      sentry_configured,
    };
  } finally {
    span.end();
  }
}

function checkMemory(): HealthCheckResult['checks']['memory'] {
  const tracer = getTracer('health-check');
  const span = tracer.startSpan('health_check_memory');

  try {
    const memoryUsage = process.memoryUsage();
    const usage_mb = Math.round(memoryUsage.rss / 1024 / 1024);
    const heap_mb = Math.round(memoryUsage.heapUsed / 1024 / 1024);
    const usage_percent = Math.round(
      (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100
    );

    let status: 'healthy' | 'unhealthy' | 'degraded';
    if (usage_mb > MEMORY_CRITICAL_THRESHOLD) {
      status = 'unhealthy';
    } else if (usage_mb > MEMORY_WARNING_THRESHOLD) {
      status = 'degraded';
    } else {
      status = 'healthy';
    }

    span.setAttributes({
      'memory.usage_mb': usage_mb,
      'memory.heap_mb': heap_mb,
      'memory.usage_percent': usage_percent,
      'memory.status': status,
    });

    return {
      status,
      usage_mb,
      usage_percent,
    };
  } finally {
    span.end();
  }
}

function checkLLMServices(): HealthCheckResult['checks']['llm_services'] {
  const tracer = getTracer('health-check');
  const span = tracer.startSpan('health_check_llm_services');

  try {
    const available_providers = [];

    if (process.env.OPENAI_API_KEY) {
      available_providers.push('openai');
    }
    if (process.env.ANTHROPIC_API_KEY) {
      available_providers.push('anthropic');
    }
    if (process.env.GEMINI_API_KEY) {
      available_providers.push('gemini');
    }
    if (process.env.DEEPSEEK_API_KEY) {
      available_providers.push('deepseek');
    }
    if (process.env.OPENROUTER_API_KEY) {
      available_providers.push('openrouter');
    }

    let status: 'healthy' | 'unhealthy' | 'degraded';
    if (available_providers.length === 0) {
      status = 'unhealthy';
    } else if (available_providers.length === 1) {
      status = 'degraded';
    } else {
      status = 'healthy';
    }

    span.setAttributes({
      'llm.available_providers': available_providers.join(','),
      'llm.provider_count': available_providers.length,
      'llm.status': status,
    });

    return {
      status,
      available_providers,
    };
  } finally {
    span.end();
  }
}

export async function GET(): Promise<Response> {
  const tracer = getTracer('health-check');
  const span = tracer.startSpan('health_check_endpoint');

  try {
    const startTime = performance.now();

    // Run all health checks
    const [databaseCheck, telemetryCheck, memoryCheck, llmServicesCheck] =
      await Promise.all([
        checkDatabase(),
        Promise.resolve(checkTelemetry()),
        Promise.resolve(checkMemory()),
        Promise.resolve(checkLLMServices()),
      ]);

    // Determine overall status
    const checks = [
      databaseCheck,
      telemetryCheck,
      memoryCheck,
      llmServicesCheck,
    ];
    const hasUnhealthy = checks.some((check) => check.status === 'unhealthy');
    const hasDegraded = checks.some((check) => check.status === 'degraded');

    let overall_status: 'healthy' | 'unhealthy' | 'degraded';
    let statusCode: number;

    if (hasUnhealthy) {
      overall_status = 'unhealthy';
      statusCode = 503; // Service Unavailable
    } else if (hasDegraded) {
      overall_status = 'degraded';
      statusCode = 200; // OK but with warnings
    } else {
      overall_status = 'healthy';
      statusCode = 200; // OK
    }

    const result: HealthCheckResult = {
      status: overall_status,
      timestamp: new Date().toISOString(),
      uptime: Math.floor(process.uptime()),
      version:
        process.env.APP_VERSION ||
        process.env.VERCEL_GIT_COMMIT_SHA ||
        'unknown',
      environment: process.env.NODE_ENV || 'unknown',
      checks: {
        database: databaseCheck,
        telemetry: telemetryCheck,
        memory: memoryCheck,
        llm_services: llmServicesCheck,
      },
    };

    // Add telemetry attributes
    const duration = performance.now() - startTime;
    span.setAttributes({
      'health_check.overall_status': overall_status,
      'health_check.duration_ms': duration,
      'health_check.database_status': databaseCheck.status,
      'health_check.telemetry_status': telemetryCheck.status,
      'health_check.memory_status': memoryCheck.status,
      'health_check.llm_services_status': llmServicesCheck.status,
    });

    addHTTPAttributes(span, 'GET', '/api/health', statusCode);

    log.info('Health check completed', {
      status: overall_status,
      duration: duration,
      checks: {
        database: databaseCheck.status,
        telemetry: telemetryCheck.status,
        memory: memoryCheck.status,
        llm_services: llmServicesCheck.status,
      },
    });

    return NextResponse.json(result, {
      status: statusCode,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Content-Type': 'application/json',
      },
    });
  } catch (error) {
    log.error({ err: error }, 'Health check failed');
    span.recordException(error as Error);
    span.setAttribute('health_check.error', true);

    const errorResult: HealthCheckResult = {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      uptime: Math.floor(process.uptime()),
      version: process.env.APP_VERSION || 'unknown',
      environment: process.env.NODE_ENV || 'unknown',
      checks: {
        database: { status: 'unhealthy', error: 'Health check failed' },
        telemetry: {
          status: 'unhealthy',
          otel_configured: false,
          sentry_configured: false,
        },
        memory: { status: 'unhealthy', usage_mb: 0, usage_percent: 0 },
        llm_services: { status: 'unhealthy', available_providers: [] },
      },
    };

    addHTTPAttributes(span, 'GET', '/api/health', 503);

    return NextResponse.json(errorResult, {
      status: 503,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Content-Type': 'application/json',
      },
    });
  } finally {
    span.end();
  }
}

// Simple health check for load balancers (no authentication required)
export async function HEAD(): Promise<Response> {
  const tracer = getTracer('health-check');
  const span = tracer.startSpan('health_check_head');

  try {
    // Basic connectivity check only
    const supabase = await createClient();
    const { error } = await supabase
      .from('llm_models')
      .select('id')
      .limit(1)
      .single();

    if (error) {
      span.recordException(error);
      addHTTPAttributes(span, 'HEAD', '/api/health', 503);
      return new Response(null, { status: 503 });
    }

    addHTTPAttributes(span, 'HEAD', '/api/health', 200);
    return new Response(null, { status: 200 });
  } catch (error) {
    span.recordException(error as Error);
    addHTTPAttributes(span, 'HEAD', '/api/health', 503);
    return new Response(null, { status: 503 });
  } finally {
    span.end();
  }
}
