import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { DatabaseService } from '@/lib/supabase/db';
import { AppError, handleError } from '@/lib/error';
import { logger } from '@/lib/logger';

const log = logger.child({ api: '/api/workspaces/[id]/notes' });

// GET /api/workspaces/[id]/notes - List all notes in a workspace
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const workspaceId = (await params).id;

    const supabase = await createClient();
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const db = DatabaseService.getInstance(supabase);

    // Get the workspace to check ownership
    const workspace = await db.getWorkspace(workspaceId);

    if (!workspace) {
      return NextResponse.json(
        { error: 'Workspace not found' },
        { status: 404 }
      );
    }

    // Check ownership
    if (workspace.owner_id !== user.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Get notes for the workspace
    const notes = await db.getWorkspaceNotes(workspaceId);

    return NextResponse.json({ notes });
  } catch (error) {
    log.error(
      { err: error, workspaceId: (await params).id },
      'Error fetching workspace notes'
    );
    const appError = error instanceof AppError ? error : handleError(error);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
}

// POST /api/workspaces/[id]/notes - Create a new note in a workspace
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const workspaceId = (await params).id;

    const supabase = await createClient();
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const db = DatabaseService.getInstance(supabase);

    // Get the workspace to check ownership
    const workspace = await db.getWorkspace(workspaceId);

    if (!workspace) {
      return NextResponse.json(
        { error: 'Workspace not found' },
        { status: 404 }
      );
    }

    // Check ownership
    if (workspace.owner_id !== user.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Parse request body
    const body = await request.json();
    const { title, body: noteBody } = body;

    if (!title || title.trim() === '') {
      return NextResponse.json(
        { error: 'Note title is required' },
        { status: 400 }
      );
    }

    if (!noteBody || noteBody.trim() === '') {
      return NextResponse.json(
        { error: 'Note body is required' },
        { status: 400 }
      );
    }

    if (title.length > 120) {
      return NextResponse.json(
        { error: 'Note title must be 120 characters or less' },
        { status: 400 }
      );
    }

    // Create the note
    const note = await db.createWorkspaceNote(workspaceId, {
      title,
      body: noteBody,
    });

    log.info({ userId: user.id, workspaceId, noteId: note.id }, 'Note created');

    return NextResponse.json({ note });
  } catch (error) {
    log.error({ err: error, workspaceId: (await params).id }, 'Error creating note');
    const appError = error instanceof AppError ? error : handleError(error);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
}

// PATCH /api/workspaces/[id]/notes - Update a note
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const workspaceId = (await params).id;

    const supabase = await createClient();
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const db = DatabaseService.getInstance(supabase);

    // Get the workspace to check ownership
    const workspace = await db.getWorkspace(workspaceId);

    if (!workspace) {
      return NextResponse.json(
        { error: 'Workspace not found' },
        { status: 404 }
      );
    }

    // Check ownership
    if (workspace.owner_id !== user.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Parse request body
    const body = await request.json();
    const { id: noteId, title, body: noteBody } = body;

    if (!noteId) {
      return NextResponse.json(
        { error: 'Note ID is required' },
        { status: 400 }
      );
    }

    // Validate title if provided
    if (title && title.length > 120) {
      return NextResponse.json(
        { error: 'Note title must be 120 characters or less' },
        { status: 400 }
      );
    }

    // Prepare updates
    const updates: Partial<{ title: string; body: string }> = {};
    if (title !== undefined) updates.title = title;
    if (noteBody !== undefined) updates.body = noteBody;

    // Update the note
    const updatedNote = await db.updateWorkspaceNote(noteId, updates);

    log.info({ userId: user.id, workspaceId, noteId }, 'Note updated');

    return NextResponse.json({ note: updatedNote });
  } catch (error) {
    log.error({ err: error, workspaceId: (await params).id }, 'Error updating note');
    const appError = error instanceof AppError ? error : handleError(error);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
}

// DELETE /api/workspaces/[id]/notes - Delete a note
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const workspaceId = (await params).id;
    const url = new URL(request.url);
    const noteId = url.searchParams.get('noteId');

    if (!noteId) {
      return NextResponse.json(
        { error: 'Note ID is required' },
        { status: 400 }
      );
    }

    const supabase = await createClient();
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const db = DatabaseService.getInstance(supabase);

    // Get the workspace to check ownership
    const workspace = await db.getWorkspace(workspaceId);

    if (!workspace) {
      return NextResponse.json(
        { error: 'Workspace not found' },
        { status: 404 }
      );
    }

    // Check ownership
    if (workspace.owner_id !== user.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Delete the note
    await db.deleteWorkspaceNote(noteId);

    log.info({ userId: user.id, workspaceId, noteId }, 'Note deleted');

    return NextResponse.json({ success: true });
  } catch (error) {
    log.error({ err: error, workspaceId: (await params).id }, 'Error deleting note');
    const appError = error instanceof AppError ? error : handleError(error);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
}
