import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { DatabaseService } from '@/lib/supabase/db';
import { AppError, handleError } from '@/lib/error';
import { logger } from '@/lib/logger';

const log = logger.child({ api: '/api/workspaces/[id]/favorite' });

// POST /api/workspaces/[id]/favorite - Toggle favorite status of a workspace
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const workspaceId = (await params).id;

    const supabase = await createClient();
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const db = DatabaseService.getInstance(supabase);

    // Get the workspace
    const workspace = await db.getWorkspace(workspaceId);

    if (!workspace) {
      return NextResponse.json(
        { error: 'Workspace not found' },
        { status: 404 }
      );
    }

    // Check ownership
    if (workspace.owner_id !== user.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Toggle the favorite status
    const isFavorite = !workspace.is_favorite;

    // Update the workspace
    const updatedWorkspace = await db.updateWorkspace(workspaceId, {
      is_favorite: isFavorite,
    });

    log.info(
      { userId: user.id, workspaceId, isFavorite },
      `Workspace ${isFavorite ? 'favorited' : 'unfavorited'}`
    );

    return NextResponse.json({
      success: true,
      workspace: updatedWorkspace,
      is_favorite: isFavorite
    });
  } catch (error) {
    log.error(
      { err: error, workspaceId: (await params).id },
      'Error toggling workspace favorite status'
    );
    const appError = error instanceof AppError ? error : handleError(error);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
}