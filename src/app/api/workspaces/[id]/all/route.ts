import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { DatabaseService } from '@/lib/supabase/db';
import { AppError, handleError } from '@/lib/error';
import { logger } from '@/lib/logger';

const log = logger.child({ api: '/api/workspaces/[id]/all' });

// GET /api/workspaces/[id]/all - Get a workspace with its files and notes
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const workspaceId = (await params).id;

    const supabase = await createClient();
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const db = DatabaseService.getInstance(supabase);

    // Get the workspace
    const workspace = await db.getWorkspace(workspaceId);

    if (!workspace) {
      return NextResponse.json(
        { error: 'Workspace not found' },
        { status: 404 }
      );
    }

    // Check ownership
    if (workspace.owner_id !== user.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Get files and notes in parallel
    const [files, notes] = await Promise.all([
      db.getWorkspaceFiles(workspaceId),
      db.getWorkspaceNotes(workspaceId)
    ]);

    return NextResponse.json({
      workspace,
      files,
      notes
    });
  } catch (error) {
    log.error({ err: error, workspaceId: (await params).id }, 'Error fetching workspace data');
    const appError = error instanceof AppError ? error : handleError(error);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
}