import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { DatabaseService } from '@/lib/supabase/db';
import { AppError, handleError } from '@/lib/error';
import { logger } from '@/lib/logger';

const log = logger.child({ api: '/api/workspaces/[id]' });

// GET /api/workspaces/[id] - Get a specific workspace
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const workspaceId = (await params).id;

    const supabase = await createClient();
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const db = DatabaseService.getInstance(supabase);

    // Get the workspace
    const workspace = await db.getWorkspace(workspaceId);

    if (!workspace) {
      return NextResponse.json(
        { error: 'Workspace not found' },
        { status: 404 }
      );
    }

    // Check ownership
    if (workspace.owner_id !== user.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    return NextResponse.json({ workspace });
  } catch (error) {
    log.error({ err: error, workspaceId: (await params).id }, 'Error fetching workspace');
    const appError = error instanceof AppError ? error : handleError(error);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
}

// PATCH /api/workspaces/[id] - Update a workspace
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const workspaceId = (await params).id;
    const body = await request.json();
    const { name, description, icon, default_model_id } = body;

    const supabase = await createClient();
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const db = DatabaseService.getInstance(supabase);

    // Get the workspace to check ownership
    const workspace = await db.getWorkspace(workspaceId);

    if (!workspace) {
      return NextResponse.json(
        { error: 'Workspace not found' },
        { status: 404 }
      );
    }

    // Check ownership
    if (workspace.owner_id !== user.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Validate input
    if (name && name.length > 80) {
      return NextResponse.json(
        { error: 'Workspace name must be 80 characters or less' },
        { status: 400 }
      );
    }

    // Update the workspace
    const updates: Partial<{
      name: string;
      description: string | null;
      icon: string | null;
      default_model_id: string | null;
    }> = {};
    if (name !== undefined) updates.name = name;
    if (description !== undefined) updates.description = description;
    if (icon !== undefined) updates.icon = icon;
    if (default_model_id !== undefined) updates.default_model_id = default_model_id;

    const updatedWorkspace = await db.updateWorkspace(workspaceId, updates);

    log.info({ userId: user.id, workspaceId }, 'Workspace updated');

    return NextResponse.json({ workspace: updatedWorkspace });
  } catch (error) {
    log.error({ err: error, workspaceId: (await params).id }, 'Error updating workspace');
    const appError = error instanceof AppError ? error : handleError(error);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
}

// DELETE /api/workspaces/[id] - Delete a workspace
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const workspaceId = (await params).id;

    const supabase = await createClient();
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const db = DatabaseService.getInstance(supabase);

    // Get the workspace to check ownership
    const workspace = await db.getWorkspace(workspaceId);

    if (!workspace) {
      return NextResponse.json(
        { error: 'Workspace not found' },
        { status: 404 }
      );
    }

    // Check ownership
    if (workspace.owner_id !== user.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Delete the workspace (this will also delete all related files and notes via cascade)
    await db.deleteWorkspace(workspaceId);

    log.info({ userId: user.id, workspaceId }, 'Workspace deleted');

    return NextResponse.json({ success: true });
  } catch (error) {
    log.error({ err: error, workspaceId: (await params).id }, 'Error deleting workspace');
    const appError = error instanceof AppError ? error : handleError(error);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
}
