import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { DatabaseService } from '@/lib/supabase/db';
import { AppError, ErrorCode, handleError } from '@/lib/error';
import { logger } from '@/lib/logger';
import { QuotaService } from '@/services/quota';
import {
  MAX_FILE_SIZE_BYTES,
  MAX_FILE_SIZE_MB,
  ALLOWED_FILE_TYPES,
} from '@/constants/attachment';

const log = logger.child({ api: '/api/workspaces/[id]/files' });
const STORAGE_BUCKET_NAME = 'attachments';

// GET /api/workspaces/[id]/files - List all files in a workspace
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const workspaceId = (await params).id;

    const supabase = await createClient();
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const db = DatabaseService.getInstance(supabase);

    // Get the workspace to check ownership
    const workspace = await db.getWorkspace(workspaceId);

    if (!workspace) {
      return NextResponse.json(
        { error: 'Workspace not found' },
        { status: 404 }
      );
    }

    // Check ownership
    if (workspace.owner_id !== user.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Get files for the workspace
    const files = await db.getWorkspaceFiles(workspaceId);

    return NextResponse.json({ files });
  } catch (error) {
    log.error(
      { err: error, workspaceId: (await params).id },
      'Error fetching workspace files'
    );
    const appError = error instanceof AppError ? error : handleError(error);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
}

// POST /api/workspaces/[id]/files - Upload a file to a workspace
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const workspaceId = (await params).id;

    const supabase = await createClient();
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const db = DatabaseService.getInstance(supabase);

    // Get the workspace to check ownership
    const workspace = await db.getWorkspace(workspaceId);

    if (!workspace) {
      return NextResponse.json(
        { error: 'Workspace not found' },
        { status: 404 }
      );
    }

    // Check ownership
    if (workspace.owner_id !== user.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Check subscription plan - file uploads require Premium plan
    const quotaService = QuotaService.getInstance();
    const userPlan = await quotaService.getEffectiveSubscriptionPlan(user.id);
    
    if (userPlan !== 'premium') {
      return NextResponse.json(
        { 
          error: 'File uploads to workspaces require a Premium subscription',
          upgradeRequired: true,
          feature: 'workspace_file_uploads'
        },
        { status: 403 }
      );
    }

    // Handle file upload
    const formData = await request.formData();
    const file = formData.get('file') as File | null;

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    // Validate file size
    if (file.size > MAX_FILE_SIZE_BYTES) {
      return NextResponse.json(
        { error: `File size exceeds the limit of ${MAX_FILE_SIZE_MB}MB` },
        { status: 413 }
      );
    }

    // Validate file type
    if (!ALLOWED_FILE_TYPES.includes(file.type)) {
      return NextResponse.json(
        {
          error: `File type '${
            file.type
          }' is not allowed. Allowed types: ${ALLOWED_FILE_TYPES.join(', ')}`,
        },
        { status: 415 }
      );
    }


    // Upload the file to storage
    const filePath = `${user.id}/workspaces/${workspaceId}/${file.name}`;

    const { error: uploadError } = await supabase.storage
      .from(STORAGE_BUCKET_NAME)
      .upload(filePath, file, {
        contentType: file.type,
      });


    // Create a database record for the file
    const workspaceFile = await db.createWorkspaceFile(workspaceId, {
      filename: file.name,
      mime_type: file.type,
      byte_size: file.size,
      file_path: filePath,
    });

    if (uploadError) {
      log.error(
        {
          err: uploadError,
          userId: user.id,
          workspaceId,
          fileId: workspaceFile.id,
        },
        'Storage upload error'
      );

      // Update file status to error
      await db.updateWorkspaceFileStatus(workspaceFile.id, 'error');

      throw new AppError(
        'Failed to upload file',
        ErrorCode.INTERNAL,
        500,
        uploadError
      );
    }

    // Get the public URL for the file
    const { data: urlData } = supabase.storage
      .from(STORAGE_BUCKET_NAME)
      .getPublicUrl(filePath);

    // For text files, extract the content
    let textContent: string | undefined = undefined;
    if (
      file.type.startsWith('text/') ||
      file.type === 'application/json' ||
      file.type === 'application/csv'
    ) {
      textContent = await file.text();
    }

    // Update the file status and text content
    const updatedFile = await db.updateWorkspaceFileStatus(
      workspaceFile.id,
      'completed',
      textContent
    );

    log.info(
      { userId: user.id, workspaceId, fileId: workspaceFile.id },
      'File uploaded successfully'
    );

    return NextResponse.json({
      file: updatedFile,
      url: urlData?.publicUrl,
    });
  } catch (error) {
    log.error({ err: error, workspaceId: (await params).id }, 'Error uploading file');
    const appError = error instanceof AppError ? error : handleError(error);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
}

// DELETE /api/workspaces/[id]/files - Delete a file
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const workspaceId = (await params).id;
    const url = new URL(request.url);
    const fileId = url.searchParams.get('fileId');

    if (!fileId) {
      return NextResponse.json(
        { error: 'File ID is required' },
        { status: 400 }
      );
    }

    const supabase = await createClient();
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const db = DatabaseService.getInstance(supabase);

    // Get the workspace to check ownership
    const workspace = await db.getWorkspace(workspaceId);

    if (!workspace) {
      return NextResponse.json(
        { error: 'Workspace not found' },
        { status: 404 }
      );
    }

    // Check ownership
    if (workspace.owner_id !== user.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Delete the file record
    await db.deleteWorkspaceFile(fileId);

    // Note: We're not deleting the actual file from storage
    // This could be added later if needed

    log.info({ userId: user.id, workspaceId, fileId }, 'File deleted');

    return NextResponse.json({ success: true });
  } catch (error) {
    log.error({ err: error, workspaceId: (await params).id }, 'Error deleting file');
    const appError = error instanceof AppError ? error : handleError(error);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
}
