import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { handleError } from '@/lib/error';
import { FeatureFlagService } from '@/lib/feature-flags/service';
import { logger } from '@/lib/logger';

const log = logger.child({ module: 'FeatureFlagsAPI' });

/**
 * GET /api/feature-flags
 * Get all feature flags for the current authenticated user
 */
export async function GET() {
  try {
    const supabase = await createClient();

    // Check authentication
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      log.warn('Unauthenticated request to feature flags API');
      return NextResponse.json(
        { flags: {}, error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get feature flag service
    const featureFlagService = await FeatureFlagService.getInstance();

    // Get all flags for the user
    const flags = await featureFlagService.getUserFeatureFlags(user.id);

    log.info(
      { userId: user.id, flagCount: Object.keys(flags).length },
      'Retrieved feature flags for user'
    );

    return NextResponse.json({
      flags,
      error: null,
    });
  } catch (error) {
    const appError = handleError(error);
    log.error({ err: appError }, 'Error retrieving feature flags');

    return NextResponse.json(
      { flags: {}, error: appError.message },
      { status: appError.statusCode }
    );
  }
}
