import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { handleError } from '@/lib/error';
import { FeatureFlagService } from '@/lib/feature-flags/service';
import { logger } from '@/lib/logger';

const log = logger.child({ module: 'FeatureFlagAPI' });

/**
 * GET /api/feature-flags/[flagName]
 * Check if a specific feature flag is enabled for the current authenticated user
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ flagName: string }> }
) {
  let flagName: string = '';
  try {
    ({ flagName } = await params);
    const supabase = await createClient();

    // Check authentication
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      log.warn({ flagName }, 'Unauthenticated request to feature flag API');
      return NextResponse.json(
        { isEnabled: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Validate flag name
    if (!flagName || typeof flagName !== 'string') {
      return NextResponse.json(
        { isEnabled: false, error: 'Invalid flag name' },
        { status: 400 }
      );
    }

    // Get feature flag service
    const featureFlagService = await FeatureFlagService.getInstance();

    // Check if flag is enabled for the user
    const isEnabled = await featureFlagService.isFeatureEnabled(
      flagName,
      user.id
    );

    log.debug(
      { userId: user.id, flagName, isEnabled },
      'Checked feature flag for user'
    );

    return NextResponse.json({
      isEnabled,
      error: null,
    });
  } catch (error) {
    const appError = handleError(error);
    log.error(
      { err: appError, flagName },
      'Error checking feature flag'
    );

    return NextResponse.json(
      { isEnabled: false, error: appError.message },
      { status: appError.statusCode }
    );
  }
}
