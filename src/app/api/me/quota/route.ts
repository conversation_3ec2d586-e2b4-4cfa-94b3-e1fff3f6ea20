import { createClient } from '@/utils/supabase/server';
import { QuotaService } from '@/services/quota';
import { logger } from '@/lib/logger';
import { NextResponse } from 'next/server';

const log = logger.child({ module: 'quota-api' });

export async function GET() {
  try {
    const supabase = await createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const quotaService = QuotaService.getInstance();

    // Get image usage
    const imageUsage = await quotaService.getQuotaUsage(user.id);

    // Get token usage
    const tokenUsage = await quotaService.checkTokenQuota(user.id);

    // Get comparison usage (Premium only)
    const comparisonUsage = await quotaService.getComparisonQuotaUsage(user.id);

    const response: {
      images: {
        used: number;
        remaining: number;
        total: number;
        resetDate: string;
      };
      tokens?: {
        used: number;
        remaining: number;
        total: number;
        resetDate: string;
      };
      comparisons?: {
        used: number;
        remaining: number;
        total: number;
        resetDate: string;
      };
    } = {
      images: {
        used: imageUsage.imagesUsed,
        remaining: imageUsage.imagesRemaining,
        total: imageUsage.maxImages,
        resetDate: imageUsage.resetDate,
      },
    };

    // Add token data for paid plans
    if (tokenUsage.tokenQuota !== null) {
      response.tokens = {
        used: tokenUsage.tokensUsed,
        remaining: tokenUsage.tokensRemaining,
        total: tokenUsage.tokenQuota,
        resetDate: tokenUsage.resetDate,
      };
    }

    // Add comparison data for premium users
    if (comparisonUsage.maxComparisons > 0) {
      response.comparisons = {
        used: comparisonUsage.comparisonsUsed,
        remaining: comparisonUsage.comparisonsRemaining,
        total: comparisonUsage.maxComparisons,
        resetDate: comparisonUsage.resetDate,
      };
    }

    return NextResponse.json(response);
  } catch (error) {
    log.error({ err: error }, 'Error fetching quota usage');
    return NextResponse.json(
      { error: 'Failed to fetch quota usage' },
      { status: 500 }
    );
  }
}
