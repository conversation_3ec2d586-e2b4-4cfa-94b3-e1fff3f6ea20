import { NextRequest, NextResponse } from 'next/server';
import { DatabaseService } from '@/lib/supabase/db';
import { logger } from '@/lib/logger';
import { createClient } from '@/utils/supabase/server';
import { AppError } from '@/lib/error';

const log = logger.child({
  module: 'group-conversation',
});

export async function GET(
  _: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  try {
    const supabase = await createClient();
    const db = DatabaseService.getInstance(supabase);
    const groupConversationMessages = await db.getGroupConversationMessages(id);
    const conversationData = groupConversationMessages.map((convo) => ({
      ...convo,
      parentMessageNode: db.buildMessageTree(convo.messages || []),
      messages: undefined,
    }));
    return Response.json(conversationData);
  } catch (error) {
    log.error({ err: error }, 'Error fetching group conversation messages');
    const statusCode = error instanceof AppError ? error.statusCode : 500;
    return Response.json(
      { error: 'Failed to fetch group conversation messages' },
      { status: statusCode }
    );
  }
}

export async function PUT(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  const { title } = await request.json();
  if (!id || !title) {
    return NextResponse.json(
      { data: null, error: 'Missing conversationId or title' },
      { status: 400 }
    );
  }
  const supabase = await createClient();
  const db = DatabaseService.getInstance(supabase);
  const conversation = await db.updateGroupConversation(id, {
    title,
  });
  return NextResponse.json({ data: conversation, error: null });
}

export async function PATCH(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  if (!id) {
    return NextResponse.json(
      { data: null, error: 'Missing conversationId' },
      { status: 400 }
    );
  }

  try {
    const supabase = await createClient();
    const db = DatabaseService.getInstance(supabase);
    const conversation = await db.toggleGroupConversationFavorite(id);
    return NextResponse.json({ data: conversation, error: null });
  } catch (error) {
    log.error({ err: error }, 'Error toggling favorite status');
    const statusCode = error instanceof AppError ? error.statusCode : 500;
    return Response.json(
      { error: 'Failed to toggle favorite status' },
      { status: statusCode }
    );
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  const supabase = await createClient();
  const db = DatabaseService.getInstance(supabase);
  await db.deleteGroupConversation(id);
  return NextResponse.json({ data: null, error: null });
}
