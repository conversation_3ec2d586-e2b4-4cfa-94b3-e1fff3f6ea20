import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';
import <PERSON><PERSON> from 'stripe';
import {
  constructEventFromPayload,
  STRIPE_PRICE_IDS,
  stripe,
} from '@/lib/stripe';
import { updateSubscription } from '@/lib/supabase/subscription';
import { logger } from '@/lib/logger';
import { AppError } from '@/lib/error';
import { SubscriptionStatus, SubscriptionPlan } from '@/lib/supabase/types';
import { createClient } from '@/utils/supabase/server';
import { SupabaseClient } from '@supabase/supabase-js';

const log = logger.child({ module: 'stripe-webhook' });

// SECURITY: Comprehensive metadata validation for webhooks
function validateAddonMetadata(
  metadata: Record<string, string> | null | undefined
): boolean {
  if (!metadata) {
    log.warn('Metadata is null or undefined');
    return false;
  }

  const { userId, addonType, tokens, credits } = metadata;

  // Validate userId (must be valid UUID format)
  if (!userId) {
    log.warn('Missing userId in metadata');
    return false;
  }

  const uuidRegex =
    /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  if (!uuidRegex.test(userId)) {
    log.warn('Invalid userId format in metadata', { userId });
    return false;
  }

  // Validate addonType (must be exact enum value)
  if (!addonType || !['tokens', 'images'].includes(addonType)) {
    log.warn('Invalid or missing addonType in metadata', { addonType });
    return false;
  }

  // Validate amounts based on addon type
  if (addonType === 'tokens') {
    if (!tokens) {
      log.warn('Missing tokens for token addon', { metadata });
      return false;
    }

    const tokenAmount = parseInt(tokens);
    if (isNaN(tokenAmount) || tokenAmount <= 0 || tokenAmount > 10000000) {
      log.warn('Invalid token amount in metadata', { tokens, tokenAmount });
      return false;
    }
  } else if (addonType === 'images') {
    if (!credits) {
      log.warn('Missing credits for image addon', { metadata });
      return false;
    }

    const creditAmount = parseInt(credits);
    if (isNaN(creditAmount) || creditAmount <= 0 || creditAmount > 100000) {
      log.warn('Invalid credit amount in metadata', { credits, creditAmount });
      return false;
    }
  }

  // Check for extraneous fields (potential injection attempt)
  const allowedFields = ['userId', 'addonType', 'tokens', 'credits'];
  const metadataKeys = Object.keys(metadata);
  const extraFields = metadataKeys.filter(
    (key) => !allowedFields.includes(key)
  );

  if (extraFields.length > 0) {
    log.warn('Unexpected fields in metadata - potential injection attempt', {
      extraFields,
      metadata,
    });
    // Don't reject, but log for security monitoring
  }

  return true;
}

// SECURITY: Distributed rate limiting for webhook processing
async function checkWebhookRateLimit(
  eventType: string,
  supabase: SupabaseClient
): Promise<boolean> {
  try {
    const rateKey = `webhook_${eventType}`;

    const { data, error } = await supabase.rpc(
      'check_and_increment_webhook_rate_limit',
      {
        p_rate_key: rateKey,
        p_max_requests: 100, // 100 requests per window
        p_window_seconds: 300, // 5-minute window
      }
    );

    if (error) {
      log.error({ err: error, eventType }, 'Error checking webhook rate limit');
      // Fail open - allow the request if rate limiting fails
      return true;
    }

    const result = data?.[0];
    if (!result?.allowed) {
      log.error('Webhook rate limit exceeded', {
        eventType,
        currentCount: result?.current_count,
        resetTime: result?.reset_time,
      });
      return false;
    }

    log.debug('Webhook rate limit check passed', {
      eventType,
      currentCount: result.current_count,
      resetTime: result.reset_time,
    });

    return true;
  } catch (error) {
    log.error(
      { err: error, eventType },
      'Unexpected error in webhook rate limiting'
    );
    // Fail open - allow the request if rate limiting fails
    return true;
  }
}

export async function POST(req: NextRequest) {
  const body = await req.text();
  const signature = (await headers()).get('stripe-signature');

  if (!signature) {
    log.warn('Webhook signature missing');
    return NextResponse.json(
      { error: 'Webhook signature missing' },
      { status: 400 }
    );
  }

  let event: Stripe.Event;
  let supabase: SupabaseClient;

  try {
    event = constructEventFromPayload(signature, Buffer.from(body));
    log.info(
      { eventType: event.type, eventId: event.id },
      'Event constructed successfully'
    );

    // Initialize Supabase client for rate limiting and processing
    supabase = await createClient();

    // SECURITY: Rate limiting check
    if (!(await checkWebhookRateLimit(event.type, supabase))) {
      log.error('Webhook rate limit exceeded', {
        eventType: event.type,
        eventId: event.id,
      });
      return NextResponse.json(
        { error: 'Rate limit exceeded' },
        { status: 429 }
      );
    }
  } catch (error: unknown) {
    log.error({ err: error }, 'Error constructing event from payload');
    const statusCode = error instanceof AppError ? error.statusCode : 500;
    return NextResponse.json(
      { error: 'Failed to construct event from payload' },
      { status: statusCode }
    );
  }

  try {
    // Use already initialized supabase client

    // Atomic idempotency check using upsert pattern to prevent race conditions
    const { data: insertResult, error: insertError } = await supabase
      .from('stripe_events')
      .upsert(
        {
          event_id: event.id,
          event_type: event.type,
          created_at: new Date().toISOString(),
        },
        {
          onConflict: 'event_id',
          ignoreDuplicates: true,
        }
      )
      .select('event_id')
      .single();

    if (insertError && insertError.code !== '23505') {
      // 23505 is unique violation
      log.error({ err: insertError }, 'Error during idempotency check');
      throw new AppError('Failed to check event uniqueness', '500');
    }

    // If no data returned and no error, event already existed
    if (!insertResult && !insertError) {
      log.info({ eventId: event.id }, 'Event already processed, skipping.');
      return NextResponse.json(
        { message: 'Event already processed' },
        { status: 200 }
      );
    }

    // Process the event since it's new
    await handleStripeEvent(event, supabase);

    log.info(
      { eventType: event.type, eventId: event.id },
      'Webhook event processed successfully'
    );
    return NextResponse.json({ message: 'Webhook received' }, { status: 200 });
  } catch (error: unknown) {
    log.error(
      { err: error, eventType: event.type, eventId: event.id },
      'Error handling webhook'
    );
    const statusCode = error instanceof AppError ? error.statusCode : 500;
    return NextResponse.json(
      { error: 'Failed to handle webhook' },
      { status: statusCode }
    );
  }
}

async function handleStripeEvent(
  event: Stripe.Event,
  supabase: SupabaseClient
) {
  switch (event.type) {
    case 'checkout.session.completed': {
      const session = event.data.object as Stripe.Checkout.Session;
      if (
        session.mode === 'subscription' &&
        session.customer &&
        session.subscription
      ) {
        await handleSessionCreated(
          String(session.customer),
          String(session.subscription),
          supabase
        );
      } else if (session.mode === 'payment' && session.metadata) {
        // Handle add-on purchases - SINGLE SOURCE OF TRUTH
        await handleAddonPurchase(session, supabase);
      }
      break;
    }

    case 'customer.subscription.created': {
      const subscription = event.data.object as Stripe.Subscription;
      const customerId =
        typeof subscription.customer === 'string'
          ? subscription.customer
          : subscription.customer.id;

      // Use current_period_end from the main subscription object if available, else from items
      await handleSubscriptionCreated(customerId, subscription, supabase);
      break;
    }

    case 'customer.subscription.updated':
      await handleSubscriptionUpdated(
        event.data.object as Stripe.Subscription,
        supabase
      );
      break;

    case 'customer.subscription.deleted':
      await handleSubscriptionDeleted(
        event.data.object as Stripe.Subscription,
        supabase
      );
      break;

    case 'invoice.payment_succeeded':
      await handleInvoicePaymentSucceeded(
        event.data.object as Stripe.Invoice & {
          subscription: string | Stripe.Subscription;
        },
        supabase
      );
      break;

    case 'invoice.payment_failed':
      await handleInvoicePaymentFailed(
        event.data.object as Stripe.Invoice & {
          subscription: string | Stripe.Subscription;
        },
        supabase
      );
      break;

    // REMOVED: charge.updated handler to prevent double-processing
    // Add-on purchases are now processed exclusively via checkout.session.completed

    default:
      log.info({ eventType: event.type }, 'Unhandled event type');
  }
}

async function handleSessionCreated(
  customerId: string,
  subscriptionId: string,
  supabase: SupabaseClient
) {
  const subscription = await findSubscriptionByCustomerId(customerId, supabase);

  await updateSubscription(subscription.id, {
    stripe_subscription_id: subscriptionId,
    status: 'active',
  });
}

async function handleSubscriptionCreated(
  customerId: string,
  subscription: Stripe.Subscription,
  supabase: SupabaseClient
) {
  const dbSubscription = await findSubscriptionByCustomerId(
    customerId,
    supabase
  );

  const priceId = subscription.items.data[0]?.price.id;
  const plan = getPlanFromPriceId(priceId);
  const currentPeriodEnd = new Date(
    (subscription.items.data[0]?.current_period_end || Date.now() / 1000) * 1000
  ).toISOString();

  await updateSubscription(dbSubscription.id, {
    plan: plan as SubscriptionPlan,
    current_period_end: currentPeriodEnd,
    stripe_subscription_id: subscription.id,
    status: subscription.status as SubscriptionStatus,
  });
}

async function handleSubscriptionUpdated(
  subscription: Stripe.Subscription,
  supabase: SupabaseClient
) {
  log.info(
    { subscriptionId: subscription.id, customerId: subscription.customer },
    'Handling Subscription Updated event'
  );

  // Try finding by customer ID instead, as it's less likely to change during upgrade
  const customerId =
    typeof subscription.customer === 'string'
      ? subscription.customer
      : subscription.customer.id;
  if (!customerId) {
    log.error(
      { subscriptionId: subscription.id },
      'Customer ID missing in subscription update event'
    );
    throw new AppError('Customer ID missing', '400');
  }

  // Use findSubscriptionByCustomerId
  const dbSubscription = await findSubscriptionByCustomerId(
    customerId,
    supabase
  );

  // Now that we have the correct DB record, proceed with updates
  const priceId = subscription.items.data[0]?.price.id;
  const plan = getPlanFromPriceId(priceId);

  await updateSubscription(dbSubscription.id, {
    plan: plan as SubscriptionPlan,
    status: subscription.status as SubscriptionStatus,
    cancel_at_period_end: subscription.cancel_at_period_end,
    // Make sure to update the subscription ID if it changed!
    stripe_subscription_id: subscription.id,
    current_period_end: new Date(
      (subscription.items.data[0]?.current_period_end || Date.now() / 1000) *
        1000
    ).toISOString(),
  });
  log.info(
    { dbSubscriptionId: dbSubscription.id, newStripeSubId: subscription.id },
    'Subscription record updated'
  );
}

async function handleSubscriptionDeleted(
  subscription: Stripe.Subscription,
  supabase: SupabaseClient
) {
  const customerId =
    typeof subscription.customer === 'string'
      ? subscription.customer
      : subscription.customer.id;
  if (!customerId) return;

  const dbSubscription = await findSubscriptionByCustomerId(
    customerId,
    supabase
  );

  await updateSubscription(dbSubscription.id, {
    status: 'canceled',
    plan: 'free',
    cancel_at_period_end: true,
  });
}

async function handleInvoicePaymentSucceeded(
  invoice: Stripe.Invoice & { subscription: string | Stripe.Subscription },
  supabase: SupabaseClient
) {
  const customerId =
    invoice.customer &&
    (typeof invoice.customer === 'string'
      ? invoice.customer
      : invoice.customer.id);
  if (!customerId) return;

  const dbSubscription = await findSubscriptionByCustomerId(
    customerId,
    supabase
  );
  const periodEnd = invoice.lines.data[0]?.period?.end;

  if (periodEnd) {
    await updateSubscription(dbSubscription.id, {
      current_period_end: new Date(periodEnd * 1000).toISOString(),
      status: 'active',
    });
  }
}

async function handleInvoicePaymentFailed(
  invoice: Stripe.Invoice & { subscription: string | Stripe.Subscription },
  supabase: SupabaseClient
) {
  const customerId =
    invoice.customer &&
    (typeof invoice.customer === 'string'
      ? invoice.customer
      : invoice.customer.id);
  if (!customerId) return;

  const dbSubscription = await findSubscriptionByCustomerId(
    customerId,
    supabase
  );

  await updateSubscription(dbSubscription.id, {
    status: 'past_due',
  });
}

// Helper functions
async function findSubscriptionByCustomerId(
  customerId: string,
  supabase: SupabaseClient
) {
  const { data, error } = await supabase.rpc(
    'find_subscription_by_customer_id',
    {
      p_stripe_customer_id: customerId,
    }
  );

  if (error || !data || data.length === 0) {
    log.error(
      { err: error, customerId },
      'Subscription not found by customer ID'
    );
    throw new AppError('Subscription not found', '404');
  }

  // The function returns an array, so we take the first (and only) result
  return data[0];
}

async function handleAddonPurchase(
  session: Stripe.Checkout.Session,
  supabase: SupabaseClient
) {
  const { userId, addonType, tokens, credits } = session.metadata || {};

  // ENHANCED VALIDATION: Comprehensive metadata validation
  if (!validateAddonMetadata(session.metadata)) {
    log.error('Invalid addon purchase metadata - validation failed', {
      sessionId: session.id,
      metadata: session.metadata,
      customerId: session.customer,
    });
    throw new AppError('Invalid addon purchase metadata', '400');
  }

  // SECURITY: Validate price_id matches claimed addon type to prevent fraud
  let priceId = session.line_items?.data?.[0]?.price?.id;

  // FALLBACK: If line_items are not available, fetch from Stripe API
  if (!priceId) {
    log.warn('No price ID found in session - retrieving from Stripe API', {
      sessionId: session.id,
      hasLineItems: !!session.line_items,
      lineItemsCount: session.line_items?.data?.length || 0,
    });

    try {
      const expandedSession = await stripe.checkout.sessions.retrieve(
        session.id,
        {
          expand: ['line_items'],
        }
      );
      priceId = expandedSession.line_items?.data?.[0]?.price?.id;

      if (!priceId) {
        log.error('No price ID found even after expanding line_items', {
          sessionId: session.id,
          expandedLineItems: expandedSession.line_items?.data,
        });
        throw new AppError('No price ID found in session', '400');
      }

      log.info('Successfully retrieved price ID from Stripe API', {
        sessionId: session.id,
        priceId,
      });
    } catch (error) {
      log.error(
        {
          err: error,
          sessionId: session.id,
        },
        'Failed to retrieve session from Stripe API'
      );
      throw new AppError('Failed to validate session price ID', '500');
    }
  }

  if (priceId) {
    // Cross-check price_id with addon type (only if we have the price_id)
    if (
      addonType === 'tokens' &&
      priceId !== STRIPE_PRICE_IDS.token_pack_100k
    ) {
      log.error('Price ID mismatch for token purchase - potential fraud', {
        sessionId: session.id,
        expectedPriceId: STRIPE_PRICE_IDS.token_pack_100k,
        actualPriceId: priceId,
        addonType,
      });
      throw new AppError('Price ID does not match addon type', '400');
    }

    if (
      addonType === 'images' &&
      priceId !== STRIPE_PRICE_IDS.image_pack_1000
    ) {
      log.error('Price ID mismatch for image purchase - potential fraud', {
        sessionId: session.id,
        expectedPriceId: STRIPE_PRICE_IDS.image_pack_1000,
        actualPriceId: priceId,
        addonType,
      });
      throw new AppError('Price ID does not match addon type', '400');
    }
  }

  // SECURITY: Verify user ownership by cross-checking customer ID using database function
  if (session.customer) {
    const { data: isOwner, error: verificationError } = await supabase.rpc(
      'verify_customer_ownership',
      {
        p_stripe_customer_id: session.customer.toString(),
        p_user_id: userId,
      }
    );

    if (verificationError) {
      log.error(
        {
          err: verificationError,
          sessionId: session.id,
          metadataUserId: userId,
          stripeCustomerId: session.customer,
          errorCode: verificationError.code,
          errorMessage: verificationError.message,
        },
        'Failed to verify customer ownership'
      );
      throw new AppError(
        'Customer verification failed - unable to verify ownership',
        '403'
      );
    }

    if (!isOwner) {
      log.error(
        {
          sessionId: session.id,
          metadataUserId: userId,
          stripeCustomerId: session.customer,
          verificationResult: isOwner,
        },
        'Customer ID mismatch - potential fraud attempt'
      );
      throw new AppError('Customer verification failed', '403');
    }

    log.info('Customer verification successful', {
      sessionId: session.id,
      userId: userId,
      stripeCustomerId: session.customer,
      verificationResult: isOwner,
    });
  }

  try {
    if (addonType === 'tokens' && tokens) {
      const tokenAmount = parseInt(tokens);
      if (isNaN(tokenAmount) || tokenAmount <= 0) {
        throw new AppError('Invalid token amount', '400');
      }

      // Use new persistent balance system (non-expiring)
      const { error: tokenError } = await supabase.rpc(
        'add_persistent_token_balance',
        {
          p_user_id: userId,
          p_tokens: tokenAmount,
        }
      );

      if (tokenError) {
        log.error(
          {
            err: tokenError,
            userId,
            tokens: tokenAmount,
            sessionId: session.id,
          },
          'Failed to process token purchase'
        );
        throw new AppError('Failed to process token purchase', '500');
      }

      log.info('Token purchase processed successfully', {
        userId,
        tokens: tokenAmount,
        sessionId: session.id,
        balanceSystem: 'persistent',
      });
    } else if (addonType === 'images' && credits) {
      const creditAmount = parseInt(credits);
      if (isNaN(creditAmount) || creditAmount <= 0) {
        throw new AppError('Invalid credit amount', '400');
      }

      // Use new persistent balance system (non-expiring)
      const { error: creditError } = await supabase.rpc(
        'add_persistent_credit_balance',
        {
          p_user_id: userId,
          p_credits: creditAmount,
        }
      );

      if (creditError) {
        log.error(
          {
            err: creditError,
            userId,
            credits: creditAmount,
            sessionId: session.id,
          },
          'Failed to process image credit purchase'
        );
        throw new AppError('Failed to process image credit purchase', '500');
      }

      log.info('Image credit purchase processed successfully', {
        userId,
        credits: creditAmount,
        sessionId: session.id,
        balanceSystem: 'persistent',
      });
    } else {
      log.error(
        {
          addonType,
          tokens,
          credits,
          sessionId: session.id,
          userId,
        },
        'Invalid addon type or missing addon data'
      );
      throw new AppError('Invalid addon type or missing addon data', '400');
    }

    // Log successful purchase for audit trail
    log.info('Addon purchase completed', {
      userId,
      addonType,
      amount: addonType === 'tokens' ? tokens : credits,
      sessionId: session.id,
      paymentStatus: session.payment_status,
      processingMethod: 'checkout_session_completed_only',
    });
  } catch (error) {
    // Re-throw AppErrors as-is, wrap other errors
    if (error instanceof AppError) {
      log.error(
        {
          err: error,
          userId,
          addonType,
          sessionId: session.id,
        },
        'AppError processing addon purchase'
      );
      throw error;
    }

    log.error(
      {
        err: error,
        userId,
        addonType,
        sessionId: session.id,
      },
      'Unexpected error processing addon purchase'
    );
    throw new AppError('Failed to process addon purchase', '500');
  }
}

// REMOVED: handleChargeUpdated function
// This function was causing double-processing of add-on purchases
// All add-on purchases are now handled exclusively via checkout.session.completed
//
// This eliminates the critical bug where users could receive double credits
// for the same purchase when both checkout.session.completed and charge.updated
// events fired for the same transaction.

function getPlanFromPriceId(priceId?: string): string {
  if (!priceId) {
    log.warn('No price ID provided');
    return 'free';
  }

  // Check for starter plans (monthly or yearly)
  if (
    priceId === STRIPE_PRICE_IDS.starter_monthly ||
    priceId === STRIPE_PRICE_IDS.starter_yearly
  ) {
    return 'starter';
  }

  // Check for premium plans (monthly or yearly)
  if (
    priceId === STRIPE_PRICE_IDS.premium_monthly ||
    priceId === STRIPE_PRICE_IDS.premium_yearly
  ) {
    return 'premium';
  }

  log.warn({ priceId }, 'Unknown price ID received');
  return 'free';
}
