import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { DatabaseService } from '@/lib/supabase/db';
import { AppError, ErrorCode, handleError } from '@/lib/error';
import { logger } from '@/lib/logger';

const STORAGE_BUCKET_NAME = 'attachments';

export async function POST(req: Request) {
  const log = logger.child({ api: '/api/upload/check-file' });

  try {
    const supabase = await createClient();
    const db = DatabaseService.getInstance(supabase);

    // Get the current user (optional - can be used to restrict access)
    const user = await db.getCurrentUser();
    if (!user) {
      log.warn('Unauthorized upload check attempt');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the hash from the request body
    const { hash } = await req.json();

    if (!hash) {
      throw new AppError(
        'No file hash provided',
        ErrorCode.INVALID_REQUEST,
        400
      );
    }

    log.info({ hash }, 'Checking if file exists');

    // Query the uploaded_files table to see if a file with this hash exists
    const existingFile = await db.getUploadedFile(hash);

    if (!existingFile) {
      log.info({ hash }, 'File with hash does not exist');
      return NextResponse.json({ exists: false });
    }

    if (existingFile) {
      // File exists, get the public URL and return the data
      log.info({ hash }, 'File with hash exists');

      // Generate the public URL for the file
      const { data: urlData } = supabase.storage
        .from(STORAGE_BUCKET_NAME)
        .getPublicUrl(existingFile.storage_path);

      // Return the file data with the URL
      return NextResponse.json({
        exists: true,
        file: {
          ...existingFile,
          url: urlData.publicUrl,
        },
      });
    } else {
      // File doesn't exist
      log.info({ hash }, 'File with hash does not exist');
      return NextResponse.json({ exists: false });
    }
  } catch (error) {
    log.error({ err: error }, 'Error in file check API');
    const appError = error instanceof AppError ? error : handleError(error);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
}
