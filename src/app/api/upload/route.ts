import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { DatabaseService } from '@/lib/supabase/db';
import { AppError, ErrorCode, handleError } from '@/lib/error';
import { v4 as uuidv4 } from 'uuid';
import { logger } from '@/lib/logger';
import { MAX_FILE_SIZE_BYTES } from '@/constants/attachment';
import { MAX_FILE_SIZE_MB } from '@/constants/attachment';
import { ALLOWED_FILE_TYPES } from '@/constants/attachment';

const STORAGE_BUCKET_NAME = 'attachments'; // Make sure this bucket exists and policies are set

export async function POST(req: Request): Promise<NextResponse> {
  const log = logger.child({ api: '/api/upload' });

  try {
    const supabase = await createClient();
    const db = DatabaseService.getInstance(supabase);

    const user = await db.getCurrentUser();
    if (!user) {
      log.warn('Unauthorized upload attempt');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const formData = await req.formData();
    const file = formData.get('file') as File | null;
    const fileHash = formData.get('hash') as string | null;

    if (!file) {
      throw new AppError('No file provided', ErrorCode.INVALID_REQUEST, 400);
    }

    if (!fileHash) {
      throw new AppError(
        'No file hash provided',
        ErrorCode.INVALID_REQUEST,
        400
      );
    }

    // --- Input Validation ---
    if (file.size > MAX_FILE_SIZE_BYTES) {
      throw new AppError(
        `File size exceeds the limit of ${MAX_FILE_SIZE_MB}MB`,
        ErrorCode.INVALID_REQUEST,
        413 // Payload Too Large
      );
    }

    // Use the updated ALLOWED_FILE_TYPES constant
    if (!ALLOWED_FILE_TYPES.includes(file.type)) {
      throw new AppError(
        `File type '${
          file.type
        }' is not allowed. Allowed: ${ALLOWED_FILE_TYPES.join(', ')}`,
        ErrorCode.INVALID_REQUEST,
        415 // Unsupported Media Type
      );
    }
    // --- End Validation ---

    // First, check if this file already exists in the database
    const existingFile = await db.getUploadedFile(fileHash);

    // If file already exists, return the existing data
    if (existingFile) {
      log.info(
        { userId: user.id, fileHash, existingId: existingFile.id },
        'File with same hash already exists, returning existing data'
      );

      // Get the public URL
      const { data: urlData } = supabase.storage
        .from(STORAGE_BUCKET_NAME)
        .getPublicUrl(existingFile.storage_path);

      return NextResponse.json(
        {
          url: urlData?.publicUrl,
          name: file.name,
          type: file.type,
          size: file.size,
          isExisting: true,
        },
        { status: 200 }
      );
    }

    // File doesn't exist, proceed with upload
    const fileExtension = file.name.split('.').pop();
    // Use the hash in the file path to ensure idempotency
    const filePath = `${user.id}/${fileHash}${
      fileExtension ? '.' + fileExtension : ''
    }`;

    log.info(
      { userId: user.id, fileName: file.name, filePath, fileHash },
      'Attempting file upload'
    );

    const { error: uploadError } = await supabase.storage
      .from(STORAGE_BUCKET_NAME)
      .upload(filePath, file, {
        // Optionally set cache control and content type
        // cacheControl: '3600',
        contentType: file.type, // Explicitly set content type
      });

    if (uploadError) {
      log.error(
        { err: uploadError, userId: user.id, filePath },
        'Supabase storage upload error'
      );
      throw new AppError(
        'Failed to upload file',
        ErrorCode.INTERNAL,
        500,
        uploadError
      );
    }

    // Get the public URL
    const { data: urlData } = supabase.storage
      .from(STORAGE_BUCKET_NAME)
      .getPublicUrl(filePath);

    if (!urlData || !urlData.publicUrl) {
      log.error(
        { filePath, userId: user.id },
        'Failed to get public URL after upload'
      );
      throw new AppError(
        'File uploaded but failed to get public URL',
        ErrorCode.INTERNAL,
        500
      );
    }

    // Insert the file record in the database (not upsert since we already checked existence)
    const { error: dbError } = await supabase
      .from('uploaded_files')
      .insert({
        id: uuidv4(),
        file_hash: fileHash,
        storage_path: filePath,
        original_filename: file.name,
        mime_type: file.type,
        size_bytes: file.size,
        uploader_user_id: user.id,
        created_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (dbError) {
      log.error(
        { err: dbError, userId: user.id, filePath, fileHash },
        'Database error when recording file upload'
      );
      throw new AppError(
        'File uploaded but failed to record in database',
        ErrorCode.INTERNAL,
        500,
        dbError
      );
    }

    log.info(
      { userId: user.id, filePath, url: urlData.publicUrl, fileHash },
      'File uploaded successfully'
    );

    return NextResponse.json(
      {
        url: urlData.publicUrl,
        name: file.name,
        type: file.type,
        size: file.size,
      },
      { status: 200 }
    );
  } catch (error) {
    log.error({ err: error }, 'Error in file upload API');
    const appError = error instanceof AppError ? error : handleError(error);
    // Ensure the error message is included in the JSON response
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
}
