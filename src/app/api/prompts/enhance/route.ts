import { NextRequest, NextResponse } from 'next/server';
import { createPromptEnhancer } from '@/services/promptEnhancer';
import { logger } from '@/lib/logger';
import { AppError } from '@/lib/error';
import { createClient } from '@/utils/supabase/server';
import { getUserSubscription } from '@/lib/supabase/subscription';
import { SUBSCRIPTION_PLANS } from '@/lib/supabase/types';

interface EnhancePromptRequest {
  prompt: string;
  model?: string;
  temperature?: number;
  maxTokens?: number;
}

// Rate limiting configuration
const DEFAULT_RATE_LIMIT_REQUESTS = parseInt(
  process.env.PROMPT_ENHANCEMENT_RATE_LIMIT_REQUESTS || '10',
  10
);
const DEFAULT_RATE_LIMIT_WINDOW_MS = parseInt(
  process.env.PROMPT_ENHANCEMENT_RATE_LIMIT_WINDOW_MS || '60000', // 1 minute
  10
);

// In-memory rate limit store (consider Redis for production)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

function checkRateLimit(userId: string): {
  allowed: boolean;
  resetTime?: number;
} {
  const now = Date.now();
  const key = `prompt_enhance:${userId}`;
  const window = rateLimitStore.get(key);

  if (!window || now > window.resetTime) {
    // Create new window
    rateLimitStore.set(key, {
      count: 1,
      resetTime: now + DEFAULT_RATE_LIMIT_WINDOW_MS,
    });
    return { allowed: true };
  }

  if (window.count >= DEFAULT_RATE_LIMIT_REQUESTS) {
    return { allowed: false, resetTime: window.resetTime };
  }

  // Increment count
  window.count++;
  return { allowed: true };
}

// Cleanup expired entries periodically
setInterval(() => {
  const now = Date.now();
  for (const [key, window] of rateLimitStore.entries()) {
    if (now > window.resetTime) {
      rateLimitStore.delete(key);
    }
  }
}, 60000); // Clean up every minute

const DEFAULT_ENHANCEMENT_MODEL =
  process.env.PROMPT_ENHANCEMENT_MODEL || 'openai/gpt-4o';
const DEFAULT_TEMPERATURE = parseFloat(
  process.env.PROMPT_ENHANCEMENT_TEMPERATURE || '0.7'
);
const DEFAULT_MAX_TOKENS = parseInt(
  process.env.PROMPT_ENHANCEMENT_MAX_TOKENS || '1000',
  10
);
const DEFAULT_TIMEOUT_MS = parseInt(
  process.env.PROMPT_ENHANCEMENT_TIMEOUT_MS || '30000',
  10
);
const DEFAULT_MAX_PROMPT_LENGTH = parseInt(
  process.env.PROMPT_ENHANCEMENT_MAX_LENGTH || '2000',
  10
);

export async function POST(request: NextRequest) {
  const log = logger.child({ route: '/api/prompts/enhance' });

  try {
    const supabase = await createClient();
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      log.warn('Unauthorized prompt enhancement request');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check rate limit
    const rateLimitResult = checkRateLimit(user.id);
    if (!rateLimitResult.allowed) {
      const resetTimeSeconds = Math.ceil(
        (rateLimitResult.resetTime! - Date.now()) / 1000
      );
      log.warn('Rate limit exceeded for prompt enhancement', {
        userId: user.id,
        resetInSeconds: resetTimeSeconds,
      });
      return NextResponse.json(
        {
          error: `Rate limit exceeded. Try again in ${resetTimeSeconds} seconds.`,
          retryAfter: resetTimeSeconds,
        },
        {
          status: 429,
          headers: {
            'Retry-After': resetTimeSeconds.toString(),
            'X-RateLimit-Limit': DEFAULT_RATE_LIMIT_REQUESTS.toString(),
            'X-RateLimit-Reset': rateLimitResult.resetTime!.toString(),
          },
        }
      );
    }

    // Check if user has prompt enhancement feature access
    const subscription = await getUserSubscription(user.id);

    const currentPlan = subscription?.plan || 'free';
    const isActive = subscription
      ? ['active', 'trialing'].includes(subscription.status)
      : false;

    // If subscription exists but isn't active, treat as free
    const effectivePlan = subscription && !isActive ? 'free' : currentPlan;

    // Check if the effective plan allows prompt enhancement using types.ts
    const planDetails = SUBSCRIPTION_PLANS[effectivePlan];

    if (!planDetails.allowsPromptEnhancement) {
      log.warn('Prompt enhancement not available for user plan', {
        userId: user.id,
        plan: effectivePlan,
      });
      return NextResponse.json(
        { error: 'Prompt enhancement requires a Starter or Premium plan' },
        { status: 403 }
      );
    }

    // Parse request body
    let body: EnhancePromptRequest;
    try {
      body = await request.json();
    } catch (error) {
      log.error({ err: error }, 'Invalid JSON in request body');
      return NextResponse.json(
        { error: 'Invalid request body' },
        { status: 400 }
      );
    }

    // Validate required fields
    if (
      !body.prompt ||
      typeof body.prompt !== 'string' ||
      !body.prompt.trim()
    ) {
      log.warn('Missing or invalid prompt in request');
      return NextResponse.json(
        { error: 'Prompt is required and must be a non-empty string' },
        { status: 400 }
      );
    }

    // Validate prompt length (reasonable limits)
    const trimmedPrompt = body.prompt.trim();
    if (trimmedPrompt.length > DEFAULT_MAX_PROMPT_LENGTH) {
      log.warn('Prompt too long', { length: trimmedPrompt.length });
      return NextResponse.json(
        {
          error: `Prompt must be less than ${DEFAULT_MAX_PROMPT_LENGTH} characters`,
        },
        { status: 400 }
      );
    }

    if (trimmedPrompt.length < 3) {
      log.warn('Prompt too short', { length: trimmedPrompt.length });
      return NextResponse.json(
        { error: 'Prompt must be at least 3 characters long' },
        { status: 400 }
      );
    }

    // Get OpenRouter API key
    const apiKey = process.env.OPENROUTER_API_KEY;
    if (!apiKey) {
      log.error('OpenRouter API key not configured');
      return NextResponse.json(
        { error: 'Prompt enhancement service is not configured' },
        { status: 500 }
      );
    }

    // Initialize prompt enhancer
    const promptEnhancer = createPromptEnhancer(apiKey);

    // Prepare enhancement configuration
    const config = {
      model: body.model || DEFAULT_ENHANCEMENT_MODEL,
      temperature: body.temperature ?? DEFAULT_TEMPERATURE,
      maxTokens: body.maxTokens ?? DEFAULT_MAX_TOKENS,
      timeoutMs: DEFAULT_TIMEOUT_MS,
    };

    // Validate model parameter
    if (config.model && typeof config.model !== 'string') {
      return NextResponse.json(
        { error: 'Model must be a string' },
        { status: 400 }
      );
    }

    // Validate temperature parameter
    if (
      config.temperature !== undefined &&
      (typeof config.temperature !== 'number' ||
        config.temperature < 0 ||
        config.temperature > 2)
    ) {
      return NextResponse.json(
        { error: 'Temperature must be a number between 0 and 2' },
        { status: 400 }
      );
    }

    // Validate maxTokens parameter
    if (
      config.maxTokens !== undefined &&
      (typeof config.maxTokens !== 'number' ||
        config.maxTokens < 1 ||
        config.maxTokens > 4000)
    ) {
      return NextResponse.json(
        { error: 'MaxTokens must be a number between 1 and 4000' },
        { status: 400 }
      );
    }

    log.info('Enhancing prompt', {
      userId: user.id,
      originalLength: trimmedPrompt.length,
      model: config.model,
    });

    // Enhance the prompt
    const result = await promptEnhancer.enhancePrompt(trimmedPrompt, config);

    if (!result.success) {
      log.error({ err: result.error }, 'Prompt enhancement failed');
      return NextResponse.json(
        { error: result.error || 'Failed to enhance prompt' },
        { status: 500 }
      );
    }

    log.info('Prompt enhancement successful', {
      userId: user.id,
      originalLength: result.originalPrompt.length,
      enhancedLength: result.enhancedPrompt.length,
      model: config.model,
    });

    return NextResponse.json({
      enhancedPrompt: result.enhancedPrompt,
      originalPrompt: result.originalPrompt,
      success: true,
    });
  } catch (error) {
    log.error({ err: error }, 'Unexpected error in prompt enhancement');

    if (error instanceof AppError) {
      return NextResponse.json(
        { error: error.message },
        { status: error.statusCode || 500 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
}
