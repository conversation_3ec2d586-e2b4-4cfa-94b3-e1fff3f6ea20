import { DatabaseService } from '@/lib/supabase/db';
import { createClient } from '@/utils/supabase/server';
import { handleError } from '@/lib/error';
import { ModelMapper } from '@/services/modelMapper';
import { NextResponse } from 'next/server';
import { logger } from '@/lib/logger';
import { Model } from '@/lib/supabase/types';

const log = logger.child({ module: 'api/models' });

export async function GET() {
  try {
    // Create a Supabase client using the current request context
    const supabase = await createClient();
    const db = DatabaseService.getInstance(supabase);

    // Fetch all active providers with their models
    const providersAndModels = await db.getProvidersAndModels();

    // Flatten the provider/model structure into a single array of API-ready models
    const modelsResponse = providersAndModels.flatMap((provider) =>
      provider.models.map((model) =>
        ModelMapper.mapInternalModelToApiResponse(model as unknown as Model)
      )
    );

    return NextResponse.json(modelsResponse);
  } catch (error) {
    log.error({ err: error }, 'Error fetching models');
    const appError = handleError(error);
    return NextResponse.json({ error: appError.message }, { status: appError.statusCode });
  }
}
