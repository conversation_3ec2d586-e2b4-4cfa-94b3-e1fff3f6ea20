import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { stripe, STRIPE_PRICE_IDS } from '@/lib/stripe';
import { logger } from '@/lib/logger';

const log = logger.child({ module: 'addon-purchases' });

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { addonType, quantity = 1 } = await request.json();

    // SECURITY: Validate addon type
    if (!addonType || !['tokens', 'images'].includes(addonType)) {
      return NextResponse.json(
        { error: 'Invalid addon type' },
        { status: 400 }
      );
    }

    // SECURITY: Enforce server-side quantity limits to prevent financial abuse
    if (!Number.isInteger(quantity) || quantity < 1 || quantity > 10) {
      log.warn('Invalid quantity attempted', {
        userId: user.id,
        quantity,
        addonType,
      });
      return NextResponse.json(
        { error: 'Quantity must be between 1 and 10' },
        { status: 400 }
      );
    }

    // Get user's subscription to get Stripe customer ID
    const { data: subscription } = await supabase
      .from('subscriptions')
      .select('stripe_customer_id')
      .eq('user_id', user.id)
      .single();

    if (!subscription?.stripe_customer_id) {
      return NextResponse.json(
        { error: 'No Stripe customer found' },
        { status: 400 }
      );
    }

    const origin = request.headers.get('origin') || 'http://localhost:3000';
    let priceId: string;
    let metadata: Record<string, string>;

    if (addonType === 'tokens') {
      priceId = STRIPE_PRICE_IDS.token_pack_100k;
      metadata = {
        userId: user.id,
        addonType: 'tokens',
        tokens: (quantity * 100000).toString(),
      };
    } else {
      priceId = STRIPE_PRICE_IDS.image_pack_1000;
      metadata = {
        userId: user.id,
        addonType: 'images',
        credits: (quantity * 1100).toString(), // 1100 credits includes 10% bonus (matches docs)
      };
    }

    // Create checkout session for one-time payment
    const session = await stripe.checkout.sessions.create({
      customer: subscription.stripe_customer_id,
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: quantity,
        },
      ],
      mode: 'payment',
      success_url: `${origin}/settings?tab=billing&success=${addonType}&quantity=${quantity}&session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${origin}/settings?tab=billing&canceled=true`,
      metadata, // FIXED: Place metadata directly on session, not payment_intent_data
      expand: ['line_items'], // CRITICAL FIX: Ensure line_items are available in webhook
    });

    log.info('Created addon checkout session', {
      userId: user.id,
      addonType,
      quantity,
      sessionId: session.id,
    });

    return NextResponse.json({ 
      url: session.url,
      sessionId: session.id 
    });
  } catch (error) {
    log.error({ err: error }, 'Error creating addon checkout session');
    return NextResponse.json(
      { error: 'Failed to create checkout session' },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    const supabase = await createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get available add-on pricing info
    const addonPricing = {
      tokens: {
        name: 'Token Pack',
        description: '100,000 additional tokens',
        price: '$5.00',
        priceId: STRIPE_PRICE_IDS.token_pack_100k,
      },
      images: {
        name: 'Image Credits',
        description: '1,100 image generation credits (10% bonus)',
        price: '$10.00',
        priceId: STRIPE_PRICE_IDS.image_pack_1000,
      },
    };

    return NextResponse.json(addonPricing);
  } catch (error) {
    log.error({ err: error }, 'Error fetching addon pricing');
    return NextResponse.json(
      { error: 'Failed to fetch addon pricing' },
      { status: 500 }
    );
  }
}
