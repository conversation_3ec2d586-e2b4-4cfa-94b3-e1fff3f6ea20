import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { logger } from '@/lib/logger';

const log = logger.child({ module: 'purchase-status' });

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('session_id');

    if (!sessionId) {
      return NextResponse.json(
        { error: 'Missing session_id' },
        { status: 400 }
      );
    }

    // Check if the webhook event for this session was processed successfully
    const { data: webhookEvents, error: webhookError } = await supabase
      .from('stripe_events')
      .select('event_id, event_type, created_at')
      .eq('event_type', 'checkout.session.completed')
      .gte('created_at', new Date(Date.now() - 10 * 60 * 1000).toISOString()) // Last 10 minutes
      .order('created_at', { ascending: false });

    if (webhookError) {
      log.error({ err: webhookError }, 'Error checking webhook events');
      return NextResponse.json(
        { error: 'Failed to check status' },
        { status: 500 }
      );
    }

    // For now, return a simple status
    // In production, you might want to correlate the session_id with webhook events
    return NextResponse.json({
      processed: webhookEvents && webhookEvents.length > 0,
      recent_webhook_events: webhookEvents?.length || 0,
    });
  } catch (error) {
    log.error({ err: error }, 'Error checking purchase status');
    return NextResponse.json(
      { error: 'Failed to check purchase status' },
      { status: 500 }
    );
  }
}
