import { NextRequest, NextResponse } from 'next/server';
import { handleError } from '@/lib/error';
import { requireAdmin, logAdminAction } from '@/lib/auth/admin';
import { FeatureFlagService } from '@/lib/feature-flags/service';
import { createFlagSchema } from '@/lib/feature-flags/types';
import { logger } from '@/lib/logger';

const log = logger.child({ module: 'AdminFeatureFlagsAPI' });

/**
 * GET /api/admin/feature-flags
 * Get all feature flags with metadata (admin only)
 */
export async function GET() {
  try {
    // Require admin authentication
    const adminUser = await requireAdmin();

    // Get feature flag service
    const featureFlagService = await FeatureFlagService.getInstance();
    
    // Get all flags
    const flags = await featureFlagService.getAllFlags();

    logAdminAction('list_feature_flags', { flagCount: flags.length }, adminUser);

    return NextResponse.json({
      data: flags,
      error: null,
    });
  } catch (error) {
    const appError = handleError(error);
    log.error({ err: appError }, 'Error retrieving feature flags for admin');
    
    return NextResponse.json(
      { data: [], error: appError.message },
      { status: appError.statusCode }
    );
  }
}

/**
 * POST /api/admin/feature-flags
 * Create a new feature flag (admin only)
 */
export async function POST(request: NextRequest) {
  try {
    // Require admin authentication
    const adminUser = await requireAdmin();

    // Parse and validate request body
    const body = await request.json();
    const validatedData = createFlagSchema.parse(body);

    // Get feature flag service
    const featureFlagService = await FeatureFlagService.getInstance();
    
    // Create the flag
    const flag = await featureFlagService.createFlag(validatedData, adminUser.id);

    logAdminAction(
      'create_feature_flag',
      { flagName: flag.name, flagId: flag.id },
      adminUser
    );

    return NextResponse.json({
      data: flag,
      error: null,
    }, { status: 201 });
  } catch (error) {
    const appError = handleError(error);
    log.error({ err: appError }, 'Error creating feature flag');
    
    return NextResponse.json(
      { data: null, error: appError.message },
      { status: appError.statusCode }
    );
  }
}
