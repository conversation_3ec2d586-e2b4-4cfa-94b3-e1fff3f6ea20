import { NextRequest, NextResponse } from 'next/server';
import { handleError } from '@/lib/error';
import { requireAdmin, logAdminAction } from '@/lib/auth/admin';
import { FeatureFlagService } from '@/lib/feature-flags/service';
import { updateFlagSchema } from '@/lib/feature-flags/types';
import { logger } from '@/lib/logger';

const log = logger.child({ module: 'AdminFeatureFlagAPI' });

/**
 * PUT /api/admin/feature-flags/[flagName]
 * Update a feature flag (admin only)
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ flagName: string }> }
) {
  let flagName: string = '';
  try {
    flagName = (await params).flagName;

    // Require admin authentication
    const adminUser = await requireAdmin();

    // Validate flag name
    if (!flagName || typeof flagName !== 'string') {
      return NextResponse.json(
        { data: null, error: 'Invalid flag name' },
        { status: 400 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = updateFlagSchema.parse(body);

    // Get feature flag service
    const featureFlagService = await FeatureFlagService.getInstance();

    // Update the flag
    const flag = await featureFlagService.updateFlag(flagName, validatedData);

    logAdminAction(
      'update_feature_flag',
      { flagName, flagId: flag.id, updates: validatedData },
      adminUser
    );

    return NextResponse.json({
      data: flag,
      error: null,
    });
  } catch (error) {
    const appError = handleError(error);
    log.error(
      { err: appError, flagName },
      'Error updating feature flag'
    );

    return NextResponse.json(
      { data: null, error: appError.message },
      { status: appError.statusCode }
    );
  }
}

/**
 * DELETE /api/admin/feature-flags/[flagName]
 * Delete a feature flag (admin only)
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ flagName: string }> }
) {
  let flagName: string = '';
  try {
    flagName = (await params).flagName;

    // Require admin authentication
    const adminUser = await requireAdmin();

    // Validate flag name
    if (!flagName || typeof flagName !== 'string') {
      return NextResponse.json(
        { error: 'Invalid flag name' },
        { status: 400 }
      );
    }

    // Get feature flag service
    const featureFlagService = await FeatureFlagService.getInstance();

    // Delete the flag
    await featureFlagService.deleteFlag(flagName);

    logAdminAction(
      'delete_feature_flag',
      { flagName },
      adminUser
    );

    return NextResponse.json({
      success: true,
      error: null,
    });
  } catch (error) {
    const appError = handleError(error);
    log.error(
      { err: appError, flagName },
      'Error deleting feature flag'
    );

    return NextResponse.json(
      { success: false, error: appError.message },
      { status: appError.statusCode }
    );
  }
}
