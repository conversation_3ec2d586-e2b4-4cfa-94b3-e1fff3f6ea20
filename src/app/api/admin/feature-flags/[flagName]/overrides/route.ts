import { NextRequest, NextResponse } from 'next/server';
import { handleError } from '@/lib/error';
import { requireAdmin, logAdminAction } from '@/lib/auth/admin';
import { FeatureFlagService } from '@/lib/feature-flags/service';
import { userOverrideSchema } from '@/lib/feature-flags/types';
import { logger } from '@/lib/logger';

const log = logger.child({ module: 'AdminFeatureFlagOverridesAPI' });

/**
 * GET /api/admin/feature-flags/[flagName]/overrides
 * Get user overrides for a feature flag (admin only)
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ flagName: string }> }
) {
  let flagName: string = '';
  try {
    flagName = (await params).flagName;

    // Require admin authentication
    const adminUser = await requireAdmin();

    // Validate flag name
    if (!flagName || typeof flagName !== 'string') {
      return NextResponse.json({ error: 'Invalid flag name' }, { status: 400 });
    }

    // Get feature flag service
    const featureFlagService = await FeatureFlagService.getInstance();

    // Get user overrides for this flag
    const overrides = await featureFlagService.getFlagUserOverrides(flagName);

    logAdminAction(
      'get_user_overrides',
      { flagName, overrideCount: overrides.length },
      adminUser
    );

    return NextResponse.json({
      data: overrides,
      error: null,
    });
  } catch (error) {
    const appError = handleError(error);
    log.error({ err: appError, flagName }, 'Error fetching user overrides');

    return NextResponse.json(
      { data: [], error: appError.message },
      { status: appError.statusCode }
    );
  }
}

/**
 * POST /api/admin/feature-flags/[flagName]/overrides
 * Set a user override for a feature flag (admin only)
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ flagName: string }> }
) {
  let flagName: string = '';
  try {
    flagName = (await params).flagName;

    // Require admin authentication
    const adminUser = await requireAdmin();

    // Validate flag name
    if (!flagName || typeof flagName !== 'string') {
      return NextResponse.json({ error: 'Invalid flag name' }, { status: 400 });
    }

    // Parse and validate request body
    const body = await request.json();
    const { user_id, is_enabled } = userOverrideSchema.parse(body);

    // Get feature flag service
    const featureFlagService = await FeatureFlagService.getInstance();

    // Set the user override
    await featureFlagService.setUserOverride(flagName, user_id, is_enabled);

    logAdminAction(
      'set_user_override',
      { flagName, userId: user_id, isEnabled: is_enabled },
      adminUser
    );

    return NextResponse.json({
      success: true,
      error: null,
    });
  } catch (error) {
    const appError = handleError(error);
    log.error({ err: appError, flagName }, 'Error setting user override');

    return NextResponse.json(
      { success: false, error: appError.message },
      { status: appError.statusCode }
    );
  }
}
