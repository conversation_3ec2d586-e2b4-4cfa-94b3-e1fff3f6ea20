import { NextRequest, NextResponse } from 'next/server';
import { handleError } from '@/lib/error';
import { requireAdmin, logAdminAction } from '@/lib/auth/admin';
import { FeatureFlagService } from '@/lib/feature-flags/service';
import { logger } from '@/lib/logger';

const log = logger.child({ module: 'AdminFeatureFlagUserOverrideAPI' });

/**
 * DELETE /api/admin/feature-flags/[flagName]/overrides/[userId]
 * Remove a user override for a feature flag (admin only)
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ flagName: string; userId: string }> }
) {
  let flagName: string = '',
    userId: string = '';
  try {
    const resolvedParams = await params;
    flagName = resolvedParams.flagName;
    userId = resolvedParams.userId;

    // Require admin authentication
    const adminUser = await requireAdmin();

    // Validate parameters
    if (!flagName || typeof flagName !== 'string') {
      return NextResponse.json({ error: 'Invalid flag name' }, { status: 400 });
    }

    if (!userId || typeof userId !== 'string') {
      return NextResponse.json({ error: 'Invalid user ID' }, { status: 400 });
    }

    // Get feature flag service
    const featureFlagService = await FeatureFlagService.getInstance();

    // Remove the user override
    await featureFlagService.removeUserOverride(flagName, userId);

    logAdminAction('remove_user_override', { flagName, userId }, adminUser);

    return NextResponse.json({
      success: true,
      error: null,
    });
  } catch (error) {
    const appError = handleError(error);
    log.error(
      { err: appError, flagName, userId },
      'Error removing user override'
    );

    return NextResponse.json(
      { success: false, error: appError.message },
      { status: appError.statusCode }
    );
  }
}
