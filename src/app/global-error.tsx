'use client';

import * as Sentry from '@sentry/nextjs';
import NextError from 'next/error';
import { useEffect } from 'react';

export default function GlobalError({
  error,
}: {
  error: Error & { digest?: string };
}) {
  useEffect(() => {
    // Only report to external services in production
    if (process.env.NODE_ENV === 'production') {
      // Report to Sentry with enhanced context
      try {
        Sentry.withScope((scope) => {
          scope.setTag('errorBoundary', 'global');
          scope.setLevel('fatal'); // Global errors are critical
          scope.setContext('globalError', {
            digest: error.digest,
            timestamp: new Date().toISOString(),
            userAgent:
              typeof window !== 'undefined'
                ? window.navigator.userAgent
                : 'server',
            url:
              typeof window !== 'undefined' ? window.location.href : 'server',
          });

          // Add breadcrumb for global error
          Sentry.addBreadcrumb({
            message: 'Global Error Boundary caught an unhandled error',
            category: 'error',
            level: 'fatal',
            data: {
              digest: error.digest,
            },
          });

          Sentry.captureException(error);
        });
      } catch (sentryError) {
        console.error('Failed to report to Sentry:', sentryError);
      }
    } else {
      // In development, just log to console
      console.error('Global error caught:', error);
    }
  }, [error]);

  return (
    <html>
      <body>
        {/* `NextError` is the default Next.js error page component. Its type
        definition requires a `statusCode` prop. However, since the App Router
        does not expose status codes for errors, we simply pass 0 to render a
        generic error message. */}
        <NextError statusCode={0} />
      </body>
    </html>
  );
}
