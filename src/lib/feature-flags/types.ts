import { z } from 'zod';
import { Tables } from '@/types/database.types';

// Database types
export type FeatureFlag = Tables<'feature_flags'>;
export type FeatureFlagUserOverride = Tables<'feature_flag_user_overrides'>;
export type FeatureFlagUserOverrideWithFlag = {
  user_id: string;
  is_enabled: boolean;
  feature_flags: { name: string };
};

// API request/response types
export interface CreateFlagInput {
  name: string;
  display_name: string;
  description?: string;
  is_enabled?: boolean;
  rollout_percentage?: number;
}

export interface UpdateFlagInput {
  display_name?: string;
  description?: string;
  is_enabled?: boolean;
  rollout_percentage?: number;
}

export interface FeatureFlagWithOverrides extends FeatureFlag {
  user_overrides?: FeatureFlagUserOverride[];
  affected_users_count?: number;
}

// Hook return types
export interface UseFeatureFlagResult {
  isEnabled: boolean;
  isLoading: boolean;
  error: Error | null;
}

export interface UseFeatureFlagsResult {
  flags: Record<string, boolean>;
  isLoading: boolean;
  error: Error | null;
}

// Service types
export interface FeatureFlagEvaluationContext {
  userId: string;
  flagName: string;
}

export interface FeatureFlagCache {
  flags: Map<string, FeatureFlag>;
  userOverrides: Map<string, Map<string, boolean>>; // userId -> flagName -> enabled
  lastUpdated: number;
}

// Validation schemas
export const createFlagSchema = z.object({
  name: z
    .string()
    .min(1, 'Name is required')
    .max(100, 'Name must be 100 characters or less')
    .regex(
      /^[a-z0-9-]+$/,
      'Name must be kebab-case (lowercase letters, numbers, and hyphens only)'
    )
    .refine(
      (name) => !name.startsWith('-') && !name.endsWith('-'),
      'Name cannot start or end with a hyphen'
    ),
  display_name: z
    .string()
    .min(1, 'Display name is required')
    .max(200, 'Display name must be 200 characters or less'),
  description: z
    .string()
    .max(1000, 'Description must be 1000 characters or less')
    .optional(),
  is_enabled: z.boolean().optional().default(false),
  rollout_percentage: z
    .number()
    .int('Rollout percentage must be an integer')
    .min(0, 'Rollout percentage must be at least 0')
    .max(100, 'Rollout percentage must be at most 100')
    .optional()
    .default(0),
});

export const updateFlagSchema = z.object({
  display_name: z
    .string()
    .min(1, 'Display name is required')
    .max(200, 'Display name must be 200 characters or less')
    .optional(),
  description: z
    .string()
    .max(1000, 'Description must be 1000 characters or less')
    .optional(),
  is_enabled: z.boolean().optional(),
  rollout_percentage: z
    .number()
    .int('Rollout percentage must be an integer')
    .min(0, 'Rollout percentage must be at least 0')
    .max(100, 'Rollout percentage must be at most 100')
    .optional(),
});

export const userOverrideSchema = z.object({
  user_id: z.string().uuid('Invalid user ID'),
  is_enabled: z.boolean(),
});

// API response schemas
export const featureFlagResponseSchema = z.object({
  data: z.array(
    z.object({
      id: z.string(),
      name: z.string(),
      display_name: z.string(),
      description: z.string().nullable(),
      is_enabled: z.boolean(),
      rollout_percentage: z.number(),
      created_at: z.string(),
      updated_at: z.string(),
      created_by: z.string(),
      user_overrides: z
        .array(
          z.object({
            id: z.string(),
            user_id: z.string(),
            is_enabled: z.boolean(),
            created_at: z.string(),
          })
        )
        .optional(),
      affected_users_count: z.number().optional(),
    })
  ),
  error: z.string().nullable(),
});

export const userFlagsResponseSchema = z.object({
  flags: z.record(z.string(), z.boolean()),
  error: z.string().nullable(),
});

// Error types
export class FeatureFlagError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number = 500
  ) {
    super(message);
    this.name = 'FeatureFlagError';
  }
}

// Constants
export const FEATURE_FLAG_CACHE_TTL = 5 * 60 * 1000; // 5 minutes in milliseconds
export const FEATURE_FLAG_CACHE_KEY = 'feature-flags';
export const USER_FLAGS_CACHE_KEY = 'user-feature-flags';

// Admin permission check
export const ADMIN_EMAILS =
  process.env.ADMIN_EMAILS?.split(',').map((email) => email.trim()) || [];
export const isAdminUser = (email: string): boolean => {
  return ADMIN_EMAILS.includes(email);
};

// Type guards
export const isFeatureFlag = (obj: unknown): obj is FeatureFlag => {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'id' in obj &&
    'name' in obj &&
    'display_name' in obj &&
    'is_enabled' in obj &&
    'rollout_percentage' in obj
  );
};

export const isFeatureFlagUserOverride = (
  obj: unknown
): obj is FeatureFlagUserOverride => {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'id' in obj &&
    'feature_flag_id' in obj &&
    'user_id' in obj &&
    'is_enabled' in obj
  );
};
