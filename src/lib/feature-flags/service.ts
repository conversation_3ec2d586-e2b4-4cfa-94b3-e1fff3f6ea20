import { createClient } from '@/utils/supabase/server';
import { SupabaseClient, PostgrestError } from '@supabase/supabase-js';
import { logger } from '@/lib/logger';
import { crc32 } from 'crc';
import {
  FeatureFlag,
  FeatureFlagUserOverride,
  CreateFlagInput,
  UpdateFlagInput,
  FeatureFlagCache,
  FEATURE_FLAG_CACHE_TTL,
  FeatureFlagError,
} from './types';

const log = logger.child({ module: 'FeatureFlagService' });

/**
 * Interface for user override with joined feature flag data from cache refresh query
 */
interface CacheUserOverrideResult {
  user_id: string;
  is_enabled: boolean;
  feature_flags: {
    name: string;
  };
}

/**
 * Service class for managing feature flags with caching and deterministic rollout
 */
export class FeatureFlagService {
  private static instance: FeatureFlagService;
  private cache: FeatureFlagCache = {
    flags: new Map(),
    userOverrides: new Map(),
    lastUpdated: 0,
  };

  private constructor(private supabase: SupabaseClient) {}

  /**
   * Get singleton instance of FeatureFlagService
   */
  public static async getInstance(): Promise<FeatureFlagService> {
    if (!FeatureFlagService.instance) {
      const supabase = await createClient();
      FeatureFlagService.instance = new FeatureFlagService(supabase);
    }
    return FeatureFlagService.instance;
  }

  /**
   * Main feature flag evaluation method
   * @param flagName - The name of the feature flag
   * @param userId - The user ID to evaluate for
   * @returns Promise<boolean> - Whether the feature is enabled for this user
   */
  public async isFeatureEnabled(
    flagName: string,
    userId: string
  ): Promise<boolean> {
    try {
      await this.refreshCacheIfNeeded();
      return this.evaluateFlag(flagName, userId);
    } catch (error) {
      log.error(
        { err: error, flagName, userId },
        'Error evaluating feature flag, returning false'
      );
      return false;
    }
  }

  /**
   * Get all feature flags for a user (bulk evaluation)
   * @param userId - The user ID to evaluate for
   * @returns Promise<Record<string, boolean>> - Map of flag names to enabled status
   */
  public async getUserFeatureFlags(
    userId: string
  ): Promise<Record<string, boolean>> {
    try {
      await this.refreshCacheIfNeeded();

      const result: Record<string, boolean> = {};

      for (const [flagName] of this.cache.flags) {
        result[flagName] = this.evaluateFlag(flagName, userId);
      }

      log.debug(
        { userId, flagCount: Object.keys(result).length },
        'Evaluated all feature flags for user'
      );

      return result;
    } catch (error) {
      log.error(
        { err: error, userId },
        'Error getting user feature flags, returning empty object'
      );
      return {};
    }
  }

  /**
   * Create a new feature flag (admin only)
   * @param data - The feature flag data
   * @param createdBy - The user ID creating the flag
   * @returns Promise<FeatureFlag> - The created feature flag
   */
  public async createFlag(
    data: CreateFlagInput,
    createdBy: string
  ): Promise<FeatureFlag> {
    try {
      const { data: flag, error } = await this.supabase
        .from('feature_flags')
        .insert({
          ...data,
          created_by: createdBy,
        })
        .select()
        .single();

      if (error) {
        throw new FeatureFlagError(
          `Failed to create feature flag: ${error.message}`,
          'CREATE_FLAG_ERROR',
          400
        );
      }

      // Invalidate cache
      this.invalidateCache();

      log.info(
        { flagName: data.name, createdBy },
        'Feature flag created successfully'
      );

      return flag;
    } catch (error) {
      if (error instanceof FeatureFlagError) {
        throw error;
      }
      throw new FeatureFlagError(
        'Failed to create feature flag',
        'CREATE_FLAG_ERROR',
        500
      );
    }
  }

  /**
   * Update an existing feature flag (admin only)
   * @param flagName - The name of the flag to update
   * @param updates - The updates to apply
   * @returns Promise<FeatureFlag> - The updated feature flag
   */
  public async updateFlag(
    flagName: string,
    updates: UpdateFlagInput
  ): Promise<FeatureFlag> {
    try {
      const { data: flag, error } = await this.supabase
        .from('feature_flags')
        .update(updates)
        .eq('name', flagName)
        .select()
        .single();

      if (error) {
        throw new FeatureFlagError(
          `Failed to update feature flag: ${error.message}`,
          'UPDATE_FLAG_ERROR',
          400
        );
      }

      if (!flag) {
        throw new FeatureFlagError(
          'Feature flag not found',
          'FLAG_NOT_FOUND',
          404
        );
      }

      // Invalidate cache
      this.invalidateCache();

      log.info({ flagName, updates }, 'Feature flag updated successfully');

      return flag;
    } catch (error) {
      if (error instanceof FeatureFlagError) {
        throw error;
      }
      throw new FeatureFlagError(
        'Failed to update feature flag',
        'UPDATE_FLAG_ERROR',
        500
      );
    }
  }

  /**
   * Set a user-specific override for a feature flag (admin only)
   * @param flagName - The name of the flag
   * @param userId - The user ID to override for
   * @param enabled - Whether the flag should be enabled for this user
   */
  public async setUserOverride(
    flagName: string,
    userId: string,
    enabled: boolean
  ): Promise<void> {
    try {
      // First, get the flag ID
      const { data: flag, error: flagError } = await this.supabase
        .from('feature_flags')
        .select('id')
        .eq('name', flagName)
        .single();

      if (flagError || !flag) {
        throw new FeatureFlagError(
          'Feature flag not found',
          'FLAG_NOT_FOUND',
          404
        );
      }

      // Upsert the override
      const { error } = await this.supabase
        .from('feature_flag_user_overrides')
        .upsert({
          feature_flag_id: flag.id,
          user_id: userId,
          is_enabled: enabled,
        });

      if (error) {
        throw new FeatureFlagError(
          `Failed to set user override: ${error.message}`,
          'SET_OVERRIDE_ERROR',
          400
        );
      }

      // Invalidate cache
      this.invalidateCache();

      log.info({ flagName, userId, enabled }, 'User override set successfully');
    } catch (error) {
      if (error instanceof FeatureFlagError) {
        throw error;
      }
      throw new FeatureFlagError(
        'Failed to set user override',
        'SET_OVERRIDE_ERROR',
        500
      );
    }
  }

  /**
   * Remove a user-specific override for a feature flag (admin only)
   * @param flagName - The name of the flag
   * @param userId - The user ID to remove override for
   */
  public async removeUserOverride(
    flagName: string,
    userId: string
  ): Promise<void> {
    try {
      // First, get the flag ID
      const { data: flag, error: flagError } = await this.supabase
        .from('feature_flags')
        .select('id')
        .eq('name', flagName)
        .single();

      if (flagError || !flag) {
        throw new FeatureFlagError(
          'Feature flag not found',
          'FLAG_NOT_FOUND',
          404
        );
      }

      // Delete the override
      const { error } = await this.supabase
        .from('feature_flag_user_overrides')
        .delete()
        .eq('feature_flag_id', flag.id)
        .eq('user_id', userId);

      if (error) {
        throw new FeatureFlagError(
          `Failed to remove user override: ${error.message}`,
          'REMOVE_OVERRIDE_ERROR',
          400
        );
      }

      // Invalidate cache
      this.invalidateCache();

      log.info({ flagName, userId }, 'User override removed successfully');
    } catch (error) {
      if (error instanceof FeatureFlagError) {
        throw error;
      }
      throw new FeatureFlagError(
        'Failed to remove user override',
        'REMOVE_OVERRIDE_ERROR',
        500
      );
    }
  }

  /**
   * Get user overrides for a specific feature flag (admin only)
   * @param flagName - The name of the flag
   * @returns Promise<FeatureFlagUserOverride[]> - Array of user overrides for the flag
   */
  public async getFlagUserOverrides(
    flagName: string
  ): Promise<FeatureFlagUserOverride[]> {
    try {
      // First, get the flag ID
      const { data: flag, error: flagError } = await this.supabase
        .from('feature_flags')
        .select('id')
        .eq('name', flagName)
        .single();

      if (flagError || !flag) {
        throw new FeatureFlagError(
          'Feature flag not found',
          'FLAG_NOT_FOUND',
          404
        );
      }

      // Get all user overrides for this flag
      const { data: overrides, error: overridesError } = await this.supabase
        .from('feature_flag_user_overrides')
        .select('*')
        .eq('feature_flag_id', flag.id)
        .order('created_at', { ascending: false });

      if (overridesError) {
        throw new FeatureFlagError(
          `Failed to fetch user overrides: ${overridesError.message}`,
          'FETCH_OVERRIDES_ERROR',
          400
        );
      }

      log.debug(
        { flagName, overrideCount: overrides?.length || 0 },
        'Fetched user overrides for flag'
      );

      return overrides || [];
    } catch (error) {
      if (error instanceof FeatureFlagError) {
        throw error;
      }
      throw new FeatureFlagError(
        'Failed to fetch user overrides',
        'FETCH_OVERRIDES_ERROR',
        500
      );
    }
  }

  /**
   * Get all feature flags with their overrides (admin only)
   * @returns Promise<FeatureFlag[]> - All feature flags
   */
  public async getAllFlags(): Promise<FeatureFlag[]> {
    try {
      const { data: flags, error } = await this.supabase
        .from('feature_flags')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        throw new FeatureFlagError(
          `Failed to get feature flags: ${error.message}`,
          'GET_FLAGS_ERROR',
          400
        );
      }

      return flags || [];
    } catch (error) {
      if (error instanceof FeatureFlagError) {
        throw error;
      }
      throw new FeatureFlagError(
        'Failed to get feature flags',
        'GET_FLAGS_ERROR',
        500
      );
    }
  }

  /**
   * Delete a feature flag (admin only)
   * @param flagName - The name of the flag to delete
   */
  public async deleteFlag(flagName: string): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('feature_flags')
        .delete()
        .eq('name', flagName);

      if (error) {
        throw new FeatureFlagError(
          `Failed to delete feature flag: ${error.message}`,
          'DELETE_FLAG_ERROR',
          400
        );
      }

      // Invalidate cache
      this.invalidateCache();

      log.info({ flagName }, 'Feature flag deleted successfully');
    } catch (error) {
      if (error instanceof FeatureFlagError) {
        throw error;
      }
      throw new FeatureFlagError(
        'Failed to delete feature flag',
        'DELETE_FLAG_ERROR',
        500
      );
    }
  }

  /**
   * Determines if a user is in the rollout percentage using consistent hashing
   * @param userId - The user ID
   * @param flagName - The flag name
   * @param percentage - The rollout percentage (0-100)
   * @returns boolean - Whether the user is in the rollout
   */
  private evaluateFlag(flagName: string, userId: string): boolean {
    // Get the flag definition
    const flag = this.cache.flags.get(flagName);
    if (!flag) {
      log.debug(
        { flagName, userId },
        'Feature flag not found, returning false'
      );
      return false;
    }

    // Check for user-specific override first (highest priority)
    const userOverrides = this.cache.userOverrides.get(userId);
    if (userOverrides?.has(flagName)) {
      const override = userOverrides.get(flagName)!;
      log.debug(
        { flagName, userId, override },
        'Using user-specific override for feature flag'
      );
      return override;
    }

    // If flag is globally disabled, return false
    if (!flag.is_enabled) {
      log.debug({ flagName, userId }, 'Feature flag is globally disabled');
      return false;
    }

    // If rollout percentage is 100%, return true
    if (flag.rollout_percentage >= 100) {
      log.debug({ flagName, userId }, 'Feature flag enabled for all users');
      return true;
    }

    // If rollout percentage is 0%, return false
    if (flag.rollout_percentage <= 0) {
      log.debug({ flagName, userId }, 'Feature flag has 0% rollout');
      return false;
    }

    // Use deterministic hashing for consistent rollout
    const isInRollout = this.isUserInRollout(
      userId,
      flagName,
      flag.rollout_percentage
    );
    log.debug(
      {
        flagName,
        userId,
        rolloutPercentage: flag.rollout_percentage,
        isInRollout,
      },
      'Evaluated feature flag rollout'
    );

    return isInRollout;
  }

  /**
   * Determines if a user is in the rollout percentage using consistent hashing
   * @param userId - The user ID
   * @param flagName - The flag name
   * @param percentage - The rollout percentage (0-100)
   * @returns boolean - Whether the user is in the rollout
   */
  private isUserInRollout(
    userId: string,
    flagName: string,
    percentage: number
  ): boolean {
    // Create a consistent hash input
    const hashInput = `${userId}-${flagName}`;

    // Use CRC32 for fast, consistent hashing
    const hash = crc32(hashInput);

    // Convert to positive number and get percentage (0-99)
    const userPercentile = Math.abs(hash) % 100;

    // User is in rollout if their percentile is less than the rollout percentage
    return userPercentile < percentage;
  }

  /**
   * Refresh cache if it's stale
   */
  private async refreshCacheIfNeeded(): Promise<void> {
    const now = Date.now();
    if (now - this.cache.lastUpdated < FEATURE_FLAG_CACHE_TTL) {
      return; // Cache is still fresh
    }

    try {
      // Fetch all flags
      const { data: flags, error: flagsError } = await this.supabase
        .from('feature_flags')
        .select('*');

      if (flagsError) {
        log.error({ err: flagsError }, 'Failed to refresh feature flags cache');
        return;
      }

      // Fetch all user overrides
      const { data: overrides, error: overridesError } = await this.supabase
        .from('feature_flag_user_overrides')
        .select(`
          user_id,
          is_enabled,
          feature_flags!inner(name)
        `) as { data: CacheUserOverrideResult[] | null; error: PostgrestError | null };

      if (overridesError) {
        log.error(
          { err: overridesError },
          'Failed to refresh user overrides cache'
        );
        return;
      }

      // Update cache
      this.cache.flags.clear();
      this.cache.userOverrides.clear();

      // Populate flags cache
      flags?.forEach((flag) => {
        this.cache.flags.set(flag.name, flag);
      });

      // Populate user overrides cache
      overrides?.forEach((override: CacheUserOverrideResult) => {
        const userId = override.user_id;
        const flagName = override.feature_flags.name;
        const isEnabled = override.is_enabled;

        if (!flagName) return; // Skip if no flag name

        if (!this.cache.userOverrides.has(userId)) {
          this.cache.userOverrides.set(userId, new Map());
        }
        this.cache.userOverrides.get(userId)!.set(flagName, isEnabled);
      });

      this.cache.lastUpdated = now;

      log.debug(
        {
          flagCount: this.cache.flags.size,
          overrideCount: this.cache.userOverrides.size,
        },
        'Feature flags cache refreshed successfully'
      );
    } catch (error) {
      log.error({ err: error }, 'Error refreshing feature flags cache');
    }
  }

  /**
   * Invalidate the cache (force refresh on next access)
   */
  private invalidateCache(): void {
    this.cache.lastUpdated = 0;
    log.debug('Feature flags cache invalidated');
  }

  /**
   * Get cache statistics (for debugging)
   */
  public getCacheStats(): {
    flagCount: number;
    userOverrideCount: number;
    lastUpdated: number;
    isStale: boolean;
  } {
    const now = Date.now();
    return {
      flagCount: this.cache.flags.size,
      userOverrideCount: this.cache.userOverrides.size,
      lastUpdated: this.cache.lastUpdated,
      isStale: now - this.cache.lastUpdated >= FEATURE_FLAG_CACHE_TTL,
    };
  }
}
