import { FeatureFlagService } from '../service';
import { createClient } from '@/utils/supabase/server';
import { crc32 } from 'crc';

// Mock dependencies
jest.mock('@/utils/supabase/server');
jest.mock('@/lib/logger', () => ({
  logger: {
    child: () => ({
      error: jest.fn(),
      warn: jest.fn(),
      info: jest.fn(),
      debug: jest.fn(),
    }),
  },
}));
jest.mock('crc');

const mockSupabase = {
  from: jest.fn(),
  auth: {
    getUser: jest.fn(),
  },
};

const mockCrc32 = crc32 as jest.MockedFunction<typeof crc32>;

describe('FeatureFlagService', () => {
  let service: FeatureFlagService;

  beforeEach(async () => {
    jest.clearAllMocks();
    (createClient as jest.Mock).mockResolvedValue(mockSupabase);

    // Reset the singleton instance
    (FeatureFlagService as unknown as { instance: FeatureFlagService | null }).instance = null;
    service = await FeatureFlagService.getInstance();
  });

  describe('isFeatureEnabled', () => {
    it('should return false for non-existent flag', async () => {
      // Mock empty cache
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          data: [],
          error: null,
        }),
      });

      const result = await service.isFeatureEnabled(
        'non-existent-flag',
        'user-123'
      );
      expect(result).toBe(false);
    });

    it('should return false for disabled flag', async () => {
      const mockFlag = {
        id: 'flag-1',
        name: 'test-flag',
        display_name: 'Test Flag',
        description: 'A test flag',
        is_enabled: false,
        rollout_percentage: 50,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        created_by: 'admin-123',
      };

      // Mock cache refresh
      mockSupabase.from.mockImplementation((table) => {
        if (table === 'feature_flags') {
          return {
            select: jest.fn().mockReturnValue({
              data: [mockFlag],
              error: null,
            }),
          };
        }
        if (table === 'feature_flag_user_overrides') {
          return {
            select: jest.fn().mockReturnValue({
              data: [],
              error: null,
            }),
          };
        }
      });

      const result = await service.isFeatureEnabled('test-flag', 'user-123');
      expect(result).toBe(false);
    });

    it('should return true for enabled flag with 100% rollout', async () => {
      const mockFlag = {
        id: 'flag-1',
        name: 'test-flag',
        display_name: 'Test Flag',
        description: 'A test flag',
        is_enabled: true,
        rollout_percentage: 100,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        created_by: 'admin-123',
      };

      mockSupabase.from.mockImplementation((table) => {
        if (table === 'feature_flags') {
          return {
            select: jest.fn().mockReturnValue({
              data: [mockFlag],
              error: null,
            }),
          };
        }
        if (table === 'feature_flag_user_overrides') {
          return {
            select: jest.fn().mockReturnValue({
              data: [],
              error: null,
            }),
          };
        }
      });

      const result = await service.isFeatureEnabled('test-flag', 'user-123');
      expect(result).toBe(true);
    });

    it('should use consistent hashing for rollout percentage', async () => {
      const mockFlag = {
        id: 'flag-1',
        name: 'test-flag',
        display_name: 'Test Flag',
        description: 'A test flag',
        is_enabled: true,
        rollout_percentage: 50,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        created_by: 'admin-123',
      };

      mockSupabase.from.mockImplementation((table) => {
        if (table === 'feature_flags') {
          return {
            select: jest.fn().mockReturnValue({
              data: [mockFlag],
              error: null,
            }),
          };
        }
        if (table === 'feature_flag_user_overrides') {
          return {
            select: jest.fn().mockReturnValue({
              data: [],
              error: null,
            }),
          };
        }
      });

      // Mock CRC32 to return a value that puts user in the 25th percentile
      mockCrc32.mockReturnValue(25);

      const result = await service.isFeatureEnabled('test-flag', 'user-123');
      expect(result).toBe(true); // 25 < 50, so user is in rollout

      expect(mockCrc32).toHaveBeenCalledWith('user-123-test-flag');
    });

    it('should respect user overrides over rollout percentage', async () => {
      const mockFlag = {
        id: 'flag-1',
        name: 'test-flag',
        display_name: 'Test Flag',
        description: 'A test flag',
        is_enabled: true,
        rollout_percentage: 0, // 0% rollout
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        created_by: 'admin-123',
      };

      const mockOverride = {
        id: 'override-1',
        feature_flag_id: 'flag-1',
        user_id: 'user-123',
        is_enabled: true, // Override enables the flag
        created_at: '2024-01-01T00:00:00Z',
        feature_flags: [{ name: 'test-flag' }],
      };

      mockSupabase.from.mockImplementation((table) => {
        if (table === 'feature_flags') {
          return {
            select: jest.fn().mockReturnValue({
              data: [mockFlag],
              error: null,
            }),
          };
        }
        if (table === 'feature_flag_user_overrides') {
          return {
            select: jest.fn().mockReturnValue({
              data: [mockOverride],
              error: null,
            }),
          };
        }
      });

      const result = await service.isFeatureEnabled('test-flag', 'user-123');
      expect(result).toBe(true); // Override should take precedence
    });

    it('should be consistent for the same user and flag', async () => {
      const mockFlag = {
        id: 'flag-1',
        name: 'test-flag',
        display_name: 'Test Flag',
        description: 'A test flag',
        is_enabled: true,
        rollout_percentage: 50,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        created_by: 'admin-123',
      };

      mockSupabase.from.mockImplementation((table) => {
        if (table === 'feature_flags') {
          return {
            select: jest.fn().mockReturnValue({
              data: [mockFlag],
              error: null,
            }),
          };
        }
        if (table === 'feature_flag_user_overrides') {
          return {
            select: jest.fn().mockReturnValue({
              data: [],
              error: null,
            }),
          };
        }
      });

      // Mock CRC32 to return consistent value
      mockCrc32.mockReturnValue(25);

      const result1 = await service.isFeatureEnabled('test-flag', 'user-123');
      const result2 = await service.isFeatureEnabled('test-flag', 'user-123');

      expect(result1).toBe(result2);
      expect(mockCrc32).toHaveBeenCalledWith('user-123-test-flag');
    });
  });

  describe('createFlag', () => {
    it('should create a new feature flag', async () => {
      const mockCreatedFlag = {
        id: 'flag-1',
        name: 'new-flag',
        display_name: 'New Flag',
        description: 'A new flag',
        is_enabled: false,
        rollout_percentage: 0,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        created_by: 'admin-123',
      };

      mockSupabase.from.mockReturnValue({
        insert: jest.fn().mockReturnValue({
          select: jest.fn().mockReturnValue({
            single: jest.fn().mockReturnValue({
              data: mockCreatedFlag,
              error: null,
            }),
          }),
        }),
      });

      const flagData = {
        name: 'new-flag',
        display_name: 'New Flag',
        description: 'A new flag',
        is_enabled: false,
        rollout_percentage: 0,
      };

      const result = await service.createFlag(flagData, 'admin-123');
      expect(result).toEqual(mockCreatedFlag);
    });
  });

  describe('updateFlag', () => {
    it('should update an existing feature flag', async () => {
      const mockUpdatedFlag = {
        id: 'flag-1',
        name: 'existing-flag',
        display_name: 'Updated Flag',
        description: 'An updated flag',
        is_enabled: true,
        rollout_percentage: 50,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T01:00:00Z',
        created_by: 'admin-123',
      };

      mockSupabase.from.mockReturnValue({
        update: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            select: jest.fn().mockReturnValue({
              single: jest.fn().mockReturnValue({
                data: mockUpdatedFlag,
                error: null,
              }),
            }),
          }),
        }),
      });

      const updates = {
        display_name: 'Updated Flag',
        is_enabled: true,
        rollout_percentage: 50,
      };

      const result = await service.updateFlag('existing-flag', updates);
      expect(result).toEqual(mockUpdatedFlag);
    });
  });

  describe('getUserFeatureFlags', () => {
    it('should return all flags for a user', async () => {
      const mockFlags = [
        {
          id: 'flag-1',
          name: 'flag-1',
          display_name: 'Flag 1',
          description: 'First flag',
          is_enabled: true,
          rollout_percentage: 100,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
          created_by: 'admin-123',
        },
        {
          id: 'flag-2',
          name: 'flag-2',
          display_name: 'Flag 2',
          description: 'Second flag',
          is_enabled: false,
          rollout_percentage: 0,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
          created_by: 'admin-123',
        },
      ];

      mockSupabase.from.mockImplementation((table) => {
        if (table === 'feature_flags') {
          return {
            select: jest.fn().mockReturnValue({
              data: mockFlags,
              error: null,
            }),
          };
        }
        if (table === 'feature_flag_user_overrides') {
          return {
            select: jest.fn().mockReturnValue({
              data: [],
              error: null,
            }),
          };
        }
      });

      const result = await service.getUserFeatureFlags('user-123');

      expect(result).toEqual({
        'flag-1': true, // enabled with 100% rollout
        'flag-2': false, // disabled
      });
    });
  });

  describe('getFlagUserOverrides', () => {
    it('should return user overrides for a specific flag', async () => {
      const mockFlag = {
        id: 'flag-1',
        name: 'test-flag',
        display_name: 'Test Flag',
        description: 'A test flag',
        is_enabled: true,
        rollout_percentage: 50,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        created_by: 'admin-123',
      };

      const mockOverrides = [
        {
          id: 'override-1',
          feature_flag_id: 'flag-1',
          user_id: 'user-123',
          is_enabled: true,
          created_at: '2024-01-01T00:00:00Z',
        },
        {
          id: 'override-2',
          feature_flag_id: 'flag-1',
          user_id: 'user-456',
          is_enabled: false,
          created_at: '2024-01-01T00:00:00Z',
        },
      ];

      mockSupabase.from.mockImplementation((table) => {
        if (table === 'feature_flags') {
          return {
            select: jest.fn().mockReturnValue({
              eq: jest.fn().mockReturnValue({
                single: jest.fn().mockReturnValue({
                  data: mockFlag,
                  error: null,
                }),
              }),
            }),
          };
        }
        if (table === 'feature_flag_user_overrides') {
          return {
            select: jest.fn().mockReturnValue({
              eq: jest.fn().mockReturnValue({
                order: jest.fn().mockReturnValue({
                  data: mockOverrides,
                  error: null,
                }),
              }),
            }),
          };
        }
      });

      const result = await service.getFlagUserOverrides('test-flag');

      expect(result).toEqual(mockOverrides);
    });
  });
});
