import Stripe from 'stripe';
import { SubscriptionPlan } from './supabase/types';
import { logger } from './logger';

const log = logger.child({
  module: 'Stripe',
});

// Validate critical environment variables on startup
const validateEnvironmentVars = () => {
  const requiredVars = {
    STRIPE_SECRET_KEY: process.env.STRIPE_SECRET_KEY,
    STRIPE_WEBHOOK_SECRET: process.env.STRIPE_WEBHOOK_SECRET,
    STRIPE_PRICE_STARTER_MONTHLY: process.env.STRIPE_PRICE_STARTER_MONTHLY,
    STRIPE_PRICE_PREMIUM_MONTHLY: process.env.STRIPE_PRICE_PREMIUM_MONTHLY,
  };

  const missingVars = Object.entries(requiredVars)
    .filter(([, value]) => !value)
    .map(([key]) => key);

  if (missingVars.length > 0) {
    log.error('Missing required Stripe environment variables:', missingVars);
    throw new Error(
      `Missing required Stripe environment variables: ${missingVars.join(', ')}`
    );
  }
};

// Validate environment variables on module load
validateEnvironmentVars();

const STRIPE_API_KEY = process.env.STRIPE_SECRET_KEY!;
const STRIPE_WEBHOOK_SECRET = process.env.STRIPE_WEBHOOK_SECRET!;

// Consolidated pricing structure - single source of truth for all Stripe price IDs
export const STRIPE_PRICE_IDS = {
  // Subscription plans
  starter_monthly: process.env.STRIPE_PRICE_STARTER_MONTHLY!,
  starter_yearly:
    process.env.STRIPE_PRICE_STARTER_YEARLY || 'price_starter_120_yearly',
  premium_monthly: process.env.STRIPE_PRICE_PREMIUM_MONTHLY!,
  premium_yearly:
    process.env.STRIPE_PRICE_PREMIUM_YEARLY || 'price_premium_240_yearly',

  // Add-on purchases (updated pricing: $5/100K tokens, $10/1000 credits)
  token_pack_100k: process.env.STRIPE_PRICE_TOKEN_PACK || 'price_token_pack_5',
  image_pack_1000: process.env.STRIPE_PRICE_IMAGE_PACK || 'price_image_pack_10',
} as const;

// Legacy price mapping for backward compatibility - will be deprecated
export const STRIPE_PRICES: Record<
  Exclude<SubscriptionPlan, 'free'>,
  string
> = {
  starter: STRIPE_PRICE_IDS.starter_monthly,
  premium: STRIPE_PRICE_IDS.premium_monthly,
};

export const stripe = new Stripe(STRIPE_API_KEY, {
  apiVersion: '2025-04-30.basil',
  typescript: true,
});

export async function createStripeCustomer(email: string, name?: string) {
  try {
    const customer = await stripe.customers.create({
      email,
      name: name || email,
    });

    return customer;
  } catch (error) {
    log.error({ err: error }, 'Error creating Stripe customer');
    throw error;
  }
}

export async function createStripeSession(
  customerId: string,
  plan: SubscriptionPlan,
  returnUrl: string,
  billingPeriod: 'monthly' | 'yearly' = 'monthly'
) {
  if (plan === 'free') {
    throw new Error('Cannot create subscription for free plan');
  }

  let priceId: string;

  if (billingPeriod === 'yearly') {
    priceId =
      plan === 'starter'
        ? STRIPE_PRICE_IDS.starter_yearly
        : STRIPE_PRICE_IDS.premium_yearly;
  } else {
    priceId = STRIPE_PRICES[plan];
  }

  if (!priceId) {
    throw new Error(`No price ID found for plan: ${plan} (${billingPeriod})`);
  }

  try {
    const session = await stripe.checkout.sessions.create({
      customer: customerId,
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: `${returnUrl}${returnUrl.includes('?') ? '&' : '?'}tab=billing&success=true`,
      cancel_url: `${returnUrl}${returnUrl.includes('?') ? '&' : '?'}tab=billing&canceled=true`,
    });

    return session;
  } catch (error) {
    log.error({ err: error }, 'Error creating Stripe session');
    throw error;
  }
}

export async function manageSubscription(
  customerId: string,
  returnUrl: string
) {
  try {
    const session = await stripe.billingPortal.sessions.create({
      customer: customerId,
      return_url: returnUrl,
    });

    return session;
  } catch (error) {
    log.error({ err: error }, 'Error creating billing portal session');
    throw error;
  }
}

export async function cancelSubscription(subscriptionId: string) {
  try {
    const subscription = await stripe.subscriptions.update(subscriptionId, {
      cancel_at_period_end: true,
    });

    return subscription;
  } catch (error) {
    log.error({ err: error }, 'Error canceling subscription');
    throw error;
  }
}

export async function reactivateSubscription(subscriptionId: string) {
  try {
    const subscription = await stripe.subscriptions.update(subscriptionId, {
      cancel_at_period_end: false,
    });

    return subscription;
  } catch (error) {
    log.error({ err: error }, 'Error reactivating subscription');
    throw error;
  }
}

export async function upgradeSubscription(
  subscriptionId: string,
  plan: SubscriptionPlan
) {
  if (plan === 'free') {
    throw new Error('Cannot upgrade to free plan');
  }

  const priceId = STRIPE_PRICES[plan];
  if (!priceId) {
    throw new Error(`No price ID found for plan: ${plan}`);
  }

  try {
    const subscription = await stripe.subscriptions.retrieve(subscriptionId);

    // Find the current item ID
    const itemId = subscription.items.data[0].id;

    // Update the subscription with the new price
    const updatedSubscription = await stripe.subscriptions.update(
      subscriptionId,
      {
        items: [
          {
            id: itemId,
            price: priceId,
          },
        ],
      }
    );

    return updatedSubscription;
  } catch (error) {
    log.error({ err: error }, 'Error upgrading subscription');
    throw error;
  }
}

export function constructEventFromPayload(signature: string, payload: Buffer) {
  try {
    const event = stripe.webhooks.constructEvent(
      payload,
      signature,
      STRIPE_WEBHOOK_SECRET
    );

    return event;
  } catch (error) {
    log.error({ err: error }, 'Error constructing webhook event');
    throw error;
  }
}
