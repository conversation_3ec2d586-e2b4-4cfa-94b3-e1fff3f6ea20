import {
  ATTR_SERVICE_NAME,
  ATTR_SERVICE_VERSION,
} from '@opentelemetry/semantic-conventions';
import {
  ATTR_SERVICE_NAMESPACE,
  ATTR_DEPLOYMENT_ENVIRONMENT_NAME,
} from '@opentelemetry/semantic-conventions/incubating';
import { NodeSDK } from '@opentelemetry/sdk-node';
import { resourceFromAttributes } from '@opentelemetry/resources';
import { getNodeAutoInstrumentations } from '@opentelemetry/auto-instrumentations-node';
import { OTLPTraceExporter } from '@opentelemetry/exporter-trace-otlp-http';
import { trace, Span, context } from '@opentelemetry/api';

//------------------------------------------------------------------------------
// Configuration Types & Defaults
//------------------------------------------------------------------------------

// Vendor-agnostic configuration types
interface TelemetryConfig {
  serviceName: string;
  serviceVersion?: string;
  environment: string;
  traceEndpoint?: string;
  headers?: Record<string, string>;
  sampleRate?: number;
  enabledInstrumentations?: string[];
  disabledInstrumentations?: string[];
}

// Default configuration that works with any OTEL backend
const DEFAULT_CONFIG: TelemetryConfig = {
  serviceName: 'sabi-chat',
  environment: process.env.NODE_ENV || 'development',
  sampleRate: 1.0, // 100% sampling in dev, should be lower in prod
  enabledInstrumentations: ['http', 'pg', 'undici', 'fs'],
  disabledInstrumentations: ['fs'], // Disable noisy instrumentations
};

function safeParseHeaders(value: string | undefined): Record<string, string> {
  if (!value) return {};

  // 1. Try JSON first
  try {
    return JSON.parse(value);
  } catch {
    /* fall through */
  }

  // 2. Fallback to OTLP “key=value,key2=value2” format
  return value.split(',').reduce<Record<string, string>>((acc, pair) => {
    const [k, ...rest] = pair.split('=');
    if (k && rest.length) acc[k.trim()] = rest.join('=').trim();
    return acc;
  }, {});
}

// Vendor-specific configurations (easily swappable)
const VENDOR_CONFIGS = {
  honeycomb: {
    traceEndpoint:
      process.env.HONEYCOMB_ENDPOINT || 'https://api.honeycomb.io/v1/traces',
    headers: {
      'x-honeycomb-team': process.env.HONEYCOMB_API_KEY || '',
      'x-honeycomb-dataset':
        process.env.HONEYCOMB_DATASET || 'sabi-chat-traces',
    },
  },
  datadog: {
    traceEndpoint:
      process.env.DATADOG_TRACE_ENDPOINT ||
      'https://trace.agent.datadoghq.com/v1/traces',
    headers: {
      'DD-API-KEY': process.env.DATADOG_API_KEY || '',
    },
  },
  jaeger: {
    traceEndpoint:
      process.env.JAEGER_ENDPOINT || 'http://localhost:14268/api/traces',
    headers: {},
  },
  // Generic OTLP endpoint - works with most backends
  otlp: {
    traceEndpoint:
      process.env.OTEL_EXPORTER_OTLP_ENDPOINT ||
      'http://localhost:4318/v1/traces',
    headers: safeParseHeaders(process.env.OTEL_EXPORTER_OTLP_HEADERS),
  },
};

// Build configuration based on environment
function buildTelemetryConfig(): TelemetryConfig {
  const config = { ...DEFAULT_CONFIG };

  // Set service version from environment
  config.serviceVersion =
    process.env.APP_VERSION || process.env.VERCEL_GIT_COMMIT_SHA || 'unknown';

  // Adjust sample rate for production
  if (process.env.NODE_ENV === 'production') {
    config.sampleRate = parseFloat(process.env.OTEL_TRACE_SAMPLE_RATE || '0.1');
  }

  // Get vendor-specific configuration
  const vendor = process.env.OTEL_VENDOR || 'honeycomb';
  const vendorConfig = VENDOR_CONFIGS[vendor as keyof typeof VENDOR_CONFIGS];

  if (vendorConfig) {
    config.traceEndpoint = vendorConfig.traceEndpoint;
    config.headers = vendorConfig.headers;
  }

  return config;
}

// Create the OpenTelemetry SDK with vendor-agnostic configuration
function createTelemetrySDK(): NodeSDK | null {
  const config = buildTelemetryConfig();

  // Skip if no endpoint configured
  if (!config.traceEndpoint) {
    console.warn(
      'No telemetry endpoint configured. Skipping OpenTelemetry initialization.'
    );
    return null;
  }

  // Skip if no API key/headers configured (except for development)
  if (
    config.environment === 'production' &&
    (!config.headers || Object.keys(config.headers).length === 0)
  ) {
    console.warn(
      'No telemetry headers configured for production. Skipping OpenTelemetry initialization.'
    );
    return null;
  }

  const traceExporter = new OTLPTraceExporter({
    url: config.traceEndpoint,
    headers: config.headers,
  });

  // Configure resource attributes (vendor-agnostic)
  const resource = resourceFromAttributes({
    [ATTR_SERVICE_NAME]: config.serviceName,
    [ATTR_SERVICE_VERSION]: config.serviceVersion,
    [ATTR_DEPLOYMENT_ENVIRONMENT_NAME]: config.environment,
    [ATTR_SERVICE_NAMESPACE]: 'sabi-chat',
    // Add custom attributes for better filtering
    'chat.app.name': 'sabi-chat',
    'chat.app.component': 'backend',
    'chat.app.instance': process.env.HOSTNAME || 'unknown',
  });

  // Configure instrumentations (vendor-agnostic)
  const instrumentations = getNodeAutoInstrumentations({
    '@opentelemetry/instrumentation-http': {
      ignoreIncomingRequestHook: (req) => {
        // Ignore health checks and monitoring endpoints
        const url = req.url || '';
        return (
          url.includes('/health') ||
          url.includes('/metrics') ||
          url.includes('/favicon.ico')
        );
      },
    },
    '@opentelemetry/instrumentation-pg': {
      enhancedDatabaseReporting: true,
    },
    '@opentelemetry/instrumentation-fs': {
      enabled: false, // Disable noisy file system instrumentation
    },
    '@opentelemetry/instrumentation-undici': {
      enabled: true,
    },
  });

  const sdk = new NodeSDK({
    resource,
    traceExporter,
    instrumentations,
    serviceName: config.serviceName,
  });

  return sdk;
}

// Initialize telemetry (call this once at startup)
export function initializeTelemetry(): void {
  try {
    const sdk = createTelemetrySDK();
    if (sdk) {
      sdk.start();
      console.log('OpenTelemetry initialized successfully');
    }
  } catch (error) {
    console.error('Failed to initialize OpenTelemetry:', error);
  }
}

// Utility functions for custom instrumentation (vendor-agnostic)
export function getTracer(name: string, version?: string) {
  return trace.getTracer(name, version);
}

// Custom span creation helpers
export function createSpan(
  tracerName: string,
  spanName: string,
  attributes?: Record<string, string | number | boolean>,
  parentSpan?: Span
) {
  const tracer = getTracer(tracerName);
  const span = tracer.startSpan(
    spanName,
    {
      attributes,
    },
    parentSpan ? trace.setSpan(context.active(), parentSpan) : undefined
  );

  return span;
}

// Performance timing utilities
export function recordSpanTiming(
  span: Span,
  operation: string,
  startTime: number,
  endTime?: number
) {
  const duration = (endTime || performance.now()) - startTime;
  span.setAttribute(`${operation}.duration_ms`, duration);
  return duration;
}

// LLM-specific span attributes (vendor-agnostic)
export function addLLMAttributes(
  span: Span,
  model: string,
  provider: string,
  promptTokens?: number,
  completionTokens?: number,
  cost?: number
) {
  span.setAttributes({
    'llm.model': model,
    'llm.provider': provider,
    'llm.tokens.prompt': promptTokens || 0,
    'llm.tokens.completion': completionTokens || 0,
    'llm.tokens.total': (promptTokens || 0) + (completionTokens || 0),
    'llm.cost_usd': cost || 0,
  });
}

// Database-specific span attributes
export function addDatabaseAttributes(
  span: Span,
  operation: string,
  table?: string,
  query?: string
) {
  span.setAttributes({
    'db.operation': operation,
    'db.table': table || 'unknown',
    'db.query': query || 'unknown',
  });
}

// HTTP-specific span attributes
export function addHTTPAttributes(
  span: Span,
  method: string,
  url: string,
  statusCode?: number,
  userAgent?: string
) {
  span.setAttributes({
    'http.method': method,
    'http.url': url,
    'http.status_code': statusCode || 0,
    'http.user_agent': userAgent || 'unknown',
  });
}

// Export the configuration for external use
export { buildTelemetryConfig };
