/**
 * Tests for error monitoring functionality
 */

import { AppError, handleError } from '../error';

// Mock console methods to avoid noise in tests
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

beforeEach(() => {
  console.error = jest.fn();
  console.warn = jest.fn();
});

afterEach(() => {
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;
});

describe('Error Monitoring', () => {
  describe('AppError', () => {
    it('should create an AppError with correct properties', () => {
      const error = new AppError('Test error', 'TEST_ERROR', 400);

      expect(error.message).toBe('Test error');
      expect(error.code).toBe('TEST_ERROR');
      expect(error.statusCode).toBe(400);
      expect(error.name).toBe('AppError');
    });

    it('should include metadata in error', () => {
      const metadata = { userId: '123', action: 'test' };
      const error = new AppError('Test error', 'TEST_ERROR', 400, undefined, metadata);

      expect(error.metadata).toEqual(metadata);
    });

    it('should serialize to JSON correctly', () => {
      const error = new AppError('Test error', 'TEST_ERROR', 400);
      const json = error.toJSON();

      expect(json).toEqual({
        name: 'AppError',
        message: 'Test error',
        code: 'TEST_ERROR',
        statusCode: 400,
        cause: undefined,
        metadata: {}
      });
    });
  });

  describe('handleError', () => {
    it('should return AppError instances unchanged', () => {
      const appError = new AppError('Test error', 'TEST_ERROR');
      const result = handleError(appError);

      expect(result).toBe(appError);
    });

    it('should wrap Error instances in AppError', () => {
      const originalError = new Error('Original error');
      const result = handleError(originalError);

      expect(result).toBeInstanceOf(AppError);
      expect(result.message).toBe('Original error');
      expect(result.code).toBe('INTERNAL_ERROR');
      expect(result.cause).toBe(originalError);
    });

    it('should handle string errors', () => {
      const result = handleError('String error');

      expect(result).toBeInstanceOf(AppError);
      expect(result.message).toBe('String error');
      expect(result.code).toBe('UNKNOWN_ERROR');
    });

    it('should handle unknown error types', () => {
      const result = handleError({ unknown: 'object' });

      expect(result).toBeInstanceOf(AppError);
      expect(result.message).toBe('An unknown error occurred');
      expect(result.code).toBe('UNKNOWN_ERROR');
    });
  });

  describe('Environment-based behavior', () => {
    const originalNodeEnv = process.env.NODE_ENV;

    afterEach(() => {
      process.env = { ...process.env, NODE_ENV: originalNodeEnv };
    });

    it('should not report errors in development environment', () => {
      process.env = { ...process.env, NODE_ENV: 'development' };

      // This should not throw or cause issues
      const error = new AppError('Dev error', 'DEV_ERROR');
      expect(error).toBeInstanceOf(AppError);
    });

    it('should handle production environment gracefully', () => {
      process.env = { ...process.env, NODE_ENV: 'production' };

      // This should not throw even if error monitoring services are not available
      const error = new AppError('Prod error', 'PROD_ERROR');
      expect(error).toBeInstanceOf(AppError);
    });
  });
});

describe('Error Monitoring Integration', () => {
  it('should not break application flow when error reporting fails', () => {
    // Simulate error reporting failure
    const originalNodeEnv = process.env.NODE_ENV;
        process.env = { ...process.env, NODE_ENV: 'production' };

    try {
      // This should complete successfully even if monitoring services fail
      const error = new AppError('Test error', 'TEST_ERROR');
      expect(error).toBeInstanceOf(AppError);

      const handledError = handleError(new Error('Test'));
      expect(handledError).toBeInstanceOf(AppError);
    } finally {
      process.env = { ...process.env, NODE_ENV: originalNodeEnv };
    }
  });
});
