import { UsageData, VerificationContext } from '@/hooks/usePurchaseVerification';

// Mock the verification logic functions (we'll extract them from the hook for testing)
interface VerificationResult {
  success: boolean;
  confidence: 'high' | 'medium' | 'low';
  updatedUsage: UsageData;
}

interface WebhookStatus {
  processed: boolean;
  recent_webhook_events: number;
}

// Extract the verification logic for unit testing
function determineVerificationResult(
  context: Pick<VerificationContext, 'purchaseType' | 'initialUsage'>,
  webhookStatus: WebhookStatus,
  updatedUsage: UsageData
): VerificationResult {
  const hasWebhookEvents = webhookStatus.processed && webhookStatus.recent_webhook_events > 0;
  
  let usageIncreased = false;
  let confidence: 'high' | 'medium' | 'low' = 'low';

  if (context.purchaseType === 'tokens') {
    const initialRemaining = context.initialUsage.tokens?.remaining || 0;
    const updatedRemaining = updatedUsage.tokens?.remaining || 0;
    usageIncreased = updatedRemaining > initialRemaining;
  } else if (context.purchaseType === 'images') {
    const initialRemaining = context.initialUsage.images?.remaining || 0;
    const updatedRemaining = updatedUsage.images?.remaining || 0;
    usageIncreased = updatedRemaining > initialRemaining;
  }

  // Determine confidence and success
  if (hasWebhookEvents && usageIncreased) {
    confidence = 'high';
    return { success: true, confidence, updatedUsage };
  } else if (hasWebhookEvents && !usageIncreased) {
    // Webhook processed but no usage increase - likely processed before initial fetch
    confidence = 'high';
    return { success: true, confidence, updatedUsage };
  } else if (!hasWebhookEvents && usageIncreased) {
    // Usage increased but no webhook events - possible timing issue
    confidence = 'medium';
    return { success: true, confidence, updatedUsage };
  } else if (!hasWebhookEvents && !usageIncreased) {
    // No webhook events and no usage increase - likely failed
    confidence = 'high';
    return { success: false, confidence, updatedUsage };
  }

  // Fallback case
  confidence = 'low';
  return { success: false, confidence, updatedUsage };
}

describe('Purchase Verification Logic', () => {
  const baseUsageData: UsageData = {
    tokens: { used: 1000, remaining: 5000, total: 6000, resetDate: '2024-01-01' },
    images: { used: 10, remaining: 90, total: 100, resetDate: '2024-01-01' },
  };

  const increasedTokensUsage: UsageData = {
    tokens: { used: 1000, remaining: 105000, total: 106000, resetDate: '2024-01-01' },
    images: { used: 10, remaining: 90, total: 100, resetDate: '2024-01-01' },
  };

  const increasedImagesUsage: UsageData = {
    tokens: { used: 1000, remaining: 5000, total: 6000, resetDate: '2024-01-01' },
    images: { used: 10, remaining: 1090, total: 1100, resetDate: '2024-01-01' },
  };

  describe('Token purchase verification', () => {
    const tokenContext = {
      purchaseType: 'tokens' as const,
      initialUsage: baseUsageData,
    };

    test('should return high confidence success when webhook processed and tokens increased', () => {
      const webhookStatus = { processed: true, recent_webhook_events: 1 };
      const result = determineVerificationResult(tokenContext, webhookStatus, increasedTokensUsage);

      expect(result).toEqual({
        success: true,
        confidence: 'high',
        updatedUsage: increasedTokensUsage,
      });
    });

    test('should return high confidence success when webhook processed but tokens not increased (early processing)', () => {
      const webhookStatus = { processed: true, recent_webhook_events: 1 };
      const result = determineVerificationResult(tokenContext, webhookStatus, baseUsageData);

      expect(result).toEqual({
        success: true,
        confidence: 'high',
        updatedUsage: baseUsageData,
      });
    });

    test('should return medium confidence success when tokens increased but no webhook', () => {
      const webhookStatus = { processed: false, recent_webhook_events: 0 };
      const result = determineVerificationResult(tokenContext, webhookStatus, increasedTokensUsage);

      expect(result).toEqual({
        success: true,
        confidence: 'medium',
        updatedUsage: increasedTokensUsage,
      });
    });

    test('should return high confidence failure when no webhook and no token increase', () => {
      const webhookStatus = { processed: false, recent_webhook_events: 0 };
      const result = determineVerificationResult(tokenContext, webhookStatus, baseUsageData);

      expect(result).toEqual({
        success: false,
        confidence: 'high',
        updatedUsage: baseUsageData,
      });
    });

    test('should handle partial webhook status (processed but no events)', () => {
      const webhookStatus = { processed: true, recent_webhook_events: 0 };
      const result = determineVerificationResult(tokenContext, webhookStatus, baseUsageData);

      // When processed is true but no events, hasWebhookEvents is false, 
      // and no usage increase means it falls into the failure case
      expect(result).toEqual({
        success: false,
        confidence: 'high',
        updatedUsage: baseUsageData,
      });
    });
  });

  describe('Image purchase verification', () => {
    const imageContext = {
      purchaseType: 'images' as const,
      initialUsage: baseUsageData,
    };

    test('should return high confidence success when webhook processed and images increased', () => {
      const webhookStatus = { processed: true, recent_webhook_events: 1 };
      const result = determineVerificationResult(imageContext, webhookStatus, increasedImagesUsage);

      expect(result).toEqual({
        success: true,
        confidence: 'high',
        updatedUsage: increasedImagesUsage,
      });
    });

    test('should return high confidence success when webhook processed but images not increased', () => {
      const webhookStatus = { processed: true, recent_webhook_events: 1 };
      const result = determineVerificationResult(imageContext, webhookStatus, baseUsageData);

      expect(result).toEqual({
        success: true,
        confidence: 'high',
        updatedUsage: baseUsageData,
      });
    });

    test('should return medium confidence success when images increased but no webhook', () => {
      const webhookStatus = { processed: false, recent_webhook_events: 0 };
      const result = determineVerificationResult(imageContext, webhookStatus, increasedImagesUsage);

      expect(result).toEqual({
        success: true,
        confidence: 'medium',
        updatedUsage: increasedImagesUsage,
      });
    });

    test('should return high confidence failure when no webhook and no image increase', () => {
      const webhookStatus = { processed: false, recent_webhook_events: 0 };
      const result = determineVerificationResult(imageContext, webhookStatus, baseUsageData);

      expect(result).toEqual({
        success: false,
        confidence: 'high',
        updatedUsage: baseUsageData,
      });
    });
  });

  describe('Edge cases', () => {
    test('should handle missing usage data gracefully', () => {
      const tokenContext = {
        purchaseType: 'tokens' as const,
        initialUsage: {} as UsageData,
      };
      const webhookStatus = { processed: true, recent_webhook_events: 1 };
      const emptyUsage = {} as UsageData;

      const result = determineVerificationResult(tokenContext, webhookStatus, emptyUsage);

      expect(result.success).toBe(true);
      expect(result.confidence).toBe('high');
    });

    test('should handle undefined remaining values', () => {
      const tokenContext = {
        purchaseType: 'tokens' as const,
        initialUsage: {
          tokens: { used: 1000, remaining: 0, total: 1000, resetDate: '2024-01-01' },
        },
      };
      const webhookStatus = { processed: false, recent_webhook_events: 0 };
      const updatedUsage = {
        tokens: { used: 1000, remaining: 100000, total: 101000, resetDate: '2024-01-01' },
      };

      const result = determineVerificationResult(tokenContext, webhookStatus, updatedUsage);

      expect(result).toEqual({
        success: true,
        confidence: 'medium',
        updatedUsage,
      });
    });

    test('should handle webhook with multiple events', () => {
      const tokenContext = {
        purchaseType: 'tokens' as const,
        initialUsage: baseUsageData,
      };
      const webhookStatus = { processed: true, recent_webhook_events: 3 };

      const result = determineVerificationResult(tokenContext, webhookStatus, increasedTokensUsage);

      expect(result).toEqual({
        success: true,
        confidence: 'high',
        updatedUsage: increasedTokensUsage,
      });
    });

    test('should handle usage decrease (unexpected scenario)', () => {
      const tokenContext = {
        purchaseType: 'tokens' as const,
        initialUsage: increasedTokensUsage, // Start with high tokens
      };
      const webhookStatus = { processed: true, recent_webhook_events: 1 };
      const decreasedUsage = baseUsageData; // End with fewer tokens

      const result = determineVerificationResult(tokenContext, webhookStatus, decreasedUsage);

      // Should still succeed because webhook was processed
      expect(result).toEqual({
        success: true,
        confidence: 'high',
        updatedUsage: decreasedUsage,
      });
    });
  });

  describe('Confidence scoring scenarios', () => {
    test('should assign high confidence to webhook-verified scenarios', () => {
      const contexts = [
        { purchaseType: 'tokens' as const, initialUsage: baseUsageData },
        { purchaseType: 'images' as const, initialUsage: baseUsageData },
      ];

      contexts.forEach(context => {
        const webhookStatus = { processed: true, recent_webhook_events: 1 };
        const result = determineVerificationResult(context, webhookStatus, baseUsageData);
        expect(result.confidence).toBe('high');
      });
    });

    test('should assign medium confidence to usage-only verification', () => {
      const tokenContext = {
        purchaseType: 'tokens' as const,
        initialUsage: baseUsageData,
      };
      const webhookStatus = { processed: false, recent_webhook_events: 0 };
      
      const result = determineVerificationResult(tokenContext, webhookStatus, increasedTokensUsage);
      expect(result.confidence).toBe('medium');
    });

    test('should assign high confidence to clear failures', () => {
      const tokenContext = {
        purchaseType: 'tokens' as const,
        initialUsage: baseUsageData,
      };
      const webhookStatus = { processed: false, recent_webhook_events: 0 };
      
      const result = determineVerificationResult(tokenContext, webhookStatus, baseUsageData);
      expect(result.confidence).toBe('high');
      expect(result.success).toBe(false);
    });
  });

  describe('Webhook processed before initial fetch scenario', () => {
    test('should handle webhook processed before usage data fetch', () => {
      // Simulate scenario where webhook processed purchase before we fetched initial usage
      const tokenContext = {
        purchaseType: 'tokens' as const,
        initialUsage: increasedTokensUsage, // Already includes purchase
      };
      const webhookStatus = { processed: true, recent_webhook_events: 1 };
      const currentUsage = increasedTokensUsage; // Same as initial

      const result = determineVerificationResult(tokenContext, webhookStatus, currentUsage);

      expect(result).toEqual({
        success: true,
        confidence: 'high',
        updatedUsage: currentUsage,
      });
    });
  });

  describe('Usage data changes correctly scenarios', () => {
    test('should detect token usage increase correctly', () => {
      const initialTokens = 5000;
      const updatedTokens = 105000;
      
      const tokenContext = {
        purchaseType: 'tokens' as const,
        initialUsage: {
          tokens: { used: 1000, remaining: initialTokens, total: initialTokens + 1000, resetDate: '2024-01-01' },
        },
      };
      
      const updatedUsage = {
        tokens: { used: 1000, remaining: updatedTokens, total: updatedTokens + 1000, resetDate: '2024-01-01' },
      };

      const webhookStatus = { processed: false, recent_webhook_events: 0 };
      const result = determineVerificationResult(tokenContext, webhookStatus, updatedUsage);

      expect(result.success).toBe(true);
      expect(result.confidence).toBe('medium');
    });

    test('should detect image usage increase correctly', () => {
      const initialImages = 90;
      const updatedImages = 1090;
      
      const imageContext = {
        purchaseType: 'images' as const,
        initialUsage: {
          images: { used: 10, remaining: initialImages, total: initialImages + 10, resetDate: '2024-01-01' },
        },
      };
      
      const updatedUsage = {
        images: { used: 10, remaining: updatedImages, total: updatedImages + 10, resetDate: '2024-01-01' },
      };

      const webhookStatus = { processed: false, recent_webhook_events: 0 };
      const result = determineVerificationResult(imageContext, webhookStatus, updatedUsage);

      expect(result.success).toBe(true);
      expect(result.confidence).toBe('medium');
    });
  });
});