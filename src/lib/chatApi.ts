import {
  AttachmentPayload,
  UrlCitationAnnotation,
  StreamMetadataClientData,
} from '@/lib/supabase/types';

// ------------------------------------------------------------
// Feature flag names
// ------------------------------------------------------------
export const IS_ADMIN_FLAG = 'is-admin';

// ------------------------------------------------------------
// Shared types
// ------------------------------------------------------------
export interface ChatInitParams {
  /** Content of the new user message. Optional for retry/edit flows. */
  message?: string;
  model: string;
  conversationId?: string;
  comparisonIndex?: number | null;
  /** Indicates if the request is part of a side-by-side comparison. */
  isComparison?: boolean;
  /** Current group conversation ID (required for new conversations). */
  groupConversationId?: string;
  isTestMode: boolean;
  parentMessageId?: string;
  attachments?: AttachmentPayload[];
  useWebSearch?: boolean;
  useImageGeneration?: boolean;
  /** Marks flows where messages are stored only temporarily. */
  isTemporary?: boolean;
  workspaceId?: string;

  // --- Retry / Edit specific fields ---
  /** ID of the original user message being retried or edited. */
  messageId?: string;
  /** Replacement content for an edit operation. */
  newContent?: string;
  /** Distinguishes edit vs retry when messageId is present. */
  isEdit?: boolean;
}

export interface InitSseResponse {
  conversationId: string;
  groupConversationId: string;
  assistantMessageId: string;
  userMessageId: string;
  isNewConversation: boolean;
  isTemporary: boolean;
  workspaceId?: string;
  /* unified-only field */
  tokenReservationId?: string | null;
}

// ------------------------------------------------------------
// Unified streaming helper (POST-based SSE)
// ------------------------------------------------------------
export interface UnifiedStreamCallbacks {
  onDelta: (text: string, replace?: boolean, attachments?: unknown[]) => void;
  onAnnotation?: (a: UrlCitationAnnotation) => void;
  onMetadata?: (m: StreamMetadataClientData) => void;
  onInit?: (data: InitSseResponse) => void;
  onError?: (err: Error) => void;
  onDone?: () => void;
}

/**
 * Open an SSE stream via POST /api/chat/stream.
 * Returns an abort function the caller can invoke to cancel.
 */
export function createUnifiedStream(
  params: ChatInitParams,
  callbacks: UnifiedStreamCallbacks
): () => void {
  const controller = new AbortController();
  const decoder = new TextDecoder();
  let buffer = '';

  fetch('/api/chat/stream', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(params),
    signal: controller.signal,
  })
    .then((res) => {
      if (!res.ok || !res.body) {
        throw new Error(`Stream failed: ${res.status} – ${res.statusText}`);
      }
      const reader = res.body.getReader();

      const pump = (): void => {
        reader
          .read()
          .then(({ value, done }) => {
            if (done) {
              callbacks.onDone?.();
              return;
            }
            buffer += decoder.decode(value, { stream: true });

            let sep: number;
            while ((sep = buffer.indexOf('\n\n')) !== -1) {
              const raw = buffer.slice(0, sep).trim();
              buffer = buffer.slice(sep + 2);

              if (!raw) continue;

              let eventType = 'message';
              let dataStr = '';
              for (const line of raw.split('\n')) {
                if (line.startsWith('event:')) {
                  eventType = line.slice(6).trim();
                } else if (line.startsWith('data:')) {
                  dataStr += line.slice(5).trim();
                }
              }
              if (!dataStr) continue;

              try {
                const payload = JSON.parse(dataStr);
                switch (eventType) {
                  case 'init':
                    callbacks.onInit?.(payload);
                    break;
                  case 'delta':
                    callbacks.onDelta(
                      payload.content,
                      payload.replace,
                      payload.attachments
                    );
                    break;
                  case 'annotation':
                    if (callbacks.onAnnotation)
                      callbacks.onAnnotation(payload.annotation);
                    break;
                  case 'metadata':
                    callbacks.onMetadata?.(payload.metadata);
                    break;
                  case 'error':
                    callbacks.onError?.(
                      new Error(payload.error?.message || 'stream error')
                    );
                    break;
                  case 'done':
                  case 'complete':
                    callbacks.onDone?.();
                    break;
                  default:
                  // ignore unknown event types
                }
              } catch (err) {
                console.warn('Failed to parse SSE line', err);
              }
            }
            pump();
          })
          .catch((err) => callbacks.onError?.(err));
      };
      pump();
    })
    .catch((err) => callbacks.onError?.(err));

  return () => controller.abort();
}

// ------------------------------------------------------------
// End of chatApi helper
// ------------------------------------------------------------
