import {
  AttachmentPayload,
  UrlCitationAnnotation,
  StreamMetadataClientData,
} from '@/lib/supabase/types';

// ------------------------------------------------------------
// Feature flag for unified streaming
// ------------------------------------------------------------
const USE_UNIFIED_STREAM =
  typeof window !== 'undefined'
    ? localStorage.getItem('use_unified_stream') === 'true'
    : process.env.NEXT_PUBLIC_USE_UNIFIED_STREAM === 'true';

// ------------------------------------------------------------
// Shared types
// ------------------------------------------------------------
export interface ChatInitParams {
  /** Content of the new user message. Optional for retry/edit flows. */
  message?: string;
  model: string;
  conversationId?: string;
  comparisonIndex?: number | null;
  /** Indicates if the request is part of a side-by-side comparison. */
  isComparison?: boolean;
  /** Current group conversation ID (required for new conversations). */
  groupConversationId?: string;
  isTestMode: boolean;
  parentMessageId?: string;
  attachments?: AttachmentPayload[];
  useWebSearch?: boolean;
  useImageGeneration?: boolean;
  /** Marks flows where messages are stored only temporarily. */
  isTemporary?: boolean;
  workspaceId?: string;

  // --- Retry / Edit specific fields ---
  /** ID of the original user message being retried or edited. */
  messageId?: string;
  /** Replacement content for an edit operation. */
  newContent?: string;
  /** Distinguishes edit vs retry when messageId is present. */
  isEdit?: boolean;
}

export interface InitSseResponse {
  conversationId: string;
  groupConversationId: string;
  assistantMessageId: string;
  userMessageId: string;
  isNewConversation: boolean;
  isTemporary: boolean;
  workspaceId?: string;
  /* unified-only field */
  tokenReservationId?: string | null;
}

export interface ChatUpdateParams {
  messageId: string;
  model: string;
  isTestMode: boolean;
  newContent?: string;
  isEdit: boolean;
  useWebSearch: boolean;
  useImageGeneration: boolean;
}

export interface UpdateSseResponse {
  conversationId?: string;
  assistantMessageId: string;
}

// ------------------------------------------------------------
// Legacy dual-request helpers (init / update / events URL)
// ------------------------------------------------------------
export async function initSse(
  params: ChatInitParams
): Promise<InitSseResponse> {
  if (USE_UNIFIED_STREAM) {
    // In unified mode the stream endpoint handles init. Return placeholders.
    return {
      conversationId: params.conversationId || 'pending',
      groupConversationId: params.groupConversationId || 'pending',
      assistantMessageId: 'pending',
      userMessageId: 'pending',
      isNewConversation: !params.conversationId,
      isTemporary: params.isTemporary ?? false,
      workspaceId: params.workspaceId,
    };
  }

  const res = await fetch('/api/chat/stream-sse/init', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(params),
  });
  if (!res.ok) {
    throw new Error(`Error initializing SSE chat: ${res.statusText}`);
  }
  return res.json();
}

export async function updateSse(
  params: ChatUpdateParams
): Promise<UpdateSseResponse> {
  if (USE_UNIFIED_STREAM) {
    throw new Error('Updates not yet supported in unified stream mode');
  }

  const res = await fetch('/api/chat/stream-sse/init', {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(params),
  });
  if (!res.ok) {
    throw new Error(`Error updating SSE chat: ${res.statusText}`);
  }
  return res.json();
}

export function createStreamUrl(
  assistantMessageId: string,
  conversationId: string,
  model: string,
  isTestMode: boolean,
  useWebSearch: boolean,
  useImageGeneration: boolean,
  tokenReservationId?: string,
  workspaceId?: string
): string {
  if (USE_UNIFIED_STREAM) {
    // handled by POST /stream
    return '';
  }

  const params = new URLSearchParams({
    assistantMessageId,
    conversationId,
    model,
    isTestMode: String(isTestMode),
    useWebSearch: String(useWebSearch),
    useImageGeneration: String(useImageGeneration),
  });
  if (tokenReservationId) params.set('tokenReservationId', tokenReservationId);
  if (workspaceId) params.set('workspaceId', workspaceId);
  return `/api/chat/stream-sse/events?${params}`;
}

export function isUnifiedStreamEnabled(): boolean {
  return USE_UNIFIED_STREAM;
}

// ------------------------------------------------------------
// Unified streaming helper (POST-based SSE)
// ------------------------------------------------------------
export interface UnifiedStreamCallbacks {
  onDelta: (text: string, replace?: boolean, attachments?: unknown[]) => void;
  onAnnotation?: (a: UrlCitationAnnotation) => void;
  onMetadata?: (m: StreamMetadataClientData) => void;
  onInit?: (data: InitSseResponse) => void;
  onError?: (err: Error) => void;
  onDone?: () => void;
}

/**
 * Open an SSE stream via POST /api/chat/stream.
 * Returns an abort function the caller can invoke to cancel.
 */
export function createUnifiedStream(
  params: ChatInitParams,
  callbacks: UnifiedStreamCallbacks
): () => void {
  if (!USE_UNIFIED_STREAM) {
    throw new Error('Unified streaming is disabled');
  }

  const controller = new AbortController();
  const decoder = new TextDecoder();
  let buffer = '';

  fetch('/api/chat/stream', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(params),
    signal: controller.signal,
  })
    .then((res) => {
      if (!res.ok || !res.body) {
        throw new Error(`Stream failed: ${res.status} – ${res.statusText}`);
      }
      const reader = res.body.getReader();

      const pump = (): void => {
        reader
          .read()
          .then(({ value, done }) => {
            if (done) {
              callbacks.onDone?.();
              return;
            }
            buffer += decoder.decode(value, { stream: true });

            let sep: number;
            while ((sep = buffer.indexOf('\n\n')) !== -1) {
              const raw = buffer.slice(0, sep).trim();
              buffer = buffer.slice(sep + 2);

              if (!raw) continue;

              let eventType = 'message';
              let dataStr = '';
              for (const line of raw.split('\n')) {
                if (line.startsWith('event:')) {
                  eventType = line.slice(6).trim();
                } else if (line.startsWith('data:')) {
                  dataStr += line.slice(5).trim();
                }
              }
              if (!dataStr) continue;

              try {
                const payload = JSON.parse(dataStr);
                switch (eventType) {
                  case 'init':
                    callbacks.onInit?.(payload);
                    break;
                  case 'delta':
                    callbacks.onDelta(
                      payload.content,
                      payload.replace,
                      payload.attachments
                    );
                    break;
                  case 'annotation':
                    if (callbacks.onAnnotation)
                      callbacks.onAnnotation(payload.annotation);
                    break;
                  case 'metadata':
                    callbacks.onMetadata?.(payload.metadata);
                    break;
                  case 'error':
                    callbacks.onError?.(
                      new Error(payload.error?.message || 'stream error')
                    );
                    break;
                  case 'done':
                  case 'complete':
                    callbacks.onDone?.();
                    break;
                  default:
                  // ignore unknown event types
                }
              } catch (err) {
                console.warn('Failed to parse SSE line', err);
              }
            }
            pump();
          })
          .catch((err) => callbacks.onError?.(err));
      };
      pump();
    })
    .catch((err) => callbacks.onError?.(err));

  return () => controller.abort();
}

// ------------------------------------------------------------
// End of chatApi helper
// ------------------------------------------------------------
