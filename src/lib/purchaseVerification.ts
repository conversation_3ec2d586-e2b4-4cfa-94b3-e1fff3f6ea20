import { UsageData } from '@/hooks/usePurchaseVerification';

export interface VerificationResult {
  success: boolean;
  confidence: 'high' | 'medium' | 'low';
  reason: string;
}

export interface WebhookStatus {
  processed: boolean;
  recent_webhook_events: number;
}

export interface VerificationInputs {
  purchaseType: 'tokens' | 'images';
  initialUsage: UsageData;
  updatedUsage: UsageData;
  webhookStatus: WebhookStatus;
  timeSinceStart: number;
}

/**
 * Enhanced verification algorithm that uses multiple indicators to determine
 * if a purchase was successfully processed, avoiding false positives.
 */
export function verifyPurchaseSuccess(
  inputs: VerificationInputs
): VerificationResult {
  const {
    purchaseType,
    initialUsage,
    updatedUsage,
    webhookStatus,
    timeSinceStart,
  } = inputs;

  const hasWebhookEvents =
    webhookStatus.processed && webhookStatus.recent_webhook_events > 0;

  // Calculate usage changes
  let usageIncreased = false;
  let initialRemaining = 0;
  let updatedRemaining = 0;

  if (purchaseType === 'tokens') {
    initialRemaining = initialUsage.tokens?.remaining || 0;
    updatedRemaining = updatedUsage.tokens?.remaining || 0;
    usageIncreased = updatedRemaining > initialRemaining;
  } else if (purchaseType === 'images') {
    initialRemaining = initialUsage.images?.remaining || 0;
    updatedRemaining = updatedUsage.images?.remaining || 0;
    usageIncreased = updatedRemaining > initialRemaining;
  }

  const usageChange = updatedRemaining - initialRemaining;

  // Scenario 1: Webhook processed AND usage increased
  if (hasWebhookEvents && usageIncreased) {
    return {
      success: true,
      confidence: 'high',
      reason: `Webhook processed and ${purchaseType} increased by ${usageChange}`,
    };
  }

  // Scenario 2: Webhook processed but no usage increase
  // This likely means the webhook processed before we fetched initial usage
  if (hasWebhookEvents && !usageIncreased) {
    return {
      success: true,
      confidence: 'high',
      reason:
        'Webhook processed successfully (credits likely added before initial check)',
    };
  }

  // Scenario 3: No webhook events but usage increased
  // Possible timing issue or webhook delay
  if (!hasWebhookEvents && usageIncreased) {
    const confidence = timeSinceStart > 10000 ? 'medium' : 'low'; // More confident after 10 seconds
    return {
      success: true,
      confidence,
      reason: `${purchaseType} increased by ${usageChange} (webhook may be delayed)`,
    };
  }

  // Scenario 4: No webhook events and no usage increase
  // Likely a genuine failure, but confidence depends on timing
  if (!hasWebhookEvents && !usageIncreased) {
    const confidence = timeSinceStart > 15000 ? 'high' : 'medium'; // More confident after 15 seconds
    return {
      success: false,
      confidence,
      reason: 'No webhook events detected and no usage increase observed',
    };
  }

  // Fallback case (shouldn't reach here)
  return {
    success: false,
    confidence: 'low',
    reason: 'Unable to determine purchase status',
  };
}

/**
 * Determines appropriate error message based on verification result
 */
export function getErrorMessage(
  result: VerificationResult,
  purchaseType: 'tokens' | 'images'
): string {
  if (result.success) {
    return ''; // No error
  }

  const itemName = purchaseType === 'tokens' ? 'tokens' : 'image credits';

  switch (result.confidence) {
    case 'high':
      return `Purchase completed but ${itemName} may not have been added due to a processing error. Please contact support if your ${itemName} don't appear within 5 minutes.`;

    case 'medium':
      return `Unable to confirm if ${itemName} were added. Please check your usage or contact support if your ${itemName} don't appear within 10 minutes.`;

    case 'low':
    default:
      return `Unable to verify purchase status. Please check your usage or contact support if your ${itemName} don't appear within 10 minutes.`;
  }
}

/**
 * Calculates retry delay with exponential backoff
 */
export function getRetryDelay(
  attempt: number,
  baseDelay: number = 2000
): number {
  return Math.min(baseDelay * Math.pow(2, attempt - 1), 10000); // Cap at 10 seconds
}

/**
 * Determines if we should retry based on error type and attempt count
 */
export function shouldRetry(
  error: Error,
  attempt: number,
  maxAttempts: number
): boolean {
  if (attempt >= maxAttempts) {
    return false;
  }

  // Retry on network errors, timeouts, and server errors
  const retryableErrors = [
    'Failed to fetch',
    'NetworkError',
    'TimeoutError',
    'AbortError',
  ];

  return retryableErrors.some(
    (errorType) =>
      error.message.includes(errorType) || error.name.includes(errorType)
  );
}
