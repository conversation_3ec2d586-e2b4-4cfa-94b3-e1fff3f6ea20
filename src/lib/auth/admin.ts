import { createClient } from '@/utils/supabase/server';
import { User } from '@supabase/supabase-js';
import { logger } from '@/lib/logger';
import { AppError, ErrorCode } from '@/lib/error';
import { FeatureFlagService } from '@/lib/feature-flags/service';

const log = logger.child({ module: 'AdminAuth' });

/**
 * List of admin email addresses from environment variables
 * Format: ADMIN_EMAILS=<EMAIL>,<EMAIL>
 */
const ADMIN_EMAILS =
  process.env.ADMIN_EMAILS?.split(',').map((email) => email.trim()) || [];

// Name of the feature flag that grants admin privileges
const ADMIN_FEATURE_FLAG = 'is-admin';

/**
 * Checks if a user is an admin based on their email address
 * @param user - The Supabase user object
 * @returns boolean indicating if the user is an admin
 */
export function isAdminUser(user: User): boolean {
  if (!user.email) {
    return false;
  }

  return ADMIN_EMAILS.includes(user.email);
}

/**
 * Checks if a user email is an admin
 * @param email - The user's email address
 * @returns boolean indicating if the email is an admin
 */
export function isAdminEmail(email: string): boolean {
  return ADMIN_EMAILS.includes(email);
}

/**
 * Middleware function to check if the current authenticated user is an admin
 * Throws an error if the user is not authenticated or not an admin
 * @returns Promise<User> - The authenticated admin user
 * @throws {AppError} - If user is not authenticated or not an admin
 */
export async function requireAdmin(): Promise<User> {
  const supabase = await createClient();

  const {
    data: { user },
    error,
  } = await supabase.auth.getUser();

  if (error) {
    log.error({ err: error }, 'Error getting user for admin check');
    throw new AppError('Authentication error', ErrorCode.API_UNAUTHORIZED, 401);
  }

  if (!user) {
    log.warn('Unauthenticated user attempted admin action');
    throw new AppError('Authentication required', ErrorCode.API_UNAUTHORIZED, 401);
  }

  // New: Check email list OR feature flag
  let isAuthorized = isAdminUser(user);

  if (!isAuthorized) {
    try {
      const featureFlagService = await FeatureFlagService.getInstance();
      isAuthorized = await featureFlagService.isFeatureEnabled(ADMIN_FEATURE_FLAG, user.id);
    } catch (ffErr) {
      // Log but don't fail feature flag lookup – we handle below
      log.error({ err: ffErr }, 'Error evaluating admin feature flag');
    }
  }

  if (!isAuthorized) {
    log.warn(
      { userId: user.id, email: user.email },
      'Non-admin user attempted admin action'
    );
    throw new AppError('Admin access required', ErrorCode.API_UNAUTHORIZED, 403);
  }

  log.info(
    { userId: user.id, email: user.email },
    'Admin user authenticated successfully'
  );

  return user;
}

/**
 * Checks if the current authenticated user is an admin without throwing
 * @returns Promise<{isAdmin: boolean, user: User | null}> - Admin status and user
 */
export async function checkAdminStatus(): Promise<{
  isAdmin: boolean;
  user: User | null;
}> {
  try {
    const supabase = await createClient();

    const {
      data: { user },
      error,
    } = await supabase.auth.getUser();

    if (error || !user) {
      return { isAdmin: false, user: null };
    }

    let isAdmin = isAdminUser(user);

    if (!isAdmin) {
      try {
        const featureFlagService = await FeatureFlagService.getInstance();
        isAdmin = await featureFlagService.isFeatureEnabled(ADMIN_FEATURE_FLAG, user.id);
      } catch (ffErr) {
        log.error({ err: ffErr }, 'Error evaluating admin feature flag');
      }
    }

    return {
      isAdmin,
      user,
    };
  } catch (error) {
    log.error({ err: error }, 'Error checking admin status');
    return { isAdmin: false, user: null };
  }
}

/**
 * Gets the list of admin emails (for debugging/testing purposes)
 * Only returns the list if called by an admin user
 * @returns Promise<string[]> - List of admin emails
 * @throws {AppError} - If user is not an admin
 */
export async function getAdminEmails(): Promise<string[]> {
  await requireAdmin(); // Ensure only admins can see this
  return ADMIN_EMAILS;
}

/**
 * Logs admin actions for audit purposes
 * @param action - The action being performed
 * @param details - Additional details about the action
 * @param user - The admin user performing the action
 */
export function logAdminAction(
  action: string,
  details: Record<string, unknown>,
  user: User
): void {
  log.info(
    {
      action,
      adminUserId: user.id,
      adminEmail: user.email,
      ...details,
    },
    `Admin action: ${action}`
  );
}

/**
 * Validates admin configuration on startup
 * Logs warnings if no admin emails are configured
 */
export function validateAdminConfig(): void {
  if (ADMIN_EMAILS.length === 0) {
    log.warn(
      'No admin emails configured. Set ADMIN_EMAILS environment variable to enable admin features.'
    );
  } else {
    log.info(
      { adminCount: ADMIN_EMAILS.length },
      'Admin configuration loaded successfully'
    );
  }
}

// Initialize admin config validation
validateAdminConfig();
