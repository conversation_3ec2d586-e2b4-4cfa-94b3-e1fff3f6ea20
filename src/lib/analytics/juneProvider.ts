import { AnalyticsBrowser } from '@june-so/analytics-next';
import { AnalyticsProvider, UserTraits, EventProperties } from './provider';

export class JuneAnalyticsProvider implements AnalyticsProvider {
  private analytics: AnalyticsBrowser | null = null;

  init() {
    // Only initialize analytics in production environment
    if (process.env.NODE_ENV !== 'production') {
      console.log('Analytics disabled in development environment');
      return;
    }

    // Read the key at runtime to allow for proper testing
    const JUNE_WRITE_KEY = process.env.NEXT_PUBLIC_JUNE_WRITE_KEY;
    if (!JUNE_WRITE_KEY) {
      console.warn('June Analytics Write Key not found. Analytics disabled.');
      return;
    }

    if (!this.analytics) {
      // Initialize June SDK
      this.analytics = AnalyticsBrowser.load({ writeKey: JUNE_WRITE_KEY });
    }
  }

  identify(userId: string, traits: UserTraits) {
    // Only track in production
    if (process.env.NODE_ENV !== 'production' || !this.analytics) return;
    this.analytics.identify(userId, traits);
  }

  track(eventName: string, properties?: EventProperties) {
    // Only track in production
    if (process.env.NODE_ENV !== 'production' || !this.analytics) return;
    this.analytics.track(eventName, properties);
  }

  reset() {
    // Only reset in production
    if (process.env.NODE_ENV !== 'production' || !this.analytics) return;
    this.analytics.reset();
  }
}
