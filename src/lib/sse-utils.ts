// ---------------------------------------------------------------------------------
// Shared SSE utilities
// ---------------------------------------------------------------------------------
// Using single instances of TextEncoder/TextDecoder across requests saves a bit of
// CPU time by avoiding re-allocation in each SSE call. They are
// stateless so it's perfectly safe to share them.
export const sseEncoder = new TextEncoder();
export const sseDecoder = new TextDecoder();