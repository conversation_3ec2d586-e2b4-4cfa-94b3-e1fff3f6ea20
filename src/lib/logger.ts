import pino, { Logger } from 'pino';

const getLogLevel = (): string => {
  const levels =
    process.env.LOGGING_LEVELS?.split(',').map((l) => l.trim().toLowerCase()) ||
    [];

  if (levels.includes('none')) return 'silent';
  if (levels.includes('all')) return 'trace';
  if (levels.includes('error')) return 'error';
  if (levels.includes('warn')) return 'warn';
  if (levels.includes('info')) return 'info';
  if (levels.includes('debug')) return 'debug';

  return 'warn'; // Default log level
};

// Custom error serializer that preserves all error properties
const errorSerializer = (err: unknown) => {
  if (!err || typeof err !== 'object') return err;

  const errorObj = err as Record<string, unknown>;
  const serialized: Record<string, unknown> = {
    type: errorObj.constructor?.name || 'Unknown',
    message: (err as unknown as <PERSON>rror).message,
    stack: (err as unknown as Error).stack,
  };

  // Copy all enumerable properties (including Stripe-specific ones)
  Object.keys(errorObj).forEach((key) => {
    serialized[key] = errorObj[key];
  });

  return serialized;
};

export const logger: Logger =
  process.env.NODE_ENV === 'production'
    ? pino({
        level: getLogLevel(),
        serializers: {
          err: errorSerializer,
          error: errorSerializer, // Support both 'err' and 'error' fields
        },
      })
    : pino({
        transport: {
          target: 'pino-pretty',
          options: { colorize: true },
        },
        level: getLogLevel(),
        serializers: {
          err: errorSerializer,
          error: errorSerializer, // Support both 'err' and 'error' fields
        },
      });
