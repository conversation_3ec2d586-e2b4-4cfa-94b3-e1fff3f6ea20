import { Component, ReactNode } from 'react';
import { AppError } from '@/lib/error';
import { logger } from '@/lib/logger';

interface Props {
  children: ReactNode;
  fallback: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Log the error
    logger.error(
      {
        error: error instanceof AppError ? error.toJSON() : error,
        componentStack: errorInfo.componentStack,
      },
      'React component error'
    );

    // Report to Sentry in production with enhanced context
    if (process.env.NODE_ENV === 'production') {
      try {
        // Dynamic import to avoid issues in environments where Sentry isn't available
        import('@sentry/nextjs')
          .then((Sentry) => {
            Sentry.withScope((scope) => {
              scope.setTag('errorBoundary', 'component');
              scope.setLevel('error');
              scope.setContext('errorInfo', {
                componentStack: errorInfo.componentStack,
                errorBoundary: this.constructor.name,
              });

              // Add breadcrumb for React error boundary
              Sentry.addBreadcrumb({
                message: 'React Error Boundary caught an error',
                category: 'ui',
                level: 'error',
                data: {
                  componentStack: errorInfo.componentStack,
                },
              });

              Sentry.captureException(error);
            });
          })
          .catch((sentryError) => {
            console.error('Failed to report to Sentry:', sentryError);
          });
      } catch (reportError) {
        console.error('Failed to report error:', reportError);
      }
    }
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback;
    }
    return this.props.children;
  }
}
