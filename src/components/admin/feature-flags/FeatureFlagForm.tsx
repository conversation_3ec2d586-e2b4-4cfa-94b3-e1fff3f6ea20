'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { toast } from 'sonner';
import { Loader2 } from 'lucide-react';
import {
  FeatureFlag,
  CreateFlagInput,
  UpdateFlagInput,
  createFlagSchema,
  updateFlagSchema,
} from '@/lib/feature-flags/types';

interface FeatureFlagFormProps {
  mode: 'create' | 'edit';
  flag?: FeatureFlag;
  onSuccess: () => void;
  onCancel: () => void;
}

type FormData = CreateFlagInput | UpdateFlagInput;

export function FeatureFlagForm({
  mode,
  flag,
  onSuccess,
  onCancel,
}: FeatureFlagFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const schema = mode === 'create' ? createFlagSchema : updateFlagSchema;

  const form = useForm<FormData>({
    resolver: zodResolver(schema),
    defaultValues:
      mode === 'edit' && flag
        ? {
            display_name: flag.display_name,
            description: flag.description || '',
            is_enabled: flag.is_enabled,
            rollout_percentage: flag.rollout_percentage,
          }
        : {
            name: '',
            display_name: '',
            description: '',
            is_enabled: false,
            rollout_percentage: 0,
          },
  });

  const onSubmit = async (data: FormData) => {
    try {
      setIsSubmitting(true);

      const url =
        mode === 'create'
          ? '/api/admin/feature-flags'
          : `/api/admin/feature-flags/${encodeURIComponent(flag!.name)}`;

      const method = mode === 'create' ? 'POST' : 'PUT';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result.error) {
        throw new Error(result.error);
      }

      onSuccess();
    } catch (error) {
      console.error(
        `Error ${mode === 'create' ? 'creating' : 'updating'} feature flag:`,
        error
      );
      toast.error(
        `Failed to ${mode === 'create' ? 'create' : 'update'} feature flag`
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const rolloutPercentage = form.watch('rollout_percentage') || 0;

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-6'>
        {mode === 'create' && (
          <FormField
            control={form.control}
            name='name'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Flag Name</FormLabel>
                <FormControl>
                  <Input
                    placeholder='new-chat-ui'
                    {...field}
                    className='font-mono'
                  />
                </FormControl>
                <FormDescription>
                  Kebab-case identifier (lowercase letters, numbers, and hyphens
                  only). This cannot be changed after creation.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        <FormField
          control={form.control}
          name='display_name'
          render={({ field }) => (
            <FormItem>
              <FormLabel>Display Name</FormLabel>
              <FormControl>
                <Input placeholder='New Chat UI' {...field} />
              </FormControl>
              <FormDescription>
                Human-readable name shown in the admin interface.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name='description'
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea
                  placeholder='Enables the new chat interface with improved UX...'
                  {...field}
                  rows={3}
                />
              </FormControl>
              <FormDescription>
                Optional description of what this feature does.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className='grid grid-cols-2 gap-6'>
          <FormField
            control={form.control}
            name='is_enabled'
            render={({ field }) => (
              <FormItem className='flex flex-row items-center justify-between rounded-lg border p-4'>
                <div className='space-y-0.5'>
                  <FormLabel className='text-base'>Enabled</FormLabel>
                  <FormDescription>
                    Global on/off switch for this feature.
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name='rollout_percentage'
            render={({ field }) => (
              <FormItem className='space-y-3'>
                <FormLabel>Rollout Percentage</FormLabel>
                <FormControl>
                  <div className='space-y-3'>
                    <Slider
                      value={[field.value || 0]}
                      onValueChange={(value) => field.onChange(value[0])}
                      max={100}
                      step={1}
                      className='w-full'
                    />
                    <div className='flex items-center justify-between text-sm text-muted-foreground'>
                      <span>0%</span>
                      <span className='font-medium'>{rolloutPercentage}%</span>
                      <span>100%</span>
                    </div>
                  </div>
                </FormControl>
                <FormDescription>
                  Percentage of users who will see this feature when enabled.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className='flex items-center justify-end gap-3 pt-4 border-t'>
          <Button
            type='button'
            variant='outline'
            onClick={onCancel}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button type='submit' disabled={isSubmitting}>
            {isSubmitting && <Loader2 className='h-4 w-4 mr-2 animate-spin' />}
            {mode === 'create' ? 'Create Flag' : 'Update Flag'}
          </Button>
        </div>
      </form>
    </Form>
  );
}
