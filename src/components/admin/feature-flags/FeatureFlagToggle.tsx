'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ToggleLeft, ToggleRight } from 'lucide-react';
import { FeatureFlag } from '@/lib/feature-flags/types';

interface FeatureFlagToggleProps {
  flag: FeatureFlag;
  onToggle: () => void;
  disabled?: boolean;
}

export function FeatureFlagToggle({
  flag,
  onToggle,
  disabled = false,
}: FeatureFlagToggleProps) {
  return (
    <div className='flex items-center gap-2'>
      <Button
        variant='ghost'
        size='sm'
        onClick={onToggle}
        disabled={disabled}
        className='p-1 h-auto'
      >
        {flag.is_enabled ? (
          <ToggleRight className='h-5 w-5 text-green-600' />
        ) : (
          <ToggleLeft className='h-5 w-5 text-gray-400' />
        )}
      </Button>

      <Badge variant={flag.is_enabled ? 'default' : 'secondary'}>
        {flag.is_enabled ? 'Enabled' : 'Disabled'}
      </Badge>
    </div>
  );
}
