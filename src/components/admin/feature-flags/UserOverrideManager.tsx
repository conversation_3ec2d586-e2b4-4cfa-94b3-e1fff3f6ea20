'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { toast } from 'sonner';
import { Plus, Trash2, Loader2, Search } from 'lucide-react';
import {
  FeatureFlag
} from '@/lib/feature-flags/types';

interface UserOverrideManagerProps {
  flag: FeatureFlag;
  onClose: () => void;
}

interface UserOverride {
  id: string;
  user_id: string;
  is_enabled: boolean;
  created_at: string;
  user_email?: string; // We might want to fetch this for display
}

export function UserOverrideManager({
  flag,
  onClose,
}: UserOverrideManagerProps) {
  const [overrides, setOverrides] = useState<UserOverride[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [newUserId, setNewUserId] = useState('');
  const [newUserEnabled, setNewUserEnabled] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch user overrides for this flag
  const fetchOverrides = async () => {
    try {
      setLoading(true);
      const response = await fetch(
        `/api/admin/feature-flags/${encodeURIComponent(flag.name)}/overrides`
      );

      if (response.ok) {
        const data = await response.json();
        setOverrides(data.data || []);
      } else {
        setOverrides([]);
      }
    } catch (error) {
      console.error('Error fetching user overrides:', error);
      setOverrides([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchOverrides();
  }, [flag.name]);

  // Add user override
  const handleAddOverride = async () => {
    if (!newUserId.trim()) {
      toast.error('Please enter a user ID');
      return;
    }

    try {
      setIsSubmitting(true);

      const response = await fetch(
        `/api/admin/feature-flags/${encodeURIComponent(flag.name)}/overrides`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            user_id: newUserId.trim(),
            is_enabled: newUserEnabled,
          }),
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.error) {
        throw new Error(data.error);
      }

      toast.success('User override added successfully');
      setIsAddModalOpen(false);
      setNewUserId('');
      setNewUserEnabled(true);
      fetchOverrides(); // Refresh the list
    } catch (error) {
      console.error('Error adding user override:', error);
      toast.error('Failed to add user override');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Remove user override
  const handleRemoveOverride = async (userId: string) => {
    if (!confirm('Are you sure you want to remove this user override?')) {
      return;
    }

    try {
      const response = await fetch(
        `/api/admin/feature-flags/${encodeURIComponent(flag.name)}/overrides/${encodeURIComponent(userId)}`,
        {
          method: 'DELETE',
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.error) {
        throw new Error(data.error);
      }

      toast.success('User override removed successfully');
      fetchOverrides(); // Refresh the list
    } catch (error) {
      console.error('Error removing user override:', error);
      toast.error('Failed to remove user override');
    }
  };

  // Filter overrides based on search query
  const filteredOverrides = overrides.filter(
    (override) =>
      override.user_id.toLowerCase().includes(searchQuery.toLowerCase()) ||
      override.user_email?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div className='flex items-center justify-between'>
        <div>
          <h3 className='text-lg font-semibold'>User Overrides</h3>
          <p className='text-sm text-muted-foreground'>
            Manage user-specific overrides for{' '}
            <span className='font-mono'>{flag.name}</span>
          </p>
        </div>

        <Dialog open={isAddModalOpen} onOpenChange={setIsAddModalOpen}>
          <DialogTrigger asChild>
            <Button size='sm'>
              <Plus className='h-4 w-4 mr-2' />
              Add Override
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add User Override</DialogTitle>
              <DialogDescription>
                Set a specific value for this feature flag for a particular
                user.
              </DialogDescription>
            </DialogHeader>

            <div className='space-y-4'>
              <div>
                <label className='text-sm font-medium'>User ID</label>
                <Input
                  placeholder='Enter user ID (UUID)'
                  value={newUserId}
                  onChange={(e) => setNewUserId(e.target.value)}
                  className='mt-1'
                />
              </div>

              <div className='flex items-center justify-between rounded-lg border p-4'>
                <div className='space-y-0.5'>
                  <label className='text-sm font-medium'>
                    Enabled for User
                  </label>
                  <p className='text-sm text-muted-foreground'>
                    Whether this feature should be enabled for this user.
                  </p>
                </div>
                <Switch
                  checked={newUserEnabled}
                  onCheckedChange={setNewUserEnabled}
                />
              </div>

              <div className='flex items-center justify-end gap-3 pt-4 border-t'>
                <Button
                  variant='outline'
                  onClick={() => setIsAddModalOpen(false)}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button onClick={handleAddOverride} disabled={isSubmitting}>
                  {isSubmitting && (
                    <Loader2 className='h-4 w-4 mr-2 animate-spin' />
                  )}
                  Add Override
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Search */}
      <div className='flex items-center gap-4'>
        <div className='relative flex-1 max-w-sm'>
          <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground' />
          <Input
            placeholder='Search users...'
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className='pl-10'
          />
        </div>

        <Badge variant='secondary'>
          {overrides.length} override{overrides.length !== 1 ? 's' : ''}
        </Badge>
      </div>

      {/* Overrides Table */}
      <div className='border rounded-lg'>
        {loading ? (
          <div className='flex items-center justify-center h-32'>
            <Loader2 className='h-6 w-6 animate-spin' />
          </div>
        ) : filteredOverrides.length === 0 ? (
          <div className='flex items-center justify-center h-32 text-muted-foreground'>
            {overrides.length === 0
              ? 'No user overrides configured'
              : 'No overrides match your search'}
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>User ID</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Created</TableHead>
                <TableHead className='text-right'>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredOverrides.map((override) => (
                <TableRow key={override.id}>
                  <TableCell className='font-mono text-sm'>
                    {override.user_id}
                    {override.user_email && (
                      <div className='text-xs text-muted-foreground'>
                        {override.user_email}
                      </div>
                    )}
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant={override.is_enabled ? 'default' : 'secondary'}
                    >
                      {override.is_enabled ? 'Enabled' : 'Disabled'}
                    </Badge>
                  </TableCell>
                  <TableCell className='text-sm text-muted-foreground'>
                    {new Date(override.created_at).toLocaleDateString()}
                  </TableCell>
                  <TableCell className='text-right'>
                    <Button
                      variant='ghost'
                      size='sm'
                      onClick={() => handleRemoveOverride(override.user_id)}
                    >
                      <Trash2 className='h-4 w-4' />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </div>

      {/* Footer */}
      <div className='flex items-center justify-end pt-4 border-t'>
        <Button variant='outline' onClick={onClose}>
          Close
        </Button>
      </div>
    </div>
  );
}
