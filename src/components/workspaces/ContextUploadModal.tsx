'use client';

import { useState, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { useSubscription } from '@/providers/SubscriptionProvider';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { Upload, Loader2, FileText, Crown } from 'lucide-react';

interface ContextUploadModalProps {
  children: React.ReactNode;
  workspaceId: string;
  onSuccess?: () => void; // Callback to refresh data on parent page
}

export function ContextUploadModal({
  children,
  workspaceId,
  onSuccess,
}: ContextUploadModalProps) {
  const router = useRouter();
  const { subscription } = useSubscription();
  const [isOpen, setIsOpen] = useState(false);
  const [uploadType, setUploadType] = useState('file'); // 'file' or 'note'
  const [isUploading, setIsUploading] = useState(false);
  const [isSubmittingNote, setIsSubmittingNote] = useState(false);
  const [noteTitle, setNoteTitle] = useState('');
  const [noteBody, setNoteBody] = useState('');
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Function to handle file selection and upload
  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    if (!event.target.files || event.target.files.length === 0) return;
    const file = event.target.files[0];

    setIsUploading(true);

    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch(`/api/workspaces/${workspaceId}/files`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        
        if (errorData.upgradeRequired && errorData.feature === 'workspace_file_uploads') {
          toast.error(
            'File uploads require a Premium subscription. Upgrade to upload files to workspaces.',
            {
              action: {
                label: 'Upgrade',
                onClick: () => router.push('/settings?tab=billing'),
              },
            }
          );
          return;
        }
        
        throw new Error(errorData.error || 'Failed to upload file');
      }

      const data = await response.json();
      toast.success(`File "${data.file.filename}" uploaded successfully`);
      setIsOpen(false);
      if (onSuccess) onSuccess();
      router.refresh();
    } catch (error) {
      console.error('Error uploading file:', error);
      toast.error(
        error instanceof Error ? error.message : 'Failed to upload file'
      );
    } finally {
      setIsUploading(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  // Function to handle manual note submission
  const handleNoteSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    if (!noteTitle.trim() || !noteBody.trim()) {
      toast.error('Both title and body are required for notes.');
      return;
    }
    setIsSubmittingNote(true);
    try {
      const response = await fetch(`/api/workspaces/${workspaceId}/notes`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ title: noteTitle, body: noteBody }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to create note');
      }

      toast.success(`Note "${noteTitle}" created successfully`);
      setNoteTitle('');
      setNoteBody('');
      setIsOpen(false);
      if (onSuccess) onSuccess();
      router.refresh();
    } catch (error) {
      console.error('Error creating note:', error);
      toast.error(
        error instanceof Error ? error.message : 'Failed to create note'
      );
    } finally {
      setIsSubmittingNote(false);
    }
  };

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      setNoteTitle('');
      setNoteBody('');
      setIsUploading(false);
      setIsSubmittingNote(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
    setIsOpen(open);
  };

  const isPremium = subscription?.plan === 'premium';
  const handleUpgrade = () => {
    router.push('/settings?tab=billing');
    setIsOpen(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className='sm:max-w-[500px]'>
        <DialogHeader>
          <DialogTitle>Add Context to Workspace</DialogTitle>
          <DialogDescription>
            Upload files (PDF, TXT, etc.) or add manual notes to provide
            context.
          </DialogDescription>
        </DialogHeader>

        <Tabs value={uploadType} onValueChange={setUploadType} className='mt-4'>
          <TabsList className='grid w-full grid-cols-2'>
            <TabsTrigger
              value='file'
              disabled={isUploading || isSubmittingNote}
              className='relative'
            >
              <Upload className='mr-2 h-4 w-4' /> Upload File
              {!isPremium && (
                <Badge variant='secondary' className='ml-2 text-xs'>
                  <Crown className='w-3 h-3 mr-1' />
                  Premium
                </Badge>
              )}
            </TabsTrigger>
            <TabsTrigger
              value='note'
              disabled={isUploading || isSubmittingNote}
            >
              <FileText className='mr-2 h-4 w-4' /> Add Note
            </TabsTrigger>
          </TabsList>

          <TabsContent value='file' className='pt-4'>
            {!isPremium ? (
              <div className='space-y-4 p-4 border border-amber-200 bg-amber-50 dark:bg-amber-950 dark:border-amber-800 rounded-lg'>
                <div className='flex items-center gap-2'>
                  <Crown className='w-5 h-5 text-amber-600' />
                  <h3 className='font-medium text-amber-800 dark:text-amber-200'>
                    Premium Feature
                  </h3>
                </div>
                <p className='text-sm text-amber-700 dark:text-amber-300'>
                  File uploads to workspaces are available with a Premium subscription. 
                  Upload documents like PDFs, text files, and more to enhance your AI conversations.
                </p>
                <Button onClick={handleUpgrade} className='w-full'>
                  <Crown className='w-4 h-4 mr-2' />
                  Upgrade to Premium
                </Button>
              </div>
            ) : (
              <>
                <div className='grid w-full max-w-sm items-center gap-1.5'>
                  <Label htmlFor='context-file'>Select File</Label>
                  <Input
                    id='context-file'
                    type='file'
                    ref={fileInputRef}
                    onChange={handleFileChange}
                    disabled={isUploading}
                    accept='.pdf,.txt,.md,.json,.csv'
                  />
                  <p className='text-sm text-muted-foreground pt-1'>
                    Supported: PDF, TXT, MD, JSON, CSV.
                  </p>
                </div>
                {isUploading && (
                  <div className='mt-4 space-y-2'>
                    <p className='text-sm text-muted-foreground flex items-center'>
                      <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                      Uploading...
                    </p>
                  </div>
                )}
              </>
            )}
          </TabsContent>

          <TabsContent value='note' className='pt-4'>
            <form onSubmit={handleNoteSubmit} className='space-y-4'>
              <div className='space-y-1.5'>
                <Label htmlFor='note-title'>Note Title</Label>
                <Input
                  id='note-title'
                  placeholder='E.g., Project Goals'
                  value={noteTitle}
                  onChange={(e) => setNoteTitle(e.target.value)}
                  disabled={isSubmittingNote}
                  required
                />
              </div>
              <div className='space-y-1.5'>
                <Label htmlFor='note-body'>Note Content</Label>
                <Textarea
                  id='note-body'
                  placeholder='Type your note content here...'
                  value={noteBody}
                  onChange={(e) => setNoteBody(e.target.value)}
                  disabled={isSubmittingNote}
                  required
                  rows={6}
                  className='resize-none'
                />
              </div>
              <div className='flex justify-end pt-2'>
                <Button type='submit' disabled={isSubmittingNote}>
                  {isSubmittingNote ? (
                    <>
                      <Loader2 className='mr-2 h-4 w-4 animate-spin' />{' '}
                      Saving...
                    </>
                  ) : (
                    'Save Note'
                  )}
                </Button>
              </div>
            </form>
          </TabsContent>
        </Tabs>

        <DialogFooter className='mt-4'>
          <DialogClose asChild>
            <Button
              type='button'
              variant='outline'
              disabled={isUploading || isSubmittingNote}
            >
              Cancel
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
