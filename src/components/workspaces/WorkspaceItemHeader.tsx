'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  PenSquare,
  Upload,
  Text,
  Trash2,
  FileText,
  AlertTriangle,
  Loader2,
  Settings,
  Plus,
  Clock,
} from 'lucide-react';
import {
  Workspace,
  WorkspaceFile,
  WorkspaceNote,
  GroupConversation,
} from '@/lib/supabase/types';
import { NoteModal } from '@/components/workspaces/NoteModal';
import { ContextUploadModal } from '@/components/workspaces/ContextUploadModal';
import { ContextPanel } from '@/components/workspaces/ContextPanel';
import { EditWorkspaceModal } from '@/components/workspaces/EditWorkspaceModal';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import {
  Too<PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { useState } from 'react';

type Params = {
  workspace: Workspace;
  files: WorkspaceFile[];
  notes: WorkspaceNote[];
  recentConversations: GroupConversation[];
  showConversations: boolean;
  fetchData: () => void;
  handleDeleteWorkspace: () => void;
  handleContextUpdate: () => void;
  handleNewChat: () => void;
  handleToggleConversations: () => void;
  isSubmitting: boolean;
  contextSheetOpen: boolean;
  setContextSheetOpen: (open: boolean) => void;
};

export default function WorkspaceItemHeader(props: Params) {
  const {
    workspace,
    files,
    notes,
    recentConversations,
    showConversations,
    fetchData,
    handleDeleteWorkspace,
    handleContextUpdate,
    handleNewChat,
    handleToggleConversations,
    isSubmitting,
    contextSheetOpen,
    setContextSheetOpen,
  } = props;

  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  const contextCount = files.length + notes.length;
  const isProcessing =
    workspace.status === 'initiated' || workspace.status === 'pending';
  const hasError = workspace.status === 'error';

  return (
    <>
      <div className='flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4'>
        <div className='flex items-center gap-4 min-w-0 flex-1'>
          <div className='h-12 w-12 rounded-lg bg-primary/10 text-primary flex items-center justify-center text-xl font-semibold flex-shrink-0'>
            {workspace.icon || workspace.name.charAt(0).toUpperCase()}
          </div>
          <div className='flex flex-col min-w-0 flex-1'>
            <div className='flex items-center gap-3'>
              <h2 className='text-xl lg:text-2xl font-bold truncate'>{workspace.name}</h2>

              {/* Status indicators */}
              {isProcessing && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>
                      <div className='flex items-center gap-1.5 text-amber-500'>
                        <Loader2 className='h-4 w-4 animate-spin' />
                        <span className='text-xs font-medium'>Processing</span>
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Files are being processed and embedded</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}

              {hasError && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>
                      <div className='flex items-center gap-1.5 text-destructive'>
                        <AlertTriangle className='h-4 w-4' />
                        <span className='text-xs font-medium'>Error</span>
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>There was an error processing the workspace</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </div>

            {workspace.description && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <p className='text-muted-foreground text-sm mt-1 line-clamp-2 cursor-default max-w-md'>
                      {workspace.description}
                    </p>
                  </TooltipTrigger>
                  {workspace.description.length > 80 && (
                    <TooltipContent side="bottom" className='max-w-sm'>
                      <p className='text-xs whitespace-pre-wrap'>
                        {workspace.description}
                      </p>
                    </TooltipContent>
                  )}
                </Tooltip>
              </TooltipProvider>
            )}
          </div>
        </div>

        <div className='flex flex-col sm:flex-row items-stretch sm:items-center gap-3 w-full lg:w-auto'>
          {/* Chat Actions */}
          <div className='flex items-center gap-2 flex-wrap'>
            <Button
              onClick={handleNewChat}
              className='bg-primary hover:bg-primary/90 text-primary-foreground font-medium px-3 lg:px-4 py-2 text-sm lg:text-base'
            >
              <Plus className='mr-1 lg:mr-2 h-4 w-4' />
              <span className='hidden sm:inline'>New Chat</span>
              <span className='sm:hidden'>New</span>
            </Button>

            {recentConversations.length > 0 && (
              <Button
                variant={showConversations ? 'secondary' : 'outline'}
                size='sm'
                onClick={handleToggleConversations}
                className='text-xs lg:text-sm'
              >
                <Clock className='mr-1 lg:mr-2 h-3 w-3' />
                <span className='hidden sm:inline'>Recent ({recentConversations.length})</span>
                <span className='sm:hidden'>({recentConversations.length})</span>
              </Button>
            )}
          </div>

          <div className='flex items-center gap-2 flex-wrap'>
            {/* Knowledge Hub */}
            <Sheet open={contextSheetOpen} onOpenChange={setContextSheetOpen}>
              <SheetTrigger asChild>
                <Button variant='outline' size='sm' className='text-xs lg:text-sm'>
                  <FileText className='mr-1 lg:mr-2 h-4 w-4' />
                  <span className='hidden sm:inline'>Knowledge Hub</span>
                  <span className='sm:hidden'>Hub</span>
                  {contextCount > 0 && (
                    <Badge variant='secondary' className='ml-1 lg:ml-2'>
                      {contextCount}
                    </Badge>
                  )}
                </Button>
              </SheetTrigger>
            <SheetContent className='sm:max-w-md'>
              <SheetHeader>
                <SheetTitle>Workspace Knowledge Hub</SheetTitle>
                <SheetDescription>
                  Files and notes providing context for this workspace.
                </SheetDescription>
              </SheetHeader>

              <div className='flex flex-wrap gap-2 mt-4 mb-6'>
                <ContextUploadModal
                  workspaceId={workspace.id}
                  onSuccess={handleContextUpdate}
                >
                  <Button variant='outline' size='sm' className='h-8'>
                    <Upload className='mr-2 h-3.5 w-3.5' /> Upload File
                  </Button>
                </ContextUploadModal>
                <NoteModal
                  mode='create'
                  workspaceId={workspace.id}
                  onSuccess={handleContextUpdate}
                >
                  <Button variant='outline' size='sm' className='h-8'>
                    <Text className='mr-2 h-3.5 w-3.5' /> Add Note
                  </Button>
                </NoteModal>
              </div>

              <div className='mt-2'>
                <ContextPanel
                  files={files}
                  notes={notes}
                  workspaceId={workspace.id}
                  onUpdate={handleContextUpdate}
                />
              </div>
            </SheetContent>
            </Sheet>

            {/* Settings Dropdown */}
            <DropdownMenu open={isDropdownOpen} onOpenChange={setIsDropdownOpen}>
              <DropdownMenuTrigger asChild>
                <Button variant='outline' size='sm' disabled={isSubmitting}>
                  <Settings className='h-4 w-4' />
                </Button>
              </DropdownMenuTrigger>
            <DropdownMenuContent align='end'>
              <DropdownMenuItem
                onSelect={() => {
                  setIsEditModalOpen(true);
                  setIsDropdownOpen(false);
                }}
              >
                  <PenSquare className='mr-2 h-4 w-4' />
                  Edit Workspace
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                className='text-destructive focus:text-destructive'
                onClick={handleDeleteWorkspace}
                disabled={isSubmitting}
              >
                <Trash2 className='mr-2 h-4 w-4' />
                Delete Workspace
              </DropdownMenuItem>
                          </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>

      <EditWorkspaceModal
        workspace={workspace}
        onSuccess={() => {
          fetchData();
          setIsEditModalOpen(false);
        }}
        open={isEditModalOpen}
        onOpenChange={setIsEditModalOpen}
      />
    </>
  );
}
