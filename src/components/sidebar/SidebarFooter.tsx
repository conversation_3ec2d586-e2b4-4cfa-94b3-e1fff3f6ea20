'use client';

import React from 'react';
import { LogIn, Settings, LogOut, Zap, Shield } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { User } from '@supabase/supabase-js';
import { SidebarState } from '@/providers/SidebarProvider';
import { UserContext } from '@/providers/AuthProvider';
import { useContext } from 'react';
import { useSubscription } from '@/providers/SubscriptionProvider';
import { useFeatureFlag } from '@/hooks/useFeatureFlag';
import { IS_ADMIN_FLAG } from '@/lib/chatApi';
void React;

export function SidebarFooter({
  isAuthenticated,
  user,
  sidebarState,
}: {
  isAuthenticated: boolean;
  user: User | null;
  sidebarState: SidebarState;
}) {
  const router = useRouter();
  const { signOut: onSignOut } = useContext(UserContext);
  const { subscription } = useSubscription();
  const plan = subscription?.plan;
  const { isEnabled: isAdmin } = useFeatureFlag(IS_ADMIN_FLAG);
  const onSignIn = () => {
    router.push('/auth/login');
  };

  const onSettings = () => {
    router.push('/settings');
  };

  const onAdmin = () => {
    router.push('/admin');
  };

  return (
    <div
      className={cn(
        'border-t border-border',
        sidebarState === 'minimized' ? 'p-2' : 'p-3'
      )}
    >
      {isAuthenticated ? (
        <>
          {/* Upgrade Button - Visible only on free plan & expanded sidebar */}
          <AnimatePresence>
            {plan === 'free' && sidebarState !== 'minimized' && (
              <motion.button
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.15 }}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className='w-full flex items-center justify-center gap-2 rounded-md bg-btn-secondary-bg hover:bg-btn-secondary-hover p-2 mb-2 text-indigo-400'
                onClick={() => router.push('/settings?tab=billing')}
              >
                <Zap className='h-4 w-4 shrink-0' />
                <span className='whitespace-nowrap'>Upgrade Plan</span>
              </motion.button>
            )}
          </AnimatePresence>

          {/* User Profile Section */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className={cn(
                  'flex items-center gap-3 w-full rounded-md hover:bg-btn-secondary-hover text-left overflow-hidden',
                  sidebarState === 'minimized' ? 'justify-center p-2' : 'p-2'
                )}
              >
                <Avatar
                  className={sidebarState === 'minimized' ? 'h-8 w-8' : ''}
                >
                  <AvatarImage src={user?.user_metadata?.avatar_url} />
                  <AvatarFallback>
                    {getInitials(user?.email || '')}
                  </AvatarFallback>
                </Avatar>
                <AnimatePresence mode='wait'>
                  {sidebarState !== 'minimized' && (
                    <motion.div
                      initial={{ opacity: 0, width: 0 }}
                      animate={{ opacity: 1, width: 'auto' }}
                      exit={{ opacity: 0, width: 0 }}
                      transition={{ duration: 0.15 }}
                      className='flex flex-col min-w-0 overflow-hidden'
                    >
                      <span className='font-medium truncate whitespace-nowrap'>
                        {user?.user_metadata?.name}
                      </span>
                      <span className='text-xs text-zinc-400 truncate whitespace-nowrap'>
                        {user?.email}
                      </span>
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              align='end'
              className='w-56 bg-sidebar-bg text-sidebar-fg border-border'
            >
              <DropdownMenuItem onClick={onSettings} className='cursor-pointer'>
                <Settings className='mr-2 h-4 w-4' />
                <span>Settings</span>
              </DropdownMenuItem>
              {isAdmin && (
                <DropdownMenuItem onClick={onAdmin} className='cursor-pointer'>
                  <Shield className='mr-2 h-4 w-4' />
                  <span>Admin</span>
                </DropdownMenuItem>
              )}
              <DropdownMenuItem
                onClick={onSignOut}
                className='cursor-pointer text-red-500 focus:text-red-500'
              >
                <LogOut className='mr-2 h-4 w-4' />
                <span>Log out</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </>
      ) : (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className={cn(
                  'w-full flex items-center justify-center rounded-md bg-btn-secondary-bg hover:bg-btn-secondary-hover p-2 overflow-hidden',
                  sidebarState === 'minimized' ? 'h-10' : 'gap-2'
                )}
                onClick={onSignIn}
              >
                <LogIn className='h-4 w-4 shrink-0' />
                <AnimatePresence mode='wait'>
                  {sidebarState !== 'minimized' && (
                    <motion.span
                      initial={{ opacity: 0, width: 0 }}
                      animate={{ opacity: 1, width: 'auto' }}
                      exit={{ opacity: 0, width: 0 }}
                      transition={{ duration: 0.15 }}
                      className='whitespace-nowrap'
                    >
                      Sign In
                    </motion.span>
                  )}
                </AnimatePresence>
              </motion.button>
            </TooltipTrigger>
            {sidebarState === 'minimized' && (
              <TooltipContent side='right'>
                <p>Sign In</p>
              </TooltipContent>
            )}
          </Tooltip>
        </TooltipProvider>
      )}
    </div>
  );
}

// Helper function to get initials from name
function getInitials(name: string): string {
  return name
    .split(' ')
    .map((part) => part[0])
    .join('')
    .toUpperCase()
    .substring(0, 2);
}
