'use client';
import { useContext, useState, useEffect } from 'react';
import { UserContext } from '@/providers/AuthProvider';
import { ChatContext } from '@/providers/ChatProvider';
import { AnimatePresence } from 'framer-motion';
import { TooltipProvider } from '@/components/ui/tooltip';
import { GroupConversation } from '@/lib/supabase/db';

// Components
import { SidebarFooter } from './SidebarFooter';
import { SidebarHeader } from './SidebarHeader';
import { SidebarShell } from './SidebarShell';
import { NavigationButtons } from './NavigationButtons';
import { MobileBackdrop } from './MobileBackdrop';
import { FullSidebar } from './FullSidebar';
import { FavoriteWorkspacesSection } from './sections/FavoriteWorkspacesSection';
import { FavoriteChatsSection } from './sections/FavoriteChatsSection';
import { RecentChatsSection } from './sections/RecentChatsSection';

// Hooks
import { useSidebarState } from '@/components/sidebar/hooks/useSidebarState';
import { useSidebarData } from '@/components/sidebar/hooks/useSidebarData';
import { useCollapsible } from '@/components/sidebar/hooks/useCollapsible';
import { useDialogHandlers } from '@/components/sidebar/hooks/useDialogHandlers';
import { useRouter } from 'next/navigation';
import { Loader2 } from 'lucide-react';

export type SidebarState = 'full' | 'minimized' | 'hidden';

type AppSidebarProps = {
  goToConversation: (conversation: GroupConversation) => void;
};

export function AppSidebar({ goToConversation }: AppSidebarProps) {
  const { isAuthenticated, user } = useContext(UserContext);
  const { initializeNewChat } = useContext(ChatContext);
  const router = useRouter();
  // Sidebar UI state
  const {
    sidebarState,
    isPeeking,
    isAnimatingOut,
    isMobile,
    handleMobileClose,
    handleMouseLeave,
    handleAnimationComplete,
  } = useSidebarState();

  // Sidebar conversation data
  const {
    favoriteConversations,
    recentConversations,
    selectedConversation,
    groupedConversations,
    orderedGroups,
    isConversationListLoading,
    isBackgroundRefreshing,
    hasInitiallyLoaded,
    hasMoreConversations,
    isFetchingMoreConversations,
    sseConnections,
    fetchMoreConversations,
    renameConversation,
    deleteConversation,
    archiveConversation,
    toggleFavorite,
    refreshSidebarData,
  } = useSidebarData();

  // Track if favorite workspaces exist
  const [hasFavoriteWorkspaces, setHasFavoriteWorkspaces] = useState(false);

  // Track refresh state
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Collapsible sections state
  const { open, toggle } = useCollapsible({
    favoriteWorkspaces: true,
    favoriteChats: false,
    recentChats: true,
  });

  // Prefetch common routes for better performance
  useEffect(() => {
    // Prefetch main routes that users commonly navigate to
    router.prefetch('/chat');
    router.prefetch('/workspaces');
    router.prefetch('/prompts');
    router.prefetch('/chats');
    router.prefetch('/settings');
  }, [router]);

  const handleNewConversation = () => {
    initializeNewChat();
    router.push('/chat');
  };

  // Add a function to handle manual refresh
  const handleManualRefresh = async () => {
    setIsRefreshing(true);
    try {
      await refreshSidebarData();
    } catch (error) {
      console.error('Failed to refresh sidebar data:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  // Dropdown menu state
  const [openDropdownId, setOpenDropdownId] = useState<string | null>(null);

  // Dialog handlers
  const { dialogs, handlers } = useDialogHandlers(renameConversation, deleteConversation);

  return (
    <>
      <MobileBackdrop isVisible={isMobile && isPeeking} onClose={handleMobileClose} />

      <AnimatePresence>
        <TooltipProvider delayDuration={0}>
          <SidebarShell
            state={sidebarState}
            isPeeking={isPeeking}
            isAnimatingOut={isAnimatingOut}
            isMobile={isMobile}
            onMouseLeave={handleMouseLeave}
            onAnimationComplete={handleAnimationComplete}
          >
            <SidebarHeader
              sidebarState={sidebarState}
              initializeNewChat={handleNewConversation}
              onRefresh={handleManualRefresh}
              isRefreshing={isRefreshing || isBackgroundRefreshing}
            />

            {sidebarState !== 'minimized' && <NavigationButtons />}

            {isConversationListLoading && !hasInitiallyLoaded ? (
              <div className="flex items-center justify-center h-full">
                {sidebarState !== 'minimized' ? (
                  <span className="flex items-center gap-2">
                    <Loader2 className="h-4 w-4 animate-spin" /> Loading...
                  </span>
                ) : (
                  <Loader2 className="h-5 w-5 animate-spin" />
                )}
              </div>
            ) : (
              <div className="flex-1 overflow-hidden border-t border-border">
                <div className="flex flex-col space-y-1 p-3 overflow-y-auto h-full">
                  <FullSidebar sidebarState={sidebarState}>
                    {/* Favorite Workspaces - only shown when favorites exist */}
                    <FavoriteWorkspacesSection
                      isExpanded={open.favoriteWorkspaces}
                      onToggleSection={() => toggle('favoriteWorkspaces')}
                      hasFavorites={hasFavoriteWorkspaces}
                      onFavoritesChange={setHasFavoriteWorkspaces}
                    />

                    {/* Favorite Chats */}
                    <FavoriteChatsSection
                      isExpanded={open.favoriteChats}
                      conversations={favoriteConversations}
                      selectedConversationId={selectedConversation?.id}
                      openDropdownId={openDropdownId}
                      sseConnections={sseConnections}
                      onToggleSection={() => toggle('favoriteChats')}
                      onSelect={goToConversation}
                      onDropdownChange={setOpenDropdownId}
                      onToggleFavorite={toggleFavorite}
                      onArchive={archiveConversation}
                      onRename={handlers.handleRenameClick}
                      onDelete={handlers.handleDeleteClick}
                    />

                    {/* Recent Chats */}
                    <RecentChatsSection
                      isExpanded={open.recentChats}
                      onToggleSection={() => toggle('recentChats')}
                      sidebarState={sidebarState}
                      groupedConversations={groupedConversations}
                      orderedGroups={orderedGroups}
                      conversations={recentConversations}
                      selectedConversation={selectedConversation}
                      goToConversation={goToConversation}
                      hasMoreConversations={hasMoreConversations}
                      fetchMoreConversations={fetchMoreConversations}
                      isFetchingMoreConversations={isFetchingMoreConversations}
                      isConversationListLoading={isConversationListLoading}
                      hasInitiallyLoaded={hasInitiallyLoaded}
                      sseConnections={sseConnections}
                      openDropdownId={openDropdownId}
                      setOpenDropdownId={setOpenDropdownId}
                      toggleFavorite={toggleFavorite}
                      archiveConversation={archiveConversation}
                      handleRenameClick={handlers.handleRenameClick}
                      handleDeleteClick={handlers.handleDeleteClick}
                    />
                  </FullSidebar>
                </div>
              </div>
            )}

            <SidebarFooter
              isAuthenticated={isAuthenticated}
              user={user}
              sidebarState={sidebarState}
            />
          </SidebarShell>
        </TooltipProvider>
      </AnimatePresence>

      {dialogs}
    </>
  );
}
