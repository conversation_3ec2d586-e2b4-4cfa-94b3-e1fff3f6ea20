'use client';

import { Check<PERSON>ircle, AlertCircle, XCircle, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';

export interface VerificationIndicatorProps {
  isVerifying: boolean;
  verificationComplete: boolean;
  error: string | null;
  confidence: 'high' | 'medium' | 'low' | null;
  purchaseType: 'tokens' | 'images';
  className?: string;
}

export function VerificationIndicator({
  isVerifying,
  verificationComplete,
  error,
  confidence,
  purchaseType,
  className,
}: VerificationIndicatorProps) {
  // Don't show anything if verification hasn't started
  if (!isVerifying && !verificationComplete) {
    return null;
  }

  const getVerificationIcon = () => {
    if (isVerifying) {
      return <Loader2 className="h-4 w-4 animate-spin" />;
    }
    
    if (error) {
      return <XCircle className="h-4 w-4 text-destructive" />;
    }

    if (verificationComplete && !error) {
      if (confidence === 'high') {
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      } else if (confidence === 'medium') {
        return <AlertCircle className="h-4 w-4 text-yellow-600" />;
      } else {
        return <AlertCircle className="h-4 w-4 text-orange-600" />;
      }
    }

    return null;
  };

  const getVerificationStatus = () => {
    if (isVerifying) {
      return 'Verifying purchase...';
    }

    if (error) {
      return error;
    }

    if (verificationComplete && !error) {
      if (confidence === 'high') {
        return `Purchase verified! Your ${purchaseType === 'tokens' ? 'tokens' : 'image credits'} have been added.`;
      } else if (confidence === 'medium') {
        return `Purchase likely successful. Your ${purchaseType === 'tokens' ? 'tokens' : 'image credits'} should appear shortly.`;
      } else {
        return `Purchase status unclear. Please check your usage or contact support if ${purchaseType === 'tokens' ? 'tokens' : 'credits'} don't appear within 10 minutes.`;
      }
    }

    return '';
  };

  const getConfidenceBadge = () => {
    if (!verificationComplete || error || !confidence) {
      return null;
    }

    const badgeProps = {
      high: { variant: 'default' as const, className: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100' },
      medium: { variant: 'secondary' as const, className: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-100' },
      low: { variant: 'outline' as const, className: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-100' },
    };

    const props = badgeProps[confidence];

    return (
      <Badge {...props}>
        {confidence === 'high' ? 'Verified' : confidence === 'medium' ? 'Likely Success' : 'Uncertain'}
      </Badge>
    );
  };

  const alertVariant = error ? 'destructive' : 'default';

  return (
    <Alert variant={alertVariant} className={cn('border-l-4', className, {
      'border-l-blue-500': isVerifying,
      'border-l-green-500': verificationComplete && !error && confidence === 'high',
      'border-l-yellow-500': verificationComplete && !error && confidence === 'medium',
      'border-l-orange-500': verificationComplete && !error && confidence === 'low',
      'border-l-red-500': error,
    })}>
      <div className="flex items-start justify-between gap-3">
        <div className="flex items-start gap-3 flex-1">
          {getVerificationIcon()}
          <div className="flex-1 space-y-2">
            <div className="flex items-center gap-2">
              <AlertDescription className="mb-0">
                {getVerificationStatus()}
              </AlertDescription>
              {getConfidenceBadge()}
            </div>
            
            {isVerifying && (
              <div className="space-y-1">
                <Progress value={33} className="h-1" />
                <p className="text-xs text-muted-foreground">
                  This may take up to 30 seconds...
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </Alert>
  );
}