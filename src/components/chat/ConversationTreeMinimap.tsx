import { MessageNode } from '@/lib/supabase/types';
import {
  <PERSON><PERSON>,
  Bo<PERSON>,
  GitBranch,
  Circle,
  ArrowRight,
  ChevronLeft,
  ChevronRight,
} from 'lucide-react';
import { ScrollArea } from '../ui/scroll-area';
import { Button } from '../ui/button';
import { cn } from '@/lib/utils';

interface ConversationTreeMinimapProps {
  rootMessage: MessageNode | null;
  onJumpToMessage: (messageId: string) => void;
  currentVisibleMessageId?: string;
  selectedBranches: Map<string, number>;
  onBranchChange: (
    messageId: string,
    direction: number,
    siblingSize: number
  ) => void;
}

interface MessageNodeItem {
  node: MessageNode;
  depth: number;
  hasMultipleBranches: boolean;
  isSelected: boolean;
}

export function ConversationTreeMinimap({
  rootMessage,
  onJumpToMessage,
  currentVisibleMessageId,
  selectedBranches,
  onBranchChange,
}: ConversationTreeMinimapProps) {
  if (!rootMessage) {
    return (
      <div className='w-full h-full border-l border-border bg-background flex items-center justify-center'>
        <p className='text-muted-foreground text-sm'>No messages</p>
      </div>
    );
  }

  // Flatten the selected path for better visualization
  const getSelectedPath = (root: MessageNode): MessageNodeItem[] => {
    const path: MessageNodeItem[] = [];
    let current: MessageNode | null = root;
    let depth = 0;

    while (current) {
      const hasMultipleBranches = (current.children?.length || 0) > 1;
      const isSelected = currentVisibleMessageId === current.id;

      path.push({
        node: current,
        depth,
        hasMultipleBranches,
        isSelected,
      });

      if (current.children?.length) {
        const selectedIndex: number =
          selectedBranches.get(current.id) ?? current.children.length - 1;
        current = current.children[selectedIndex] || null;
        depth++;
      } else {
        current = null;
      }
    }

    return path;
  };

  // Get branch summary for a message
  const getBranchSummary = (node: MessageNode) => {
    if (!node.children || node.children.length <= 1) return null;

    const selectedIndex =
      selectedBranches.get(node.id) ?? node.children.length - 1;
    const totalBranches = node.children.length;

    return {
      current: selectedIndex + 1,
      total: totalBranches,
      hasAlternatives: totalBranches > 1,
    };
  };

  const getSummary = (message: MessageNode) => {
    if (!message.content) return 'Empty message';
    const firstLine = message.content.split('\n')[0];
    return firstLine.length > 25
      ? firstLine.substring(0, 25) + '...'
      : firstLine;
  };

  const selectedPath = getSelectedPath(rootMessage);

  return (
    <div className='w-full h-full border-l border-border bg-background flex flex-col'>
      <div className='p-3 border-b border-border'>
        <h3 className='font-medium text-sm'>Conversation Flow</h3>
        <p className='text-xs text-muted-foreground mt-1'>
          Following selected branches
        </p>
      </div>

      <ScrollArea className='flex-1'>
        <div className='p-3 space-y-2'>
          {selectedPath.map((item, index) => {
            const branchInfo = getBranchSummary(item.node);
            const isLast = index === selectedPath.length - 1;

            return (
              <div key={`${item.node.id}-${index}`} className='relative'>
                {/* Connection line to next message */}
                {!isLast && (
                  <div className='absolute left-4 top-8 w-0.5 h-6 bg-border' />
                )}

                <div
                  className={cn(
                    'flex items-start gap-3 p-2 rounded-lg border transition-colors cursor-pointer',
                    item.isSelected
                      ? 'bg-primary/10 border-primary/30'
                      : 'bg-background hover:bg-accent/50 border-border'
                  )}
                  onClick={() => onJumpToMessage(item.node.id)}
                >
                  {/* Message icon with connection */}
                  <div className='flex flex-col items-center'>
                    <div
                      className={cn(
                        'w-8 h-8 rounded-full flex items-center justify-center',
                        item.node.role === 'user'
                          ? 'bg-blue-100 text-blue-600'
                          : 'bg-green-100 text-green-600'
                      )}
                    >
                      {item.node.role === 'user' ? (
                        <User className='h-4 w-4' />
                      ) : (
                        <Bot className='h-4 w-4' />
                      )}
                    </div>

                    {/* Branch indicator */}
                    {item.hasMultipleBranches && (
                      <div className='mt-1 flex items-center gap-1'>
                        <GitBranch className='h-3 w-3 text-orange-500' />
                        {branchInfo && (
                          <span className='text-xs font-medium text-orange-600'>
                            {branchInfo.current}/{branchInfo.total}
                          </span>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Message content */}
                  <div className='flex-1 min-w-0'>
                    <div className='flex items-center gap-2 mb-1'>
                      <span className='text-xs font-medium capitalize'>
                        {item.node.role}
                      </span>
                      {item.hasMultipleBranches && (
                        <div className='flex items-center gap-1'>
                          <Circle className='h-2 w-2 fill-orange-500 text-orange-500' />
                          <span className='text-xs text-orange-600'>
                            {branchInfo?.total} branches
                          </span>
                        </div>
                      )}
                    </div>

                    <p className='text-sm text-foreground/80 line-clamp-2'>
                      {getSummary(item.node)}
                    </p>

                    {/* Alternative branches preview with navigation */}
                    {item.hasMultipleBranches &&
                      branchInfo &&
                      branchInfo.hasAlternatives && (
                        <div className='mt-2 p-2 bg-muted/50 rounded text-xs'>
                          <div className='flex items-center justify-between'>
                            <div className='flex items-center gap-1 text-muted-foreground'>
                              <ArrowRight className='h-3 w-3' />
                              <span>
                                {branchInfo.total - 1} alternative response
                                {branchInfo.total - 1 === 1 ? '' : 's'}{' '}
                                available
                              </span>
                            </div>

                            {/* Branch navigation controls */}
                            <div className='flex items-center gap-1'>
                              <Button
                                variant='ghost'
                                size='sm'
                                className='h-6 w-6 p-0'
                                onClick={(e) => {
                                  e.stopPropagation();
                                  e.preventDefault();
                                  onBranchChange(
                                    item.node.id,
                                    -1,
                                    branchInfo.total
                                  );
                                }}
                                disabled={branchInfo.current === 1}
                              >
                                <ChevronLeft className='h-3 w-3' />
                              </Button>

                              <span className='text-xs font-medium px-1'>
                                {branchInfo.current}/{branchInfo.total}
                              </span>

                              <Button
                                variant='ghost'
                                size='sm'
                                className='h-6 w-6 p-0'
                                onClick={(e) => {
                                  e.stopPropagation();
                                  e.preventDefault();
                                  onBranchChange(
                                    item.node.id,
                                    1,
                                    branchInfo.total
                                  );
                                }}
                                disabled={
                                  branchInfo.current === branchInfo.total
                                }
                              >
                                <ChevronRight className='h-3 w-3' />
                              </Button>
                            </div>
                          </div>
                        </div>
                      )}
                  </div>
                </div>
              </div>
            );
          })}

          {/* End indicator */}
          <div className='flex justify-center py-2'>
            <div className='text-xs text-muted-foreground bg-muted px-3 py-1 rounded-full'>
              End of conversation
            </div>
          </div>
        </div>
      </ScrollArea>
    </div>
  );
}
