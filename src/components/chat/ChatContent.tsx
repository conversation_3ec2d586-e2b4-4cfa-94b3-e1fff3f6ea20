'use client';
import React, {
  use<PERSON><PERSON>back,
  useMemo,
  useRef,
  useEffect,
  useState,
} from 'react';
import { ChatContext } from '@/providers/ChatProvider';
import ChatMessage from './Message';
import { ChatScrollButton } from '@/components/ui/chat-scroll-button';
import { DUMMY_ROOT_MESSAGE_ID } from '@/constants/chat';
import { ConversationTreeMinimap } from './ConversationTreeMinimap';
import useMediaQuery from '@/hooks/useMediaQuery';
import { MessageNavigator } from '@/components/ui/message-navigator';
import { But<PERSON> } from '@/components/ui/button';
import { TreePine } from 'lucide-react';
import {
  MessageNode,
  Model,
  LLMModel,
  ModelCapabilities,
  ModelArchitecture,
  ModelPricing,
  ProviderSpecificData,
} from '@/lib/supabase/types';
import { Json } from '@/types/database.types';

// Helper function to convert Model to LLMModel for components that expect the raw database type
function modelToLLMModel(model: Model): LLMModel {
  return {
    id: model.id,
    name: model.name,
    display_name: model.display_name,
    created_at: model.created_at,
    updated_at: model.updated_at,
    is_active: model.is_active,
    is_visible_by_default: model.is_visible_by_default,
    allows_file_upload: model.allows_file_upload,
    allows_search: model.allows_search,
    allows_tool_usage: model.allows_tool_usage,
    max_tokens: model.max_tokens,
    openrouter_name: model.openrouter_name,
    provider_id: model.provider_id,
    priority: model.priority,
    tier: model.tier,
    config: model.config,
    context_length: model.context_length,
    description: model.description,
    last_synced: model.last_synced,
    capabilities: model.capabilities as unknown as Json, // Convert back to Json
    supported_parameters: model.supported_parameters as unknown as Json, // Convert back to Json
    architecture: model.architecture as unknown as Json, // Convert back to Json
    pricing: model.pricing as unknown as Json, // Convert back to Json
    provider_specific_data: model.provider_specific_data as unknown as Json, // Convert back to Json
  };
}

// Helper function to convert LLMModel back to Model for components that expect the transformed type
function llmModelToModel(llmModel: LLMModel): Model {
  return {
    id: llmModel.id,
    name: llmModel.name,
    display_name: llmModel.display_name,
    created_at: llmModel.created_at,
    updated_at: llmModel.updated_at,
    is_active: llmModel.is_active,
    is_visible_by_default: llmModel.is_visible_by_default,
    allows_file_upload: llmModel.allows_file_upload,
    allows_search: llmModel.allows_search,
    allows_tool_usage: llmModel.allows_tool_usage,
    max_tokens: llmModel.max_tokens,
    openrouter_name: llmModel.openrouter_name,
    provider_id: llmModel.provider_id,
    priority: llmModel.priority,
    tier: llmModel.tier,
    config: llmModel.config,
    context_length: llmModel.context_length,
    description: llmModel.description,
    last_synced: llmModel.last_synced,
    capabilities: llmModel.capabilities as unknown as ModelCapabilities | null, // Convert from Json to typed interface
    supported_parameters: llmModel.supported_parameters as unknown as
      | string[]
      | null, // Convert from Json to typed array
    architecture: llmModel.architecture as unknown as ModelArchitecture | null, // Convert from Json to typed interface
    pricing: llmModel.pricing as unknown as ModelPricing | null, // Convert from Json to typed interface
    provider_specific_data:
      llmModel.provider_specific_data as unknown as ProviderSpecificData | null, // Convert from Json to typed interface
  };
}

interface ChatContentProps {
  processingModels: string[];
  handleRetryMessage: (chatSessionId: string, modelToRetry?: Model) => void;
  isChatDirty: boolean;
  flattenedMessages: Record<string, MessageNode[]>;
  handleBranchChange: (
    messageId: string,
    direction: number,
    siblingSize: number
  ) => void;
  isChatLoading: boolean;
  editMessage: (modelId: string, messageId: string, content: string) => void;
  workspaceId?: string;
  selectedBranches: Map<string, number>;
  rootMessage: MessageNode | null;
}

export default function ChatContent({
  processingModels,
  handleRetryMessage,
  isChatDirty,
  flattenedMessages,
  handleBranchChange,
  isChatLoading,
  editMessage,
  workspaceId,
  selectedBranches,
  rootMessage,
}: ChatContentProps) {
  const { chatSessions } = React.useContext(ChatContext);
  const chatContainerRefs = useRef<Record<string, HTMLDivElement | null>>({});
  const hasScrolledOnce = useRef<Record<string, boolean>>({});
  const lastUserElement = useRef<HTMLDivElement>(null);
  const chatAreaId = 'chat-scroll-area';
  const [showMessageIndex, setShowMessageIndex] = useState(false);
  const [showTreeMinimap, setShowTreeMinimap] = useState(false);
  const [canShowMessageIndex, setCanShowMessageIndex] = useState(false);
  const [currentVisibleMessageId, setCurrentVisibleMessageId] =
    useState<string>();
  const isDesktop = useMediaQuery('(min-width: 1024px)');
  // const { navigationState, handleScrollNavigation } = useMessageNav(
  //   chatContainerRefs,
  //   chatSessions,
  //   isChatLoading
  // );

  const lastModeUserMessages = useMemo(() => {
    return chatSessions.reduce(
      (acc, model) => {
        const flatMessages = flattenedMessages[model.id] || [];
        const lastUserMessage = flatMessages.findLast(
          (message) => message.role === 'user'
        );
        if (lastUserMessage) {
          acc[model.id] = lastUserMessage;
        }
        return acc;
      },
      {} as Record<string, MessageNode>
    );
  }, [chatSessions, flattenedMessages]);

  const handleMessageMount = useCallback(
    (modelId: string, id: string, element: HTMLDivElement) => {
      if (
        lastModeUserMessages[modelId]?.id === id &&
        processingModels.includes(modelId)
      ) {
        const messageEl = element;
        lastUserElement.current = messageEl;
        // messageEl.scrollIntoView({ behavior: 'smooth', block: 'start' });
        // scroll to the bottom of the chat area
        chatContainerRefs.current[modelId]?.scrollTo({
          top: chatContainerRefs.current[modelId]?.scrollHeight,
          behavior: 'smooth',
        });
      }
    },
    [lastModeUserMessages, processingModels]
  );

  const getMinHeight = useCallback(
    (modelId: string) => {
      if (isChatDirty && chatContainerRefs.current[modelId]) {
        const containerHeight =
          chatContainerRefs.current[modelId]?.clientHeight ?? 0;
        return containerHeight - 120;
      }
      return 'initial';
    },
    [isChatDirty]
  );

  // Get all messages from the primary chat session for the index
  const allMessages = useMemo(() => {
    if (chatSessions.length === 0) return [];
    const primarySessionId = chatSessions[0].id;
    return flattenedMessages[primarySessionId] || [];
  }, [chatSessions, flattenedMessages]);

  // Function to jump to a specific message
  const handleJumpToMessage = useCallback(
    (messageId: string, behavior: ScrollBehavior = 'smooth') => {
      if (chatSessions.length === 0) return;

      // Search across all chat session containers to find the message
      for (const chatSession of chatSessions) {
        const container = chatContainerRefs.current[chatSession.id];
        if (!container) continue;

        const messageElement = container.querySelector(
          `[data-message-id="${messageId}"]`
        );
        if (messageElement) {
          messageElement.scrollIntoView({ behavior, block: 'start' });
          setCurrentVisibleMessageId(messageId);
          return; // Exit once we find and scroll to the message
        }
      }
    },
    [chatSessions]
  );

  // Effect to determine if MessageIndex toggle should be available
  useEffect(() => {
    const shouldBeAvailable = isDesktop && allMessages.length > 3;
    setCanShowMessageIndex(shouldBeAvailable);
    if (!shouldBeAvailable) {
      setShowMessageIndex(false);
    }
  }, [isDesktop, allMessages.length]);

  // Effect to scroll to bottom when messages load or models change
  useEffect(() => {
    if (!isChatLoading) {
      chatSessions.forEach((model) => {
        if (hasScrolledOnce.current[model.id]) return;
        const container = chatContainerRefs.current[model.id];
        if (container) {
          container.scrollTo({
            top: container.scrollHeight,
            behavior: 'instant',
          });
          hasScrolledOnce.current[model.id] = true;
        }
      });
    }
  }, [chatSessions, flattenedMessages, isChatLoading]);

  return (
    <div className='flex-1 flex overflow-hidden'>
      <div
        className={`${showMessageIndex ? 'flex-1' : 'w-full'} ${
          chatSessions.length > 1 ? 'flex' : ''
        } overflow-hidden`}
      >
        {chatSessions.map((chatSession, modelIndex) => {
          const messageList = flattenedMessages[chatSession.id] || [];

          if (isChatLoading) {
            return null;
          }

          return (
            <div
              key={chatSession.id}
              className={`
              relative
              ${chatSessions.length > 1 ? 'w-1/2' : 'w-full'}
              ${modelIndex > 0 ? 'border-l border-border' : ''}
              h-full flex flex-col bg-background text-foreground
            `}
            >
              <div
                className='flex-1 overflow-y-auto p-4 scroll-smooth relative'
                ref={(el) => {
                  chatContainerRefs.current[chatSession.id] = el;
                }}
                id={`${chatAreaId}-${chatSession.id}`}
              >
                <div className='max-w-3xl mx-auto space-y-4'>
                  {messageList.map((message, messageIndex) => {
                    const parentMessage =
                      messageIndex > 0
                        ? messageList[messageIndex - 1]
                        : undefined;
                    const siblings = parentMessage?.children;
                    const siblingsSize = siblings?.length;
                    const currentBranchIndex =
                      siblings && siblings.length > 0
                        ? siblings.findIndex((child) => child.id === message.id)
                        : 0;

                    const validIndex =
                      currentBranchIndex === -1 ? 0 : currentBranchIndex;

                    const isLastMessage =
                      messageIndex === messageList.length - 1;
                    const isProcessing = processingModels.includes(
                      chatSession.id
                    );

                    return (
                      message.id !== DUMMY_ROOT_MESSAGE_ID && (
                        <div
                          key={`${message.id}-${messageIndex}`}
                          className='relative'
                        >
                          <ChatMessage
                            message={message}
                            isLastMessage={isLastMessage}
                            isProcessing={isProcessing}
                            onMount={handleMessageMount}
                            minHeight={
                              isLastMessage && message.role === 'assistant'
                                ? getMinHeight(chatSession.id)
                                : 'initial'
                            }
                            siblingsSize={siblingsSize}
                            handleBranchChange={handleBranchChange}
                            parentMessageId={parentMessage?.id}
                            modelId={chatSession.id}
                            currentBranchIndex={validIndex}
                            retryMessage={(model) =>
                              handleRetryMessage(
                                chatSession.id,
                                model ? llmModelToModel(model) : undefined
                              )
                            }
                            selectedLLMModel={modelToLLMModel(
                              chatSession.model
                            )}
                            editMessage={(messageId, content) =>
                              editMessage(chatSession.id, messageId, content)
                            }
                            conversationId={chatSession.conversationId}
                            workspaceId={workspaceId}
                            jumpToMessage={handleJumpToMessage}
                          />
                        </div>
                      )
                    );
                  })}

                  {chatSession.conversationState === 'error' && (
                    <div className='text-center text-sm text-red-500'>
                      <p>
                        The conversation has encountered an error. Please try
                        again.
                      </p>
                      <button
                        className='underline'
                        onClick={() => handleRetryMessage(chatSession.id)}
                      >
                        Retry
                      </button>
                    </div>
                  )}
                </div>
              </div>

              <ChatScrollButton
                scrollAreaId={`${chatAreaId}-${chatSession.id}`}
              />
              <MessageNavigator
                scrollAreaId={`${chatAreaId}-${chatSession.id}`}
              />

              {/* Tree Minimap Toggle Button - only show on first chat session */}
              {modelIndex === 0 &&
                canShowMessageIndex &&
                rootMessage &&
                isDesktop && (
                  <Button
                    variant='outline'
                    size='icon'
                    className='fixed right-4 top-20 z-10 h-8 w-8 rounded-full bg-background/80 backdrop-blur-sm'
                    onClick={() => setShowTreeMinimap(!showTreeMinimap)}
                  >
                    <TreePine className='h-4 w-4' />
                  </Button>
                )}
            </div>
          );
        })}
      </div>
      {/* Tree Minimap Sidebar */}
      {showTreeMinimap && rootMessage && isDesktop && (
        <div className='w-80 flex-shrink-0'>
          <ConversationTreeMinimap
            rootMessage={rootMessage}
            onJumpToMessage={handleJumpToMessage}
            currentVisibleMessageId={currentVisibleMessageId}
            selectedBranches={selectedBranches}
            onBranchChange={handleBranchChange}
          />
        </div>
      )}
    </div>
  );
}
