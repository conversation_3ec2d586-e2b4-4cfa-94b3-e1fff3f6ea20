import { use<PERSON><PERSON>back, useContext, useMemo, useState } from 'react';
import { Button } from '@/components/ui/button';
import { ArrowLeftRight, X, ChevronDown, Plus } from 'lucide-react';
import { v4 as uuidv4 } from 'uuid';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { isModelVisible } from '@/lib/modelVisibility';
import { Model as DbModel } from '@/lib/supabase/types';
import { ChatContext } from '@/providers/ChatProvider';
import { UserContext } from '@/providers/AuthProvider';
import { useChatModels } from '../hooks/useChatModels';
import { useSubscription } from '@/providers/SubscriptionProvider';
import { Label } from '@/components/ui/label';
import {
  Command,
  CommandEmpty,
  CommandInput,
  CommandList,
} from '@/components/ui/command';
import { ProviderFilterDropdown } from '@/components/chat/ModelSelector/ProviderFilterDropdown';
import { ProviderModelList } from '@/components/chat/ModelSelector/ProviderModelList';
import { ModelSelectionService } from '@/services/modelSelection';
import { useFeatureAccess } from '@/hooks/useFeatureAccess';
import { UpgradePrompt } from '@/components/ui/upgrade-prompt';

// Define types for model selector state
type ModelSelectorState = {
  isOpen: boolean;
  providerFilter: string;
};

export default function ModelSelectorPopover() {
  const { chatSessions, setChatSessions } = useContext(ChatContext);
  const { me } = useContext(UserContext);
  const { canUseComparison } = useFeatureAccess();
  const [showUpgrade, setShowUpgrade] = useState(false);
  const [directModelSelectorOpen, setDirectModelSelectorOpen] = useState(false);
  const [directProviderFilter, setDirectProviderFilter] = useState('all');
  const [comparisonSelectorOpen, setComparisonSelectorOpen] = useState(false);
  const [comparisonProviderFilter, setComparisonProviderFilter] =
    useState('all');

  // Use object state instead of parallel arrays for comparison mode
  const [modelSelectorState, setModelSelectorState] = useState<
    Record<number, ModelSelectorState>
  >({
    0: { isOpen: false, providerFilter: 'all' },
    1: { isOpen: false, providerFilter: 'all' },
  });

  const { canAccessModelTier } = useSubscription();
  const {
    providers,
    getModelsForProvider,
    removeComparisonModel,
    handleModelChange,
  } = useChatModels();

  // Memoize the model visibility check function
  const checkModelVisibility = useMemo(() => {
    return (model: DbModel) => {
      return isModelVisible(
        model.id,
        model.is_visible_by_default ?? true,
        me?.preferences
      );
    };
  }, [me?.preferences]);

  const getModelName = useCallback(
    (providerId: string, modelId: string): string => {
      if (!providerId || !modelId) return getRandomModelName();
      const provider = providers.find((p) => p.id === providerId);
      const model = provider?.models.find((m) => m.id === modelId);
      return model?.display_name || modelId || 'Unknown Model';
    },
    [providers]
  );

  const getRandomModelName = useCallback(() => {
    const provider = providers[Math.floor(Math.random() * providers.length)];
    const model =
      provider?.models[Math.floor(Math.random() * provider.models.length)];
    return model?.display_name || '';
  }, [providers]);

  const handleModelSelection = useCallback(
    (model: DbModel, index: number) => {
      // Preserve existing conversation history and ID when switching models
      handleModelChange(model, index);
      // Track this as an explicit user selection
      ModelSelectionService.updateLastUsedModel(model.id);
      setModelSelectorState((prev) => ({
        ...prev,
        [index]: { ...prev[index], isOpen: false },
      }));
    },
    [handleModelChange]
  );

  const handleProviderFilterChange = useCallback(
    (filter: string, index: number) => {
      setModelSelectorState((prev) => ({
        ...prev,
        [index]: { ...prev[index], providerFilter: filter },
      }));
    },
    []
  );

  const handlePopoverOpenChange = useCallback(
    (isOpen: boolean, index: number) => {
      setModelSelectorState((prev) => ({
        ...prev,
        [index]: { ...prev[index], isOpen },
      }));
    },
    []
  );

  const handleDirectModelSelection = useCallback(
    (model: DbModel) => {
      handleModelChange(model, 0);
      ModelSelectionService.updateLastUsedModel(model.id);
      setDirectModelSelectorOpen(false);
    },
    [handleModelChange]
  );

  const handleDirectProviderFilterChange = useCallback((filter: string) => {
    setDirectProviderFilter(filter);
  }, []);

  const handleComparisonProviderFilterChange = useCallback((filter: string) => {
    setComparisonProviderFilter(filter);
  }, []);

  // Common popover content styles
  const popoverContentStyle = {
    backgroundColor: 'var(--color-input-bg)',
    color: 'var(--color-input-fg)',
    backdropFilter: 'none',
    WebkitBackdropFilter: 'none',
    boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
    border: '1px solid var(--color-border)',
  };

  return (
    <>
      <div className='inline-flex items-center'>
        {/* Single Model Mode - Direct Selection */}
        {chatSessions.length === 1 && (
          <Popover
            open={directModelSelectorOpen}
            onOpenChange={setDirectModelSelectorOpen}
          >
            <PopoverTrigger asChild>
              <Button
                variant='ghost'
                size='sm'
                className='font-medium text-foreground truncate px-2 sm:px-3'
              >
                {chatSessions[0].model?.display_name}
                <ChevronDown className='h-4 w-4 ml-1 opacity-60' />
              </Button>
            </PopoverTrigger>
            <PopoverContent
              className='w-[300px] p-0'
              style={popoverContentStyle}
              align='start'
              onOpenAutoFocus={(e) => {
                e.preventDefault();
                const input = (e.currentTarget as HTMLElement).querySelector(
                  'input[placeholder="Search model..."]'
                ) as HTMLInputElement;
                if (input) {
                  setTimeout(() => input.focus(), 0);
                }
              }}
            >
              <ProviderFilterDropdown
                providers={providers}
                currentFilter={directProviderFilter}
                onFilterChange={handleDirectProviderFilterChange}
                checkModelVisibility={checkModelVisibility}
              />
              <Command>
                <CommandInput placeholder='Search model...' />
                <CommandList>
                  <CommandEmpty>No model found.</CommandEmpty>
                  <ProviderModelList
                    providers={providers}
                    chatSession={chatSessions[0]}
                    checkModelVisibility={checkModelVisibility}
                    canAccessModelTier={canAccessModelTier}
                    onModelSelect={handleDirectModelSelection}
                    getModelsForProvider={getModelsForProvider}
                    currentProviderFilter={directProviderFilter}
                  />
                </CommandList>
              </Command>
            </PopoverContent>
          </Popover>
        )}

        {/* Comparison Mode - Model Comparison Dropdown */}
        {chatSessions.length > 1 && (
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant='ghost'
                size='sm'
                className='text-foreground px-2 sm:px-3 rounded-r-none'
              >
                {chatSessions[0].model?.display_name}
                <ArrowLeftRight className='h-4 w-4 mx-1 sm:mx-2 flex-shrink-0' />
                {chatSessions[1].model?.display_name}
              </Button>
            </PopoverTrigger>
            <PopoverContent className='w-80' style={popoverContentStyle}>
              <div className='grid gap-4'>
                <div className='space-y-2'>
                  <h4 className='font-medium leading-none'>Model Comparison</h4>
                  <p className='text-sm text-muted-foreground'>
                    Select the models for comparison.
                  </p>
                </div>
                <div className='grid gap-3'>
                  {chatSessions.map((chatSession, index) => (
                    <div
                      className='grid grid-cols-3 items-center gap-4 border-b pb-3 last:border-b-0 last:pb-0'
                      key={chatSession.id}
                    >
                      <Label className='col-span-3'>Model {index + 1}</Label>
                      <div className='col-span-3'>
                        <Popover
                          open={modelSelectorState[index]?.isOpen || false}
                          onOpenChange={(isOpen) =>
                            handlePopoverOpenChange(isOpen, index)
                          }
                        >
                          <PopoverTrigger asChild>
                            <Button
                              variant='outline'
                              role='combobox'
                              aria-expanded={
                                modelSelectorState[index]?.isOpen || false
                              }
                              className='w-full justify-between'
                              style={{
                                backgroundColor: 'var(--color-input-bg)',
                                color: 'var(--color-input-fg)',
                                border: '1px solid var(--color-input-border)',
                              }}
                            >
                              {chatSession.model?.provider?.id &&
                              chatSession.model?.id
                                ? getModelName(
                                    chatSession.model.provider.id,
                                    chatSession.model.id
                                  )
                                : 'Select model...'}
                              <ChevronDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent
                            className='w-[300px] p-0'
                            style={popoverContentStyle}
                            align='start'
                            onOpenAutoFocus={(e) => {
                              e.preventDefault();
                              const input = (
                                e.currentTarget as HTMLElement
                              ).querySelector(
                                'input[placeholder="Search model..."]'
                              ) as HTMLInputElement;
                              if (input) {
                                setTimeout(() => input.focus(), 0);
                              }
                            }}
                          >
                            <ProviderFilterDropdown
                              providers={providers}
                              currentFilter={
                                modelSelectorState[index]?.providerFilter ||
                                'all'
                              }
                              onFilterChange={(filter) =>
                                handleProviderFilterChange(filter, index)
                              }
                              checkModelVisibility={checkModelVisibility}
                            />
                            <Command>
                              <CommandInput placeholder='Search model...' />
                              <CommandList>
                                <CommandEmpty>No model found.</CommandEmpty>
                                <ProviderModelList
                                  providers={providers}
                                  chatSession={chatSession}
                                  checkModelVisibility={checkModelVisibility}
                                  canAccessModelTier={canAccessModelTier}
                                  onModelSelect={(model) =>
                                    handleModelSelection(model, index)
                                  }
                                  getModelsForProvider={getModelsForProvider}
                                  currentProviderFilter={
                                    modelSelectorState[index]?.providerFilter ||
                                    'all'
                                  }
                                />
                              </CommandList>
                            </Command>
                          </PopoverContent>
                        </Popover>
                      </div>
                      {index === 1 && removeComparisonModel && (
                        <div className='col-span-3 flex justify-end'>
                          <Button
                            variant='ghost'
                            size='sm'
                            className='text-red-500 hover:text-red-600'
                            onClick={() => removeComparisonModel(index)}
                          >
                            <X className='h-4 w-4 mr-1' /> Remove Comparison
                          </Button>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </PopoverContent>
          </Popover>
        )}
        {/* Add Comparison Button - Only show in single model mode */}
        {chatSessions.length === 1 && providers.length > 0 && (
          <Popover
            open={comparisonSelectorOpen}
            onOpenChange={setComparisonSelectorOpen}
          >
            <PopoverTrigger asChild>
              <Button
                variant='ghost'
                size='sm'
                className='text-muted-foreground hover:text-foreground px-2 ml-1'
                aria-label='Add comparison model'
              >
                <Plus className='h-4 w-4' />
              </Button>
            </PopoverTrigger>
            <PopoverContent
              className='w-[300px] p-0'
              style={popoverContentStyle}
              align='start'
              onOpenAutoFocus={(e) => {
                e.preventDefault();
                const input = (e.currentTarget as HTMLElement).querySelector(
                  'input[placeholder="Search model..."]'
                ) as HTMLInputElement;
                if (input) {
                  setTimeout(() => input.focus(), 0);
                }
              }}
            >
              <div className='p-3 border-b border-border'>
                <h4 className='font-medium leading-none'>
                  Add Comparison Model
                </h4>
                <p className='text-sm text-muted-foreground mt-1'>
                  Select a model to compare responses side-by-side.
                </p>
              </div>
              <ProviderFilterDropdown
                providers={providers}
                currentFilter={comparisonProviderFilter}
                onFilterChange={handleComparisonProviderFilterChange}
                checkModelVisibility={checkModelVisibility}
              />
              <Command>
                <CommandInput placeholder='Search model...' />
                <CommandList>
                  <CommandEmpty>No model found.</CommandEmpty>
                  <ProviderModelList
                    providers={providers}
                    chatSession={chatSessions[0]}
                    checkModelVisibility={checkModelVisibility}
                    canAccessModelTier={canAccessModelTier}
                    onModelSelect={(model) => {
                      if (!canUseComparison) {
                        setShowUpgrade(true);
                        setComparisonSelectorOpen(false);
                        return;
                      }

                      // Check if we already have 2 models for comparison
                      if (chatSessions.length >= 2) {
                        setComparisonSelectorOpen(false);
                        return;
                      }

                      setChatSessions((prev) => [
                        ...prev,
                        {
                          model,
                          parentMessageNode: JSON.parse(
                            JSON.stringify(prev[0].parentMessageNode)
                          ),
                          conversationId: '',
                          conversationState: 'healthy',
                          id: uuidv4(),
                        },
                      ]);

                      ModelSelectionService.updateLastUsedModel(model.id);
                      setComparisonSelectorOpen(false);
                    }}
                    getModelsForProvider={getModelsForProvider}
                    currentProviderFilter={comparisonProviderFilter}
                  />
                </CommandList>
              </Command>
            </PopoverContent>
          </Popover>
        )}

        {/* Remove Comparison Button - Show when in comparison mode */}
        {chatSessions.length > 1 && (
          <Button
            variant='ghost'
            size='sm'
            onClick={() => removeComparisonModel(1)}
            className='text-muted-foreground hover:text-foreground px-2 rounded-l-none -ml-px'
            aria-label='Remove comparison model'
          >
            <X className='h-4 w-4' />
          </Button>
        )}
      </div>

      <UpgradePrompt
        isOpen={showUpgrade}
        onClose={() => setShowUpgrade(false)}
        feature='Model Comparison'
        requiredPlan='premium'
        title='Model Comparison requires Premium Plan'
        description='Upgrade to Premium plan to compare responses from multiple AI models side-by-side.'
      />
    </>
  );
}
