'use client';
import { useState, useContext, useEffect } from 'react';
import { ConversationState } from '@/lib/supabase/types';
import { AppContext } from '@/providers/AppProvider';
import Chat<PERSON>rea from './ChatArea';
import ChatHeader from './ChatHeader';
import { useWorkspaceInfo } from '@/hooks/useWorkspaceInfo';

export type MaybeConversationState = ConversationState | null;

interface MainProps {
  groupConversationId?: string;
  initialPrompt?: string;
  initialDefaultModel?: string;
}

export default function Main({
  groupConversationId: propGroupId,
  initialPrompt,
  initialDefaultModel,
}: MainProps) {
  const { viewportHeight, isMobile } = useContext(AppContext);

  const { workspace } = useWorkspaceInfo(propGroupId);

  const [isTemporary, setIsTemporary] = useState<boolean>(false);

  // cleanup the url after getting the initial prompt and default model
  useEffect(() => {
    if (initialPrompt || initialDefaultModel) {
      const url = new URL(window.location.href);
      url.searchParams.delete('prompt');
      url.searchParams.delete('defaultModel');
      window.history.replaceState({}, '', url.toString());
    }
  }, [initialPrompt, initialDefaultModel]);

  const chatAreaProps = {
    groupConversationId: propGroupId,
    isTemporary,
    setIsTemporary,
    selectedWorkspaceInfo: workspace,
    initialPrompt,
    initialDefaultModel,
  };

  return (
    <div
      className='flex-1 flex flex-col bg-chat-bg text-foreground overflow-hidden relative'
      style={{ height: isMobile ? `${viewportHeight}px` : '100svh' }}
    >
      {/* Header */}
      <ChatHeader
        isTemporary={isTemporary}
        conversationId={propGroupId || ''}
        workspace={workspace}
      />

      {/* Main Content */}
      <div className='flex-1 overflow-hidden'>
        <ChatArea {...chatAreaProps} chatState='chat' />
      </div>
    </div>
  );
}
