import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import ChatInput from '../ChatInput';
import * as SubscriptionProvider from '@/providers/SubscriptionProvider';
void React;

jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
  }),
}));

// Mock the useWorkspaces hook
jest.mock('@/hooks/useWorkspaces', () => ({
  useWorkspaces: () => ({
    workspaces: [],
    isLoading: false,
    error: null,
    refetch: jest.fn(),
  }),
}));

// Mock the custom hook
jest.mock('../hooks/useFileAttach', () => ({
  useFileAttach: ({
    onFileRemoved,
  }: {
    onFileRemoved?: (filename: string) => void;
  }) => ({
    selectedFiles: [],
    setSelectedFiles: jest.fn(),
    fileInputRef: { current: null },
    handleFileChange: jest.fn(),
    handlePaste: jest.fn(),
    onRemoveFile: (filename: string) => {
      if (onFileRemoved) {
        onFileRemoved(filename);
      }
    },
  }),
}));

describe('ChatInput Component', () => {
  const defaultProps = {
    inputValue: '',
    setInputValue: jest.fn(),
    handleSubmit: jest.fn(),
    handleInputContainerClick: jest.fn(),
    isStreaming: false,
    inputContainerRef: { current: null },
    textareaRef: { current: null },
    handleUpload: jest.fn(),
    handleRemoveFile: jest.fn(),
    canUpload: true,
    canSearch: true,
    useWebSearch: false,
    setUseWebSearch: jest.fn(),
    canGenerateImages: false,
    useImageGeneration: false,
    setUseImageGeneration: jest.fn(),
  };

  let useSubSpy: jest.SpyInstance;
  beforeEach(() => {
    jest.clearAllMocks();
    useSubSpy = jest
      .spyOn(SubscriptionProvider, 'useSubscription')
      .mockReturnValue({
        isLoading: false,
        subscription: null,
        canSendMessage: true,
        canAccessModelTier: () => false,
        canAccessPremiumModels: () => false,
        messagesRemaining: 20,
        getPlan: () => 'free',
        refreshSubscription: jest.fn(),
        handleMessageSent: jest.fn(),
        accessibleModelIds: [],
        quotaStatus: {},
        canUseComparison: true,
        refreshQuota: jest.fn(),
        workspaceCount: 0,
        canCreateWorkspace: true,
        canUsePromptLibrary: false,
        canUsePromptEnhancement: false,
      });
  });

  test('renders the input field correctly', () => {
    render(<ChatInput {...defaultProps} />);

    // Check if the textarea is rendered
    const textarea = screen.getByTestId('chat-input');
    expect(textarea).toBeInTheDocument();

    // Check if the send button is rendered
    const sendButton = screen.getByTestId('send-button');
    expect(sendButton).toBeInTheDocument();

    // Check if the attach button is rendered
    const attachButton = screen.getByTestId('attachment-button');
    expect(attachButton).toBeInTheDocument();
  });

  test('disables send button when input is empty', () => {
    render(<ChatInput {...defaultProps} />);

    const sendButton = screen.getByTestId('send-button');
    expect(sendButton).toBeDisabled();
  });

  test('enables send button when input has text', () => {
    render(<ChatInput {...defaultProps} inputValue='Hello world' />);

    const sendButton = screen.getByTestId('send-button');
    expect(sendButton).not.toBeDisabled();
  });

  test('calls handleSubmit when send button is clicked', async () => {
    const user = userEvent.setup();
    const handleSubmit = jest.fn();

    render(
      <ChatInput
        {...defaultProps}
        inputValue='Hello world'
        handleSubmit={handleSubmit}
      />
    );

    const sendButton = screen.getByTestId('send-button');
    await user.click(sendButton);

    expect(handleSubmit).toHaveBeenCalledTimes(1);
  });

  test('calls setInputValue when typing in the textarea', async () => {
    const user = userEvent.setup();
    const setInputValue = jest.fn();

    render(<ChatInput {...defaultProps} setInputValue={setInputValue} />);

    const textarea = screen.getByTestId('chat-input');
    await user.type(textarea, 'H');

    expect(setInputValue).toHaveBeenCalled();
    // user.type calls setInputValue for each character, so we can't check exact input
  });

  test('shows stop button when streaming is true', () => {
    render(<ChatInput {...defaultProps} isStreaming={true} />);

    const textarea = screen.getByTestId('chat-input');
    expect(textarea).not.toBeDisabled();

    const stopButton = screen.getByTestId('stop-button');
    expect(stopButton).toBeInTheDocument();
    expect(screen.queryByTestId('send-button')).not.toBeInTheDocument();
  });

  test('shows web search button when useWebSearch is true', () => {
    render(
      <ChatInput {...defaultProps} canSearch={true} useWebSearch={true} />
    );

    const webButton = screen.getByText('Web');
    expect(webButton).toBeInTheDocument();
  });

  test('does not show web search button when useWebSearch is false and not dirty', () => {
    render(
      <ChatInput {...defaultProps} canSearch={true} useWebSearch={false} />
    );

    const webButton = screen.queryByText('Web');
    expect(webButton).not.toBeInTheDocument();
  });

  test('toggles useWebSearch when the web button is clicked', async () => {
    const user = userEvent.setup();
    const setUseWebSearch = jest.fn();

    // When web search is active, the button should be visible
    render(
      <ChatInput
        {...defaultProps}
        useWebSearch={true}
        setUseWebSearch={setUseWebSearch}
      />
    );

    const webButton = screen.getByText('Web');
    await user.click(webButton);

    expect(setUseWebSearch).toHaveBeenCalledWith(false);
  });

  test('shows warning when message limit is low', () => {
    useSubSpy.mockReturnValue({
      isLoading: false,
      subscription: null,
      canSendMessage: true,
      canAccessModelTier: () => false,
      canAccessPremiumModels: () => false,
      messagesRemaining: 5,
      getPlan: () => 'free',
      refreshSubscription: jest.fn(),
      handleMessageSent: jest.fn(),
      accessibleModelIds: [],
    });

    render(<ChatInput {...defaultProps} />);
    expect(screen.getByText('5 messages left today')).toBeInTheDocument();
  });

  test('shows error when message limit is reached', () => {
    // Override the mock for this specific test
    useSubSpy.mockReturnValue({
      isLoading: false,
      subscription: null,
      canSendMessage: false,
      canAccessModelTier: () => false,
      canAccessPremiumModels: () => false,
      messagesRemaining: 0,
      getPlan: () => 'free',
      refreshSubscription: jest.fn(),
      handleMessageSent: jest.fn(),
      accessibleModelIds: [],
    });

    render(<ChatInput {...defaultProps} />);

    const errorMessage = screen.getByText('Daily message limit reached');
    expect(errorMessage).toBeInTheDocument();

    // Look for the button by icon name or class instead
    const upgradeButton = screen.getByTestId('upgrade-button');
    expect(upgradeButton).toBeInTheDocument();
  });
});
