import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import ChatContent from '../ChatContent';
import { MessageNode, LLMModel } from '@/lib/supabase/types';
import { DUMMY_ROOT_MESSAGE_ID } from '@/constants/chat';
void React;


// Mock scrollTo for tests (not available in JSDOM)
Element.prototype.scrollTo = jest.fn();
HTMLElement.prototype.scrollTo = jest.fn();

// Mock the ChatContext
jest.mock('@/providers/ChatProvider', () => {
  const originalModule = jest.requireActual('react');

  const ChatContext = originalModule.createContext({
    chatSessions: [
      {
        id: 'session-1',
        model: 'claude-3-haiku',
        provider: 'anthropic',
        conversationId: 'conv-1',
        conversationState: 'healthy',
        parentMessageNode: null,
      },
    ],
  });

  return {
    ChatContext,
    useContext: () => originalModule.useContext(ChatContext),
  };
});

// Mock the ChatMessage component
jest.mock('../Message', () => {
  return function MockChatMessage({
    message,
    isLastMessage,
    retryMessage,
    editMessage,
  }: {
    message: MessageNode;
    isLastMessage: boolean;
    retryMessage: (model?: LLMModel) => void;
    editMessage: (messageId: string, content: string) => void;
  }) {
    return (
      <div
        data-testid={`message-${message.id}`}
        className={`message ${message.role}`}
      >
        <div>{message.content}</div>
        {message.role === 'user' && (
          <button
            data-testid={`edit-${message.id}`}
            onClick={() => editMessage(message.id, 'edited content')}
          >
            Edit
          </button>
        )}
        {isLastMessage && message.role === 'assistant' && (
          <button
            data-testid='retry-message'
            onClick={() => retryMessage({
              id: 'claude-3-haiku-id',
              name: 'claude-3-haiku',
              display_name: 'Claude 3 Haiku',
              created_at: '2024-01-01T00:00:00Z',
              updated_at: '2024-01-01T00:00:00Z',
              is_active: true,
              is_visible_by_default: true,
              allows_file_upload: false,
              allows_search: false,
              allows_tool_usage: false,
              max_tokens: 4096,
              openrouter_name: 'anthropic/claude-3-haiku',
              provider_id: 'anthropic',
              priority: 1,
              tier: 'free',
              config: {},
              context_length: 200000,
              description: 'Claude 3 Haiku',
              last_synced: '2024-01-01T00:00:00Z',
              capabilities: null,
              supported_parameters: null,
              architecture: null,
              pricing: null,
              provider_specific_data: null,
            } as LLMModel)}
          >
            Retry
          </button>
        )}
      </div>
    );
  };
});

// Mock the chat-scroll-button component
jest.mock('@/components/ui/chat-scroll-button', () => ({
  ChatScrollButton: () => (
    <button data-testid='scroll-button'>Scroll to Bottom</button>
  ),
}));

describe('ChatContent Component', () => {
  // Create sample messages for testing
  const createSampleMessages = (): Record<string, MessageNode[]> => {
    const rootMessage: MessageNode = {
      id: DUMMY_ROOT_MESSAGE_ID,
      content: '',
      role: 'system',
      created_at: new Date().toISOString(),
      children: [],
      conversation_id: null,
      metadata: {},
      model_id: null,
      parent_message_id: null,
      provider_id: null,
      attachments: [],
      tokens_used: null,
      updated_at: null,
      annotations: [],
      modelData: null,
    };

    const userMessage: MessageNode = {
      id: 'user-msg-1',
      content: 'Hello, this is a test message',
      role: 'user',
      created_at: new Date().toISOString(),
      children: [],
      conversation_id: null,
      metadata: {},
      model_id: null,
      parent_message_id: DUMMY_ROOT_MESSAGE_ID,
      provider_id: null,
      attachments: [],
      tokens_used: null,
      updated_at: null,
      annotations: [],
      modelData: null,
    };

    const assistantMessage: MessageNode = {
      id: 'assistant-msg-1',
      content: 'Hello! I am Claude. How can I help you today?',
      role: 'assistant',
      created_at: new Date().toISOString(),
      children: [],
      conversation_id: null,
      metadata: {},
      model_id: null,
      parent_message_id: 'user-msg-1',
      provider_id: null,
      attachments: [],
      tokens_used: null,
      updated_at: null,
      annotations: [],
      modelData: null,
    };

    rootMessage.children = [userMessage];
    userMessage.children = [assistantMessage];

    return {
      'session-1': [rootMessage, userMessage, assistantMessage],
    };
  };

  const defaultProps = {
    processingModels: [],
    handleRetryMessage: jest.fn(),
    isChatDirty: false,
    flattenedMessages: createSampleMessages(),
    handleBranchChange: jest.fn(),
    isChatLoading: false,
    editMessage: jest.fn(),
    selectedBranches: new Map<string, number>(),
    rootMessage: {
      id: DUMMY_ROOT_MESSAGE_ID,
      content: '',
      role: 'system' as const,
      created_at: new Date().toISOString(),
      children: [],
      conversation_id: null,
      metadata: {},
      model_id: null,
      parent_message_id: null,
      provider_id: null,
      attachments: [],
      tokens_used: null,
      updated_at: null,
      annotations: [],
      modelData: null,
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders messages correctly', () => {
    render(<ChatContent {...defaultProps} />);

    // The dummy root message should not be visible
    expect(
      screen.queryByTestId(`message-${DUMMY_ROOT_MESSAGE_ID}`)
    ).not.toBeInTheDocument();

    // User and assistant messages should be visible
    expect(screen.getByTestId('message-user-msg-1')).toBeInTheDocument();
    expect(screen.getByTestId('message-assistant-msg-1')).toBeInTheDocument();

    // Scroll button should be present
    expect(screen.getByTestId('scroll-button')).toBeInTheDocument();
  });

  test('does not render any messages when chat is loading', () => {
    render(<ChatContent {...defaultProps} isChatLoading={true} />);

    // No messages should be visible during loading
    expect(screen.queryByTestId('message-user-msg-1')).not.toBeInTheDocument();
    expect(
      screen.queryByTestId('message-assistant-msg-1')
    ).not.toBeInTheDocument();
  });

  test('has retry button for messages', () => {
    render(<ChatContent {...defaultProps} />);

    // The retry button should be present in our mocked Message component
    expect(screen.getByTestId('retry-message')).toBeInTheDocument();
  });

  test('calls editMessage when edit button is clicked', async () => {
    const user = userEvent.setup();
    const editMessage = jest.fn();

    render(<ChatContent {...defaultProps} editMessage={editMessage} />);

    const editButton = screen.getByTestId('edit-user-msg-1');
    await user.click(editButton);

    expect(editMessage).toHaveBeenCalledWith(
      'session-1',
      'user-msg-1',
      'edited content'
    );
  });

  test('calls handleRetryMessage when retry button is clicked', async () => {
    const user = userEvent.setup();
    const handleRetryMessage = jest.fn();

    render(
      <ChatContent {...defaultProps} handleRetryMessage={handleRetryMessage} />
    );

    const retryButton = screen.getByTestId('retry-message');
    await user.click(retryButton);

    expect(handleRetryMessage).toHaveBeenCalledWith(
      'session-1',
      expect.objectContaining({
        id: 'claude-3-haiku-id',
        name: 'claude-3-haiku',
        display_name: 'Claude 3 Haiku'
      })
    );
  });

  test('renders user messages correctly', () => {
    render(<ChatContent {...defaultProps} />);

    // Check for user message
    const userMessage = screen.getByTestId('message-user-msg-1');
    expect(userMessage).toBeInTheDocument();
    expect(userMessage).toHaveTextContent('Hello, this is a test message');
  });
});
