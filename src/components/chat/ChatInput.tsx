import React, { useEffect, useState, useContext } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import {
  Plus,
  ArrowUp,
  AlertCircle,
  ArrowRight,
  XIcon,
  FileIcon,
  BookOpen,
  Sparkles,
  RotateCcw,
  Loader2,
  Square,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { useRouter } from 'next/navigation';
import { useSubscription } from '@/providers/SubscriptionProvider';
import Image from 'next/image';
import {
  ALLOWED_FILE_TYPES,
  MAX_FILE_SIZE_MB,
  MAX_FILE_SIZE_BYTES,
} from '@/constants/attachment';
import { useFileAttach } from './hooks/useFileAttach';
import { useWorkspaces } from '@/hooks/useWorkspaces';
import { ChatInputSettings } from './ChatInputSettings';
import { PromptLibraryModal } from '../prompts/PromptLibraryModal';
import { VariableResolutionContext } from '@/lib/promptVariables';
import { Workspace, Model } from '@/lib/supabase/types';
import { ChatContext } from '@/providers/ChatProvider';
import { UserContext } from '@/providers/AuthProvider';
import { createModelSelectionService } from '@/services/modelSelection';

// Define interface for file previews
interface FilePreview {
  name: string;
  type: string;
  size: number;
  previewUrl: string | null; // Base64 for images, null for others
  isImage: boolean; // Flag to differentiate
  file: File; // Keep the original file object
}

// Update Props interface
interface ChatInputProps {
  inputValue: string;
  setInputValue: (value: string) => void;
  handleSubmit: () => void;
  handleInputContainerClick: () => void;
  isStreaming: boolean;
  inputContainerRef: React.RefObject<HTMLDivElement | null>;
  textareaRef: React.RefObject<HTMLTextAreaElement | null>;
  handleUpload: (attachments?: FilePreview[]) => void;
  handleRemoveFile: (fileName: string) => void;
  canUpload: boolean;
  canSearch: boolean;
  useWebSearch: boolean;
  setUseWebSearch: (value: boolean) => void;
  canGenerateImages: boolean;
  useImageGeneration: boolean;
  setUseImageGeneration: (value: boolean) => void;
  isTemporary?: boolean;
  setIsTemporary?: (value: boolean) => void;
  showTemporaryToggle?: boolean;
  selectedWorkspaceId?: string | null;
  setSelectedWorkspaceId?: (workspaceId: string | null) => void;
  canSelectWorkspace?: boolean;
  // New capability checking functions
  checkAllModelsSupport?: (capability: string) => boolean;
  modelSupportsCapability?: (
    model: Model | null | undefined,
    capability: string
  ) => boolean;
}

function ChatInput({
  inputValue,
  setInputValue,
  handleSubmit,
  handleInputContainerClick,
  isStreaming,
  inputContainerRef,
  textareaRef,
  handleUpload,
  handleRemoveFile,
  canUpload,
  canSearch,
  useWebSearch,
  setUseWebSearch,
  canGenerateImages,
  useImageGeneration,
  setUseImageGeneration,
  isTemporary = false,
  setIsTemporary,
  showTemporaryToggle = false,
  selectedWorkspaceId,
  canSelectWorkspace = false,
  setSelectedWorkspaceId = () => {},
  checkAllModelsSupport,
  modelSupportsCapability,
}: ChatInputProps) {
  const router = useRouter();
  const { canSendMessage, messagesRemaining, canUsePromptEnhancement } =
    useSubscription();
  const { workspaces, isLoading: isLoadingWorkspaces } = useWorkspaces();
  const { providers, setChatSessions, updateSessionModelId, abortStream, chatSessions } =
    useContext(ChatContext);
  const { me } = useContext(UserContext);
  const { accessibleModelIds } = useSubscription();
  const hasTyped = inputValue.trim() !== '';

  // Prompt library state
  const [showPromptLibrary, setShowPromptLibrary] = useState(false);

  // Prompt enhancement state
  const [originalPrompt, setOriginalPrompt] = useState('');
  const [isEnhanced, setIsEnhanced] = useState(false);
  const [isEnhancing, setIsEnhancing] = useState(false);
  const [enhancementError, setEnhancementError] = useState<string | null>(null);

  const updateTextareaHeight = () => {
    const textarea = textareaRef.current;
    if (!textarea) return;
    textarea.style.height = 'auto';
    const newHeight = Math.max(24, Math.min(textarea.scrollHeight, 160));
    textarea.style.height = `${newHeight}px`;
  };

  useEffect(() => {
    updateTextareaHeight();
  }, [inputValue]);

  // Auto-focus the textarea when component mounts
  useEffect(() => {
    const timer = setTimeout(() => {
      textareaRef.current?.focus();
    }, 100);
    return () => clearTimeout(timer);
  }, []);

  // Keyboard shortcuts for prompt library and enhancement
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Prompt library (⌘⇧P or Ctrl⇧P)
      if (e.key === 'P' && e.shiftKey && (e.metaKey || e.ctrlKey)) {
        e.preventDefault();
        setShowPromptLibrary(true);
      }

      // Prompt enhancement (⌘⇧E or Ctrl⇧E)
      if (e.key === 'E' && e.shiftKey && (e.metaKey || e.ctrlKey)) {
        e.preventDefault();
        if (
          hasTyped &&
          !isEnhancing &&
          !isStreaming &&
          canUsePromptEnhancement
        ) {
          handleEnhancePrompt();
        }
      }

      // Reset prompt (⌘⇧R or Ctrl⇧R) - only when enhanced
      if (e.key === 'R' && e.shiftKey && (e.metaKey || e.ctrlKey)) {
        e.preventDefault();
        if (isEnhanced && !isEnhancing && !isStreaming) {
          handleResetPrompt();
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [hasTyped, isEnhancing, isStreaming, isEnhanced]);

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInputValue(e.target.value);
  };

  const {
    selectedFiles,
    setSelectedFiles,
    fileInputRef,
    handleFileChange,
    handlePaste,
    onRemoveFile,
  } = useFileAttach({
    maxFiles: 5,
    allowedTypes: ALLOWED_FILE_TYPES,
    maxSizeBytes: MAX_FILE_SIZE_BYTES,
    maxSizeMb: MAX_FILE_SIZE_MB,
    onFilesAdded: handleUpload,
    onFileRemoved: handleRemoveFile,
    canUpload: canUpload,
  });

  const onSubmit = () => {
    if (!canSendMessage) return;
    if (!inputValue.trim() && selectedFiles.length === 0) return;

    handleSubmit(); // handleSubmit in parent now knows about useWebSearch
    setInputValue('');
    setSelectedFiles([]);
    // Resetting web search is now handled by parent if needed
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  const handleUsePrompt = (resolvedPrompt: string, defaultModel?: string) => {
    setInputValue(resolvedPrompt);
    setShowPromptLibrary(false);

    // Handle prompt default model selection using our centralized service
    if (defaultModel && defaultModel.trim() !== '') {
      // Re-initialize chat with prompt default model
      updateSessionModelId(defaultModel);
    }

    // Focus the textarea after a brief delay to ensure the modal is closed
    setTimeout(() => {
      textareaRef.current?.focus();
    }, 100);
  };

  // Create resolution context for prompts
  const promptContext: VariableResolutionContext = {
    conversationTitle: undefined, // Could be passed from parent if available
    lastMessage: undefined, // Could be passed from parent if available
    workspaceContext: selectedWorkspaceId
      ? (workspaces.find((w) => w.id === selectedWorkspaceId) as Workspace)
      : undefined,
  };

  // Handle workspace changes - update model when workspace selection changes
  useEffect(() => {
    if (
      !selectedWorkspaceId ||
      providers.length === 0 ||
      !me ||
      accessibleModelIds.length === 0
    ) {
      return;
    }

    // Fetch workspace and update model if it has a default
    const updateModelForWorkspace = async () => {
      try {
        const response = await fetch(`/api/workspaces/${selectedWorkspaceId}`);
        if (response.ok) {
          const { workspace } = await response.json();
          if (workspace?.default_model_id) {
            // Use model selection service to determine if workspace model should be used
            const modelSelectionService = createModelSelectionService({
              accessibleModelIds,
              userDefaultModelId:
                me?.preferences?.default_model_id || undefined,
              workspaceDefaultModel: workspace.default_model_id,
            });

            const selectionResult = modelSelectionService.selectModel();

            // Only update if workspace model is selected and it's different from current
            if (selectionResult.reason === 'workspace_default') {
              // Find the model object
              const findModelAndProvider = (modelId: string) => {
                for (const provider of providers) {
                  const model = provider.models.find((m) => m.id === modelId);
                  if (model) {
                    return model;
                  }
                }
                return null;
              };

              const modelToUse = findModelAndProvider(selectionResult.modelId);
              if (modelToUse) {
                setChatSessions((prev) => {
                  if (prev.length === 0) return prev;
                  const updated = [...prev];
                  updated[0] = {
                    ...updated[0],
                    model: modelToUse,
                  };
                  return updated;
                });
                // ModelSelectionService.updateLastUsedModel(selectionResult.modelId);
              }
            }
          }
        }
      } catch (error) {
        console.error('Error updating model for workspace:', error);
      }
    };

    updateModelForWorkspace();
  }, [selectedWorkspaceId, providers, me, accessibleModelIds, setChatSessions]);

  // Prompt enhancement functions
  const handleEnhancePrompt = async () => {
    if (!inputValue.trim() || isEnhancing || !canUsePromptEnhancement) return;

    setIsEnhancing(true);
    setEnhancementError(null);

    try {
      const response = await fetch('/api/prompts/enhance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: inputValue.trim(),
          // Let the API endpoint handle the default model configuration
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to enhance prompt');
      }

      const data = await response.json();

      if (data.success && data.enhancedPrompt) {
        setOriginalPrompt(inputValue);
        // remove wrapping quotes if they exist
        const enhancedPrompt = data.enhancedPrompt.replace(/^["']|["']$/g, '');
        setInputValue(enhancedPrompt);
        setIsEnhanced(true);
        setEnhancementError(null);

        // Focus the textarea after enhancement
        setTimeout(() => {
          textareaRef.current?.focus();
        }, 100);
      } else {
        throw new Error(data.error || 'Failed to enhance prompt');
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Failed to enhance prompt';
      setEnhancementError(errorMessage);
      console.error('Prompt enhancement error:', error);

      // If it's a subscription error, show upgrade message
      if (errorMessage.includes('Starter or Premium plan')) {
        setEnhancementError('Upgrade to Starter or Premium to enhance prompts');
      }
    } finally {
      setIsEnhancing(false);
    }
  };

  const handleResetPrompt = () => {
    if (originalPrompt && isEnhanced) {
      setInputValue(originalPrompt);
      setIsEnhanced(false);
      setOriginalPrompt('');
      setEnhancementError(null);

      // Focus the textarea after reset
      setTimeout(() => {
        textareaRef.current?.focus();
      }, 100);
    }
  };

  // Generate the accept string from allowed types
  const acceptString = ALLOWED_FILE_TYPES.join(',');

  // Handle stopping streaming
  const handleStopStreaming = () => {
    chatSessions.forEach(session => {
      abortStream(session.id);
    });
  };

  return (
    <div
      ref={inputContainerRef}
      className={cn(
        'relative max-w-3xl mx-auto rounded-3xl border border-border bg-input-bg text-foreground p-3 cursor-text w-full',
        isStreaming && 'opacity-80',
        !canSendMessage && 'border-red-300 bg-red-50 dark:bg-red-950'
      )}
      onClick={handleInputContainerClick}
      data-testid='chat-input-container'
    >
      <input
        type='file'
        ref={fileInputRef}
        onChange={handleFileChange}
        style={{ display: 'none' }}
        accept={acceptString}
        multiple
      />

      {selectedFiles.length > 0 && (
        <div className='mt-2 mb-2 px-1 flex flex-wrap gap-2 border-t border-border pt-2'>
          {selectedFiles.map((file) => (
            <div
              key={`${file.name}-${file.size}`} // Use a more unique key
              className='relative group w-20 h-20 border border-border rounded-md p-1 flex flex-col items-center justify-center bg-secondary/50'
            >
              {file.isImage && file.previewUrl ? (
                <Image
                  src={file.previewUrl}
                  alt={file.name}
                  width={64}
                  height={64}
                  className='object-contain max-w-full max-h-full'
                />
              ) : (
                <div className='flex flex-col items-center justify-center text-center'>
                  <FileIcon className='h-8 w-8 text-muted-foreground' />
                  <span
                    className='text-xs mt-1 text-muted-foreground truncate w-16'
                    title={file.name}
                  >
                    {file.name}
                  </span>
                </div>
              )}
              <button
                onClick={() => onRemoveFile(file.name)}
                className='absolute top-0 right-0 -mt-2 -mr-2 bg-gray-700 text-white rounded-full p-0.5 opacity-0 group-hover:opacity-100 transition-opacity focus:outline-none focus:ring-1 focus:ring-gray-500'
                aria-label={`Remove ${file.name}`}
              >
                <XIcon className='h-3 w-3' />
              </button>
            </div>
          ))}
        </div>
      )}

      <div className='pb-9'>
        <Textarea
          ref={(el) => {
            if (el) {
              // el.focus(); // Remove auto-focus to avoid issues with paste
              textareaRef.current = el;
            }
          }}
          placeholder={
            !canSendMessage
              ? 'Message limit reached. Upgrade your plan to continue.'
              : isStreaming && selectedFiles.length === 0
                ? 'Waiting for response...'
                : selectedFiles.length > 0 && isStreaming
                  ? 'Uploading...'
                  : selectedFiles.length > 0
                    ? 'Describe the file(s) or ask a question...'
                    : 'Ask Anything'
          }
          className='min-h-[24px] max-h-[160px] w-full rounded-3xl border-0 bg-input-bg text-foreground placeholder:text-gray-400 placeholder:text-base focus-visible:ring-0 focus-visible:ring-offset-0 text-base pl-2 pr-4 pt-0 pb-0 resize-none overflow-y-auto leading-tight'
          value={inputValue}
          onChange={handleInputChange}
          onPaste={handlePaste} // Add paste handler here
          onKeyDown={(e) => {
            if (
              e.key === 'Enter' &&
              !e.shiftKey &&
              !isStreaming &&
              canSendMessage &&
              (inputValue.trim() || selectedFiles.length > 0)
            ) {
              e.preventDefault();
              onSubmit();
            }
          }}
          data-testid='chat-input'
        />
        <div className='absolute bottom-2.5 left-3 flex items-center gap-2'>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant='ghost'
                  size='icon'
                  className='rounded-full h-8 w-8 text-muted-foreground hover:bg-muted disabled:opacity-50'
                  onClick={triggerFileInput}
                  disabled={
                    isStreaming || !canUpload || selectedFiles.length >= 5
                  } // Disable attach if max files reached
                  data-testid='attachment-button'
                >
                  <Plus className='h-5 w-5' />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Attach files (max 5)</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant='ghost'
                  size='icon'
                  className='rounded-full h-8 w-8 text-muted-foreground hover:bg-muted disabled:opacity-50'
                  onClick={() => setShowPromptLibrary(true)}
                  disabled={isStreaming}
                  data-testid='prompt-library-button'
                >
                  <BookOpen className='h-5 w-5' />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Prompt Library (⌘⇧P)</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          {/* Prompt Enhancement Button */}
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant='ghost'
                  size='icon'
                  className={cn(
                    'rounded-full h-8 w-8 text-muted-foreground hover:bg-muted disabled:opacity-50',
                    isEnhanced && 'text-blue-600 dark:text-blue-400'
                  )}
                  onClick={handleEnhancePrompt}
                  disabled={
                    isStreaming ||
                    !hasTyped ||
                    isEnhancing ||
                    !canUsePromptEnhancement
                  }
                  data-testid='prompt-enhance-button'
                >
                  {isEnhancing ? (
                    <Loader2 className='h-5 w-5 animate-spin' />
                  ) : (
                    <Sparkles className='h-5 w-5' />
                  )}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>
                  {!canUsePromptEnhancement
                    ? 'Upgrade to Starter or Premium to enhance prompts'
                    : isEnhancing
                      ? 'Enhancing prompt...'
                      : isEnhanced
                        ? 'Prompt enhanced'
                        : 'Enhance prompt (⌘⇧E)'}
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          {/* Reset Prompt Button - Only show when enhanced */}
          {isEnhanced && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant='ghost'
                    size='icon'
                    className='rounded-full h-8 w-8 text-muted-foreground hover:bg-muted disabled:opacity-50'
                    onClick={handleResetPrompt}
                    disabled={isStreaming || isEnhancing}
                    data-testid='prompt-reset-button'
                  >
                    <RotateCcw className='h-5 w-5' />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Reset to original prompt (⌘⇧R)</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}

          {/* New Settings Component */}
          <ChatInputSettings
            canSearch={canSearch}
            useWebSearch={useWebSearch}
            setUseWebSearch={setUseWebSearch}
            canGenerateImages={canGenerateImages}
            useImageGeneration={useImageGeneration}
            setUseImageGeneration={setUseImageGeneration}
            isTemporary={isTemporary}
            setIsTemporary={setIsTemporary}
            showTemporaryToggle={showTemporaryToggle}
            selectedWorkspaceId={selectedWorkspaceId}
            setSelectedWorkspaceId={setSelectedWorkspaceId}
            workspaces={workspaces}
            isLoadingWorkspaces={isLoadingWorkspaces}
            canSelectWorkspace={canSelectWorkspace}
            checkAllModelsSupport={checkAllModelsSupport}
            modelSupportsCapability={modelSupportsCapability}
          />

          {messagesRemaining > 0 && messagesRemaining < 10 && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className='text-sm text-amber-600 flex items-center gap-1'>
                    <AlertCircle className='h-4 w-4' />
                    <span>{messagesRemaining} messages left today</span>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Upgrade your plan for unlimited messages</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
        </div>
        <div className='absolute bottom-2.5 right-3 flex items-center gap-2'>
          {isStreaming ? (
            <Button
              variant='destructive'
              size='icon'
              className='rounded-full h-8 w-8'
              onClick={handleStopStreaming}
              data-testid='stop-button'
              aria-label='Stop'
            >
              <Square className='h-4 w-4' />
            </Button>
          ) : (
            <Button
              variant='default'
              size='icon'
              className='rounded-full h-8 w-8'
              onClick={onSubmit}
              disabled={
                !canSendMessage ||
                (!hasTyped && selectedFiles.length === 0)
              }
              data-testid='send-button'
              aria-label='Send'
            >
              <ArrowUp className='h-5 w-5' />
            </Button>
          )}
        </div>
        {!canSendMessage && (
          <div className='text-sm text-red-600 flex items-center gap-1'>
            <AlertCircle className='h-4 w-4' />
            <span>Daily message limit reached</span>
            <Button
              variant='outline'
              size='icon'
              className='ml-2 h-4 w-4 bg-transparent text-red-600 hover:bg-transparent hover:text-red-600 hover:scale-110 transition-all duration-200'
              onClick={() => router.push('/settings?tab=billing')}
              data-testid='upgrade-button'
            >
              <ArrowRight className='h-4 w-4' />
            </Button>
          </div>
        )}
        {enhancementError && (
          <div className='text-sm text-red-600 flex items-center gap-1 mt-1'>
            <AlertCircle className='h-4 w-4' />
            <span>{enhancementError}</span>
            {enhancementError.includes('Upgrade to Starter or Premium') && (
              <Button
                variant='outline'
                size='icon'
                className='ml-2 h-4 w-4 bg-transparent text-red-600 hover:bg-transparent hover:text-red-600 hover:scale-110 transition-all duration-200'
                onClick={() => router.push('/settings?tab=billing')}
                data-testid='upgrade-prompt-enhancement-button'
              >
                <ArrowRight className='h-4 w-4' />
              </Button>
            )}
          </div>
        )}
      </div>

      {/* Prompt Library Modal */}
      <PromptLibraryModal
        isOpen={showPromptLibrary}
        onClose={() => setShowPromptLibrary(false)}
        onUsePrompt={handleUsePrompt}
        context={promptContext}
      />
    </div>
  );
}

export default ChatInput;
