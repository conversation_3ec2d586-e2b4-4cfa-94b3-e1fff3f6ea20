import React from 'react';
import { renderHook, act } from '@testing-library/react';
import { useSendMessage } from '../useSendMessage';
import { MessageNode, Model } from '@/lib/supabase/types';
import { createUnifiedStream } from '@/lib/chatApi';

// Add setImmediate polyfill
if (typeof setImmediate === 'undefined') {
  (
    global as unknown as {
      setImmediate: (callback: () => void) => NodeJS.Timeout;
    }
  ).setImmediate = (callback: () => void) => setTimeout(callback, 0);
}

// Mock logger
jest.mock('@/lib/logger', () => ({
  logger: {
    child: () => ({
      error: jest.fn(),
      info: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn(),
    }),
  },
}));

// Mock dependencies
jest.mock('uuid', () => ({
  v4: jest.fn(() => 'mock-uuid'),
}));

jest.mock('sonner', () => ({
  toast: {
    error: jest.fn(),
  },
}));

jest.mock('@/hooks/useAnalytics', () => ({
  useAnalytics: () => ({
    trackMessageSent: jest.fn(),
    trackMessageRetried: jest.fn(),
    trackEvent: jest.fn(),
  }),
}));

jest.mock('@/hooks/useFeatureFlag', () => ({
  useFeatureFlag: () => ({
    isEnabled: false,
    isLoading: false,
    error: null,
  }),
}));

jest.mock('react', () => ({
  ...jest.requireActual('react'),
  useContext: jest.fn(),
}));

jest.mock('@/providers/SubscriptionProvider', () => ({
  useSubscription: () => ({
    quotaStatus: null,
    canSendMessage: true,
    canUseComparison: true,
    getPlan: () => 'free',
  }),
}));

jest.mock('@/lib/chatApi', () => ({
  createUnifiedStream: jest.fn(),
}));

// Helper function to create a mock Model
const createMockModel = (overrides: Partial<Model> = {}) => {
  return {
    id: 'gpt-4',
    name: 'gpt-4',
    display_name: 'GPT-4',
    created_at: null,
    updated_at: null,
    is_active: true,
    is_visible_by_default: true,
    allows_file_upload: false,
    allows_search: false,
    allows_tool_usage: false,
    max_tokens: 8192,
    openrouter_name: 'openai/gpt-4',
    provider_id: 'openai',
    priority: 1,
    tier: 'premium' as const,
    config: {},
    context_length: 8192,
    description: 'GPT-4 model for testing',
    last_synced: new Date().toISOString(),
    capabilities: {
      file_upload: false,
      web_search: false,
      visible_by_default: true,
      image_generation: false,
      code_generation: true,
      function_calling: false,
      reasoning: false,
      structured_output: false,
    },
    supported_parameters: ['temperature', 'max_tokens'],
    architecture: {
      modality: 'text',
      input_modalities: ['text'],
      output_modalities: ['text'],
      tokenizer: 'gpt-4',
      instruct_type: null,
    },
    pricing: {
      prompt: '0.03',
      completion: '0.06',
    },
    provider_specific_data: {
      context_length: 8192,
      max_completion_tokens: 4096,
      is_moderated: false,
      per_request_limits: null,
    },
    ...overrides,
  };
};

// Helper function to create a mock MessageNode
const createMockMessageNode = (
  overrides: Partial<MessageNode>
): MessageNode => {
  return {
    id: 'default-id',
    role: 'user', // Default role
    content: '',
    created_at: new Date().toISOString(),
    children: [],
    parent_message_id: null,
    conversation_id: 'conv-1', // Default conversation_id
    model_id: null,
    provider_id: null,
    tokens_used: 0,
    metadata: {},
    annotations: [],
    attachments: null,
    file_annotations: null,
    updated_at: null,
    modelData: null,
    ...overrides,
  } as MessageNode; // Cast if MessageNode has complex subtypes not fully mocked
};

describe('useSendMessage', () => {
  // Setup common test variables
  const mockSetChatSessions = jest.fn();
  const mockSetInputMessage = jest.fn();

  const mockChatSession = {
    id: 'model-1',
    model: createMockModel(),
    conversationId: 'conv-1',
    conversationState: 'healthy' as const,
    parentMessageNode: {
      id: 'root',
      role: 'system',
      content: '',
      created_at: new Date().toISOString(),
      children: [],
      conversation_id: null,
      model_id: null,
      parent_message_id: null,
      provider_id: null,
      metadata: {},
      annotations: [],
      updated_at: null,
      tokens_used: 0,
      attachments: null,
      modelData: {
        id: 'gpt-4',
        name: 'gpt-4',
        display_name: 'GPT-4',
        created_at: null,
        updated_at: null,
        is_active: true,
        is_visible_by_default: true,
        allows_file_upload: false,
        allows_search: false,
        allows_tool_usage: false,
        max_tokens: 8192,
        openrouter_name: 'openai/gpt-4',
        provider_id: 'openai',
        priority: 1,
        tier: 'premium' as const,
        config: {},
        context_length: 8192,
        description: 'GPT-4 model for testing',
        last_synced: new Date().toISOString(),
        capabilities: {
          file_upload: false,
          web_search: false,
          visible_by_default: true,
          image_generation: false,
          code_generation: true,
          function_calling: false,
          reasoning: false,
          structured_output: false,
        },
        supported_parameters: ['temperature', 'max_tokens'],
        architecture: {
          modality: 'text',
          input_modalities: ['text'],
          output_modalities: ['text'],
          tokenizer: 'gpt-4',
          instruct_type: null,
        },
        pricing: {
          prompt: '0.03',
          completion: '0.06',
        },
        provider_specific_data: {
          context_length: 8192,
          max_completion_tokens: 4096,
          is_moderated: false,
          per_request_limits: null,
        },
      },
    },
  };

  const mockProviders = [
    {
      id: 'provider-1',
      name: 'Provider 1',
      display_name: 'Provider 1',
      created_at: null,
      updated_at: null,
      is_active: true,
      openrouter_name: null,
      supports_native: true,
      supports_openrouter: false,
      supports_tool_usage: false,
      config: {},
      models: [createMockModel()],
    },
  ];

  const mockGetFlattenedMessages = jest.fn(() => [
    createMockMessageNode({
      id: 'user-1',
      role: 'user',
      content: 'Original message',
      // Other necessary fields are covered by createMockMessageNode defaults or can be overridden
    }),
  ]);

  const defaultProps = {
    chatSessions: [mockChatSession],
    setChatSessions: mockSetChatSessions,
    groupConversationId: 'group-1',
    isTestMode: false,
    getFlattenedMessages: mockGetFlattenedMessages,
    providers: mockProviders,
    setInputMessage: mockSetInputMessage,
    useWebSearch: false,
    useImageGeneration: false,
    isTemporary: false,
  };

  // Helper to create a mock Response
  const createMockResponse = (options: Partial<Response>): Response => ({
    ok: true,
    status: 200,
    statusText: 'OK',
    headers: new Headers(),
    redirected: false,
    type: 'basic',
    url: '',
    body: null,
    bodyUsed: false,
    clone: () => createMockResponse(options),
    arrayBuffer: () => Promise.resolve(new ArrayBuffer(0)),
    blob: () => Promise.resolve(new Blob()),
    formData: () => Promise.resolve(new FormData()),
    json: () => Promise.resolve({}),
    text: () => Promise.resolve(''),
    bytes: () => Promise.resolve(new Uint8Array()),
    ...options,
  });

  // Helper to mock fetch responses
  const mockFetchResponses = (
    responses: Array<[string, Partial<Response>]>
  ) => {
    global.fetch = jest.fn().mockImplementation((url) => {
      for (const [pattern, response] of responses) {
        if (url.includes(pattern)) {
          return Promise.resolve(createMockResponse(response));
        }
      }
      return Promise.resolve(
        createMockResponse({ ok: false, statusText: 'Not found' })
      );
    });
  };

  // Helper to create a mock readable stream
  const createMockStream = (chunks: string[]): ReadableStream<Uint8Array> => {
    let chunkIndex = 0;
    const readerProperties = {
      read: jest.fn().mockImplementation(() => {
        if (chunkIndex < chunks.length) {
          const chunk = chunks[chunkIndex];
          chunkIndex++;
          const value = new TextEncoder().encode(chunk ?? ''); // Handle null/undefined chunks
          return Promise.resolve({ value, done: false });
        }
        return Promise.resolve({ value: undefined, done: true });
      }),
      closed: new Promise<void>(() => {}), // Mock for an open stream
      cancel: jest.fn(() => Promise.resolve()),
      releaseLock: jest.fn(),
    };

    return {
      getReader: () => readerProperties,
      locked: false,
      cancel: jest.fn(() => readerProperties.cancel()),
      pipeTo: jest.fn().mockResolvedValue(undefined),
      pipeThrough: jest
        .fn()
        .mockImplementation(
          () => createMockStream([]) as ReadableStream<Uint8Array>
        ),
      tee: jest
        .fn()
        .mockImplementation(() => [
          createMockStream([]) as ReadableStream<Uint8Array>,
          createMockStream([]) as ReadableStream<Uint8Array>,
        ]),
    } as ReadableStream<Uint8Array>;
  };

  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();

    // Mock useContext to return mock values for ChatContext
    jest.spyOn(React, 'useContext').mockImplementation(() => ({
      registerStream: jest.fn(),
      abortStream: jest.fn(),
    }));

    // Default session storage mock
    Object.defineProperty(window, 'sessionStorage', {
      value: {
        getItem: jest.fn(() => null),
        setItem: jest.fn(),
      },
      writable: true,
    });
  });

  afterEach(() => {
    if (jest.isMockFunction(setTimeout)) {
      act(() => {
        jest.runOnlyPendingTimers(); // flush within act
      });
    }
    jest.useRealTimers();
    jest.restoreAllMocks();
  });

  describe('Attachment Handling', () => {
    test('should include attachments in message request', async () => {
      const mockAttachments = [
        {
          name: 'test.pdf',
          type: 'application/pdf',
          url: 'http://example.com/test.pdf',
        },
      ];

      // Mock fetch response
      mockFetchResponses([
        [
          '/api/chat/stream',
          {
            ok: true,
            headers: new Headers({
              'X-Conversation-Id': 'conv-1',
              'X-Assistant-Message-Id': 'assistant-1',
            }),
            body: createMockStream([
              JSON.stringify({ type: 'delta', content: 'Response' }) + '\n',
              '',
            ]),
          },
        ],
      ]);

      const { result } = renderHook(() => useSendMessage(defaultProps));

      // Set attachments
      act(() => {
        result.current.handleUpload = jest.fn().mockImplementation(() => {
          return mockAttachments;
        });
      });

      // Mock createUnifiedStream to verify attachments are passed
      (createUnifiedStream as jest.Mock).mockImplementation((request, callbacks) => {
        // Verify attachments are included in the request
        expect(request.attachments).toContainEqual(
          expect.objectContaining({
            name: 'test.pdf',
          })
        );

        // Simulate successful streaming
        if (callbacks.onInit) {
          callbacks.onInit({
            conversationId: 'conv-1',
            groupConversationId: 'group-1',
            userMessageId: 'user-1',
            assistantMessageId: 'assistant-1',
            isNewConversation: false,
            isTemporary: false,
          });
        }
        if (callbacks.onDelta) {
          callbacks.onDelta('Response');
        }
        if (callbacks.onDone) {
          callbacks.onDone();
        }

        return jest.fn(); // Return abort function
      });

      await act(async () => {
        await result.current.handleSendMessage('Test message with attachment');
      });

      expect(createUnifiedStream).toHaveBeenCalled();
    });
  });

  describe('General Behavior', () => {
    beforeEach(() => {
      // Mock ChatContext for general behavior tests
      jest.spyOn(React, 'useContext').mockImplementation(() => ({
        registerStream: jest.fn(),
        abortStream: jest.fn(),
      }));
      // Mock SSE init endpoint
      mockFetchResponses([
        [
          '/api/chat/stream-sse/init',
          {
            ok: true,
            json: () =>
              Promise.resolve({
                conversationId: 'conv-1',
                assistantMessageId: 'assistant-1',
                userMessageId: 'user-1',
                isNewConversation: false,
                isTemporary: false,
              }),
          },
        ],
      ]);
    });

    test('should not send empty messages', async () => {
      const { result } = renderHook(() => useSendMessage(defaultProps));

      await act(async () => {
        await result.current.handleSendMessage('   ');
      });

      expect(global.fetch).not.toHaveBeenCalled();
    });

    test('should update processing state correctly', async () => {
      // ProcessingModels should clear after SSE completion (onDone)
      const { result } = renderHook(() => useSendMessage(defaultProps));

      // Mock createUnifiedStream to simulate proper streaming flow
      (createUnifiedStream as jest.Mock).mockImplementation((request, callbacks) => {
        // Simulate successful streaming
        if (callbacks.onInit) {
          callbacks.onInit({
            conversationId: 'conv-1',
            groupConversationId: 'group-1',
            userMessageId: 'user-1',
            assistantMessageId: 'assistant-1',
            isNewConversation: false,
            isTemporary: false,
          });
        }
        if (callbacks.onDelta) {
          callbacks.onDelta('Response');
        }
        if (callbacks.onDone) {
          callbacks.onDone();
        }

        return jest.fn(); // Return abort function
      });

      // Should start with empty processing models
      expect(result.current.processingModels.length).toBe(0);

      await act(async () => {
        await result.current.handleSendMessage('Test message');
      });

      // Should have cleared processing after completion
      expect(result.current.processingModels.length).toBe(0);
    });

    test('should update input message on send', async () => {
      // Mock createUnifiedStream to simulate proper streaming flow
      (createUnifiedStream as jest.Mock).mockImplementation((request, callbacks) => {
        // Simulate successful streaming
        if (callbacks.onInit) {
          callbacks.onInit({
            conversationId: 'conv-1',
            groupConversationId: 'group-1',
            userMessageId: 'user-1',
            assistantMessageId: 'assistant-1',
            isNewConversation: false,
            isTemporary: false,
          });
        }
        if (callbacks.onDelta) {
          callbacks.onDelta('Response');
        }
        if (callbacks.onDone) {
          callbacks.onDone();
        }

        return jest.fn(); // Return abort function
      });

      const { result } = renderHook(() => useSendMessage(defaultProps));

      await act(async () => {
        await result.current.handleSendMessage('Test message');
      });

      // Verify the input was cleared
      expect(mockSetInputMessage).toHaveBeenCalledWith('');
    });
  });
});
