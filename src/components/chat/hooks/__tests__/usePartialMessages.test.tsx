import React from 'react';
import { renderHook } from '@testing-library/react';
import { usePartialMessages } from '../usePartialMessages';
import { ChatContext } from '@/providers/ChatProvider';
import { MessageNode } from '@/lib/supabase/types';
import { ChatSession } from '@/providers/ChatProvider';

// Mock the ChatContext
const mockSetChatSessions = jest.fn();
const mockPartialMessages = new Map();

// Mock wrapper component for the hook
const wrapper = ({ children }: { children: React.ReactNode }) => (
  <ChatContext.Provider
    value={{
      providers: [],
      setProviders: jest.fn(),
      chatSessions: [],
      setChatSessions: mockSetChatSessions,
      isSidebarOpen: false,
      toggleSidebar: jest.fn(),
      selectedConversation: null,
      isChatLoading: false,
      isConversationListLoading: false,
      isBackgroundRefreshing: false,
      hasInitiallyLoaded: true,
      setIsChatLoading: jest.fn(),
      loadedConversations: [],
      fetchUserConversations: jest.fn(),
      fetchMoreConversations: jest.fn(),
      hasMoreConversations: false,
      isFetchingMoreConversations: false,
      setSelectedConversation: jest.fn(),
      initializeNewChat: jest.fn(),
      setUserConversations: jest.fn(),
      performSearch: jest.fn(),
      fetchRecentConversations: jest.fn(),
      subscribeToStream: jest.fn(),
      unsubscribeFromStream: jest.fn(),
      partialMessages: mockPartialMessages,
      getPartialMessage: (id: string) => mockPartialMessages.get(id),
      clearPartialMessage: jest.fn(),
      sseConnections: new Map(),
      appDefaultModel: {
        id: 'gpt-4',
        display_name: 'GPT-4',
        provider_id: 'provider-id',
        allows_file_upload: true,
        allows_search: true,
        allows_tool_usage: false,
        config: {},
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        max_tokens: 1000,
        name: 'gpt-4',
        openrouter_name: 'gpt-4',
        priority: 0,
        tier: 'free',
        is_active: true,
        is_visible_by_default: true,
        context_length: 8192,
        description: 'GPT-4 model for testing',
        last_synced: new Date().toISOString(),
        capabilities: {
          file_upload: true,
          web_search: true,
          visible_by_default: true,
          image_generation: false,
          code_generation: true,
          function_calling: false,
          reasoning: false,
          structured_output: false,
        },
        supported_parameters: ['temperature', 'max_tokens'],
        architecture: {
          modality: 'text',
          input_modalities: ['text'],
          output_modalities: ['text'],
          tokenizer: 'gpt-4',
          instruct_type: null,
        },
        pricing: {
          prompt: '0.03',
          completion: '0.06',
        },
      },
      lastModelSelection: null,
      getModelSelectionReason: () => null,
      updateSessionModelId: jest.fn(),
      registerStream: jest.fn(),
      abortStream: jest.fn(),
      streamingSessionIds: new Set(),
    }}
  >
    {children}
  </ChatContext.Provider>
);

describe('usePartialMessages Hook', () => {
  beforeEach(() => {
    mockPartialMessages.clear();
    mockSetChatSessions.mockClear();
  });

  test('should update model tree when partial message has more content', () => {
    // Create mock models and messages
    const mockAssistantMessage: MessageNode = {
      id: 'assistant-message-id',
      content: 'Partial content',
      role: 'assistant',
      created_at: new Date().toISOString(),
      children: [],
      conversation_id: null,
      metadata: {},
      model_id: null,
      parent_message_id: null,
      provider_id: null,
      attachments: [],
      tokens_used: null,
      updated_at: null,
      modelData: null,
      annotations: [],
    };

    const mockUserMessage: MessageNode = {
      id: 'user-message-id',
      content: 'User message',
      role: 'user',
      created_at: new Date().toISOString(),
      children: [mockAssistantMessage],
      conversation_id: null,
      metadata: {},
      model_id: null,
      parent_message_id: null,
      provider_id: null,
      attachments: [],
      tokens_used: null,
      updated_at: null,
      modelData: null,
      annotations: [],
    };

    const mockRootMessage: MessageNode = {
      id: 'root-message-id',
      content: '',
      role: 'system',
      created_at: new Date().toISOString(),
      children: [mockUserMessage],
      conversation_id: null,
      metadata: {},
      model_id: null,
      parent_message_id: null,
      provider_id: null,
      attachments: [],
      tokens_used: null,
      updated_at: null,
      modelData: null,
      annotations: [],
    };

    const mockChatSession: ChatSession = {
      id: 'model-id',
      model: {
        id: 'model-name',
        display_name: 'Model Name',
        name: 'model-name',
        created_at: null,
        updated_at: null,
        is_active: true,
        is_visible_by_default: true,
        allows_file_upload: false,
        allows_search: false,
        allows_tool_usage: false,
        max_tokens: 8192,
        provider_id: 'provider-id',
        priority: 1,
        tier: 'premium' as const,
        config: {},
        openrouter_name: null,
        context_length: 8192,
        description: 'Test model',
        last_synced: new Date().toISOString(),
        capabilities: {
          file_upload: false,
          web_search: false,
          visible_by_default: true,
          image_generation: false,
          code_generation: true,
          function_calling: false,
          reasoning: false,
          structured_output: false,
        },
        supported_parameters: ['temperature', 'max_tokens'],
        architecture: {
          modality: 'text',
          input_modalities: ['text'],
          output_modalities: ['text'],
          tokenizer: 'test-tokenizer',
          instruct_type: null,
        },
        pricing: {
          prompt: '0.01',
          completion: '0.02',
        },
      },
      conversationId: 'conversation-id',
      conversationState: 'healthy',
      parentMessageNode: mockRootMessage,
    };

    // Add a partial message with more content than the actual message
    mockPartialMessages.set('assistant-message-id', {
      content: 'Partial content with more text',
      annotations: [],
    });

    const getFlattenedMessages = jest
      .fn()
      .mockImplementation((root: MessageNode | null) => {
        if (!root) return [];
        return [root, ...root.children, ...root.children[0].children];
      });

    // Render the hook
    renderHook(
      () =>
        usePartialMessages({
          chatSessions: [mockChatSession],
          getFlattenedMessages,
        }),
      { wrapper }
    );

    // The hook should update chatSessions with the partial message content
    expect(mockSetChatSessions).toHaveBeenCalled();

    // The session passed to setChatSessions should have updated content
    const updatedSession = mockSetChatSessions.mock.calls[0][0][0];

    // Check that parentMessageNode was updated correctly
    expect(updatedSession.id).toBe('model-id');

    // When we follow the tree down to the assistant message, it should have the updated content
    const assistantMessageInTree =
      updatedSession.parentMessageNode.children[0].children[0];
    expect(assistantMessageInTree.content).toBe(
      'Partial content with more text'
    );
  });

  test('should not update when partial message has less content', () => {
    // Similar setup as previous test but with more content in the message than in partial
    const mockAssistantMessage: MessageNode = {
      id: 'assistant-message-id',
      content: 'Full content that is longer',
      role: 'assistant',
      created_at: new Date().toISOString(),
      children: [],
      conversation_id: null,
      metadata: {},
      model_id: null,
      parent_message_id: null,
      provider_id: null,
      attachments: [],
      tokens_used: null,
      updated_at: null,
      modelData: null,
      annotations: [],
    };

    const mockUserMessage: MessageNode = {
      id: 'user-message-id',
      content: 'User message',
      role: 'user',
      created_at: new Date().toISOString(),
      children: [mockAssistantMessage],
      conversation_id: null,
      metadata: {},
      model_id: null,
      parent_message_id: null,
      provider_id: null,
      attachments: [],
      tokens_used: null,
      updated_at: null,
      modelData: null,
      annotations: [],
    };

    const mockRootMessage: MessageNode = {
      id: 'root-message-id',
      content: '',
      role: 'system',
      created_at: new Date().toISOString(),
      children: [mockUserMessage],
      conversation_id: null,
      metadata: {},
      model_id: null,
      parent_message_id: null,
      provider_id: null,
      attachments: [],
      tokens_used: null,
      updated_at: null,
      modelData: null,
      annotations: [],
    };

    const mockChatSession: ChatSession = {
      id: 'model-id',
      model: {
        id: 'model-name',
        display_name: 'Model Name',
        name: 'model-name',
        created_at: null,
        updated_at: null,
        is_active: true,
        is_visible_by_default: true,
        allows_file_upload: false,
        allows_search: false,
        allows_tool_usage: false,
        max_tokens: 8192,
        provider_id: 'provider-id',
        priority: 1,
        tier: 'premium' as const,
        config: {},
        openrouter_name: null,
        context_length: 8192,
        description: 'Test model',
        last_synced: new Date().toISOString(),
        capabilities: {
          file_upload: false,
          web_search: false,
          visible_by_default: true,
          image_generation: false,
          code_generation: true,
          function_calling: false,
          reasoning: false,
          structured_output: false,
        },
        supported_parameters: ['temperature', 'max_tokens'],
        architecture: {
          modality: 'text',
          input_modalities: ['text'],
          output_modalities: ['text'],
          tokenizer: 'test-tokenizer',
          instruct_type: null,
        },
        pricing: {
          prompt: '0.01',
          completion: '0.02',
        },
      },
      conversationId: 'conversation-id',
      conversationState: 'healthy',
      parentMessageNode: mockRootMessage,
    };

    // Add a partial message with LESS content than the actual message
    mockPartialMessages.set('assistant-message-id', {
      content: 'Short',
      annotations: [],
    });

    const getFlattenedMessages = jest
      .fn()
      .mockImplementation((root: MessageNode | null) => {
        if (!root) return [];
        return [root, ...root.children, ...root.children[0].children];
      });

    // Render the hook
    renderHook(
      () =>
        usePartialMessages({
          chatSessions: [mockChatSession],
          getFlattenedMessages,
        }),
      { wrapper }
    );

    // The hook should not call setChatSessions
    expect(mockSetChatSessions).not.toHaveBeenCalled();
  });

  test('should return hasPartialMessages true when there are partial messages', () => {
    mockPartialMessages.set('test-id', { content: 'test', annotations: [] });

    const { result } = renderHook(
      () =>
        usePartialMessages({
          chatSessions: [],
          getFlattenedMessages: () => [],
        }),
      { wrapper }
    );

    expect(result.current.hasPartialMessages).toBe(true);
  });

  test('should return hasPartialMessages false when there are no partial messages', () => {
    // Empty partialMessages map
    mockPartialMessages.clear();

    const { result } = renderHook(
      () =>
        usePartialMessages({
          chatSessions: [],
          getFlattenedMessages: () => [],
        }),
      { wrapper }
    );

    expect(result.current.hasPartialMessages).toBe(false);
  });
});
