import { useEffect, useState, useRef, useCallback } from 'react';
import { ChatSession } from '@/providers/ChatProvider';
import {
  Provider,
  MessageNode,
  AttachmentPayload,
  UrlCitationAnnotation,
  StreamMetadataClientData,
} from '@/lib/supabase/types';
import { logger } from '@/lib/logger';
import { toast } from 'sonner';
import {
  addMessageToTree,
  updateMessageInTree,
  removeMessageFromTree,
} from '../utils/addMessageToTree';
import { v4 as uuidv4 } from 'uuid';
import {
  DUMMY_ROOT_MESSAGE_ID,
  PROCESSING_MODELS_STORAGE_KEY,
} from '@/constants/chat';
import { useAnalytics } from '@/hooks/useAnalytics';
import { ChatContext } from '@/providers/ChatProvider';
import { useContext } from 'react';
import { useAttachmentUpload } from './useAttachmentUpload';
import {
  initSse,
  createUnifiedStream,
  updateSse,
  UNIFIED_STREAM_FLAG,
} from '@/lib/chatApi';
import { useFeatureFlag } from '@/hooks/useFeatureFlag';
import { Model } from '@/lib/supabase/types';
import { useSubscription } from '@/providers/SubscriptionProvider';
import * as Sentry from '@sentry/nextjs';

// ---------------------------------------------------------------------------
// Safe wrapper for setting performance measurements without hard dependency on
// a specific Sentry SDK version.  The newer SDKs expose metrics via
// `Sentry.metrics.setMetric`, while older versions used `Sentry.setMeasurement`.
// If neither API is available we simply no-op to avoid runtime/type errors.
// ---------------------------------------------------------------------------
const setMeasurement = (name: string, value: number) => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const anySentry = Sentry as any;
  if (typeof anySentry?.setMeasurement === 'function') {
    anySentry.setMeasurement(name, value);
  } else if (anySentry?.metrics?.setMetric) {
    anySentry.metrics.setMetric(name, value);
  }
};

const log = logger.child({
  module: 'useSendMessage',
});

const messageNodeOtherFields = {
  conversation_id: null,
  metadata: {},
  model_id: null,
  parent_message_id: null,
  provider_id: null,
  attachments: [],
  tokens_used: null,
  updated_at: null,
  modelData: null,
  annotations: [],
};

// Function to get initial state from sessionStorage
const getInitialProcessingModels = (): Set<string> => {
  if (typeof window === 'undefined') {
    return new Set(); // Return empty set during SSR or build time
  }
  try {
    const storedValue = sessionStorage.getItem(PROCESSING_MODELS_STORAGE_KEY);
    if (storedValue) {
      const parsedArray: string[] = JSON.parse(storedValue);
      return new Set(parsedArray);
    }
  } catch (error) {
    log.error(
      { err: error },
      'Error reading processingModels from sessionStorage'
    );
  }
  return new Set();
};

// Batched update types
interface BatchedUpdate {
  content?: string;
  attachments?: AttachmentPayload[];
  annotations?: UrlCitationAnnotation[];
  metadata?: Record<string, unknown>;
}

export const useSendMessage = ({
  chatSessions,
  setChatSessions,
  groupConversationId,
  isTestMode,
  getFlattenedMessages,
  providers,
  setInputMessage,
  useWebSearch,
  useImageGeneration,
  isTemporary = false,
  workspaceId,
  onMessageSent,
}: {
  chatSessions: ChatSession[];
  setChatSessions: React.Dispatch<React.SetStateAction<ChatSession[]>>;
  groupConversationId?: string;
  isTestMode: boolean;
  getFlattenedMessages: (root: MessageNode | null) => MessageNode[];
  providers: Provider[];
  setInputMessage: React.Dispatch<React.SetStateAction<string>>;
  useWebSearch: boolean;
  useImageGeneration: boolean;
  isTemporary?: boolean;
  workspaceId?: string;
  onMessageSent?: () => void;
}) => {
  // Local state to hold the current group ID (initially from prop, then updated from server)
  const [localGroupConversationId, setLocalGroupConversationId] = useState<
    string | undefined
  >(groupConversationId);
  // Use ref for processingModels to avoid unnecessary re-renders
  const processingModels = useRef<Set<string>>(getInitialProcessingModels());
  const [processingModelsArray, setProcessingModelsArray] = useState<string[]>(
    Array.from(processingModels.current)
  );

  // Batched updates for performance
  const batchedUpdates = useRef<Record<string, Record<string, BatchedUpdate>>>(
    {}
  );
  const rafId = useRef<number | null>(null);
  const lastActivityRef = useRef<Record<string, number>>({});
  // Feature flag for unified streaming
  // While the feature flags are still loading, we optimistically assume
  // unified streaming is enabled. This prevents the first message of a new
  // conversation from falling back to legacy streaming when the flag is
  // globally enabled but the client hasn’t finished fetching flags yet.
  const { isEnabled: unifiedStreamEnabled, isLoading: unifiedStreamLoading } =
    useFeatureFlag(UNIFIED_STREAM_FLAG);

  // If flags are still loading, fall back to **enabled** to avoid regression
  // on the very first message. Once the flags finish loading the correct
  // value will propagate on the next render.
  const canUseUnifiedStream = unifiedStreamLoading
    ? true
    : unifiedStreamEnabled;

  // TTFT tracking - maps sessionId to start time and first token flag
  const ttftTracker = useRef<
    Record<string, { startTime: number; firstTokenReceived: boolean }>
  >({});

  // Stream stability tracking
  const streamStabilityTracker = useRef<
    Record<
      string,
      {
        startTime: number;
        tokenCount: number;
        errorCount: number;
        disconnections: number;
        retries: number;
      }
    >
  >({});

  const {
    attachments,
    handleUpload,
    isUploading,
    handleRemoveFile,
    clearAttachments,
  } = useAttachmentUpload();
  // Cast to any to avoid TypeScript missing method errors across SDK versions
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const analytics: any = useAnalytics();
  const { subscribeToStream, registerStream, abortStream } =
    useContext(ChatContext);
  const { quotaStatus, canSendMessage, canUseComparison, getPlan } =
    useSubscription();

  // Keep a ref to current chatSessions to avoid unnecessary state updates in streaming callbacks
  const chatSessionsRef = useRef(chatSessions);
  useEffect(() => {
    chatSessionsRef.current = chatSessions;
  }, [chatSessions]);

  // Keep the local copy in sync when the prop changes.
  // We intentionally omit localGroupConversationId from dependencies to prevent
  // a feedback loop where server-provided updates are immediately overwritten
  // by a stale prop value.
  useEffect(() => {
    if (groupConversationId !== localGroupConversationId) {
      setLocalGroupConversationId(groupConversationId);
    }
  }, [groupConversationId]);

  // Helper functions for processing models management
  const addProcessing = useCallback((id: string) => {
    processingModels.current.add(id);
    lastActivityRef.current[id] = Date.now();
    setProcessingModelsArray(Array.from(processingModels.current));
  }, []);

  const removeProcessing = useCallback((id: string) => {
    processingModels.current.delete(id);
    delete lastActivityRef.current[id];
    delete ttftTracker.current[id]; // Clean up TTFT tracking
    delete streamStabilityTracker.current[id]; // Clean up stream stability tracking
    setProcessingModelsArray(Array.from(processingModels.current));
  }, []);

  // TTFT tracking helpers
  const startTTFTTracking = useCallback((sessionId: string) => {
    ttftTracker.current[sessionId] = {
      startTime: performance.now(),
      firstTokenReceived: false,
    };
  }, []);

  const recordTTFT = useCallback(
    (sessionId: string, model: string, provider: string) => {
      const tracker = ttftTracker.current[sessionId];
      if (!tracker || tracker.firstTokenReceived) {
        return; // Already tracked or no tracking started
      }

      const ttft = performance.now() - tracker.startTime;
      tracker.firstTokenReceived = true;

      // Send TTFT metric to Sentry (vendor-agnostic)
      setMeasurement('TTFT', ttft);
      Sentry.addBreadcrumb({
        category: 'chat-performance',
        message: `TTFT: ${ttft.toFixed(2)}ms`,
        level: 'info',
        data: {
          ttft,
          model,
          provider,
          sessionId,
        },
      });

      // Log for debugging
      log.info(
        {
          sessionId,
          model,
          provider,
          ttft,
        },
        'Time-to-First-Token recorded'
      );

      // Track in analytics
      analytics.trackEvent('chat_ttft', {
        ttft,
        model,
        provider,
        sessionId,
      });

      if (
        process.env.NODE_ENV !== 'production' &&
        typeof performance !== 'undefined' &&
        performance.mark &&
        performance.measure
      ) {
        try {
          performance.mark(`first_token_${sessionId}`);
          performance.measure(
            'TTFT_full',
            `send_clicked_${sessionId}`,
            `first_token_${sessionId}`
          );
          const ttftFull = performance
            .getEntriesByName('TTFT_full')
            .at(-1)?.duration;
          console.log(`[DEV] TTFT (full): ${ttftFull?.toFixed(2)} ms`);
          console.log(`[DEV] TTFT (stream only): ${ttft.toFixed(2)} ms`);
        } catch (err) {
          // Gracefully handle missing marks or other Performance API errors in dev
          console.warn('TTFT measurement failed', err);
        }
      }
    },
    [analytics]
  );

  // Stream stability tracking helpers
  const startStreamStabilityTracking = useCallback((sessionId: string) => {
    streamStabilityTracker.current[sessionId] = {
      startTime: performance.now(),
      tokenCount: 0,
      errorCount: 0,
      disconnections: 0,
      retries: 0,
    };
  }, []);

  const recordStreamError = useCallback(
    (sessionId: string, error: Error, model?: string, provider?: string) => {
      const tracker = streamStabilityTracker.current[sessionId];
      if (tracker) {
        tracker.errorCount++;

        // Send error metrics to Sentry
        Sentry.addBreadcrumb({
          category: 'chat-stream',
          message: `Stream error: ${error.message}`,
          level: 'error',
          data: {
            sessionId,
            model,
            provider,
            errorCount: tracker.errorCount,
            duration: performance.now() - tracker.startTime,
          },
        });

        // Track in analytics
        analytics.trackEvent('chat_stream_error', {
          sessionId,
          model,
          provider,
          error: error.message,
          errorCount: tracker.errorCount,
          duration: performance.now() - tracker.startTime,
        });
      }
    },
    [analytics]
  );

  // Disconnection metric currently unused; can be re-enabled when needed.

  const recordStreamSuccess = useCallback(
    (sessionId: string, model?: string, provider?: string) => {
      const tracker = streamStabilityTracker.current[sessionId];
      if (tracker) {
        const duration = performance.now() - tracker.startTime;
        const tokensPerSecond = tracker.tokenCount / (duration / 1000);

        // Send success metrics to Sentry
        setMeasurement('stream_duration', duration);
        setMeasurement('stream_tokens_per_second', tokensPerSecond);
        Sentry.addBreadcrumb({
          category: 'chat-stream',
          message: 'Stream completed successfully',
          level: 'info',
          data: {
            sessionId,
            model,
            provider,
            duration,
            tokenCount: tracker.tokenCount,
            tokensPerSecond,
            errorCount: tracker.errorCount,
            disconnections: tracker.disconnections,
            retries: tracker.retries,
          },
        });

        // Track in analytics
        analytics.trackEvent('chat_stream_success', {
          sessionId,
          model,
          provider,
          duration,
          tokenCount: tracker.tokenCount,
          tokensPerSecond,
          errorCount: tracker.errorCount,
          disconnections: tracker.disconnections,
          retries: tracker.retries,
        });
      }
    },
    [analytics]
  );

  const recordStreamToken = useCallback((sessionId: string) => {
    const tracker = streamStabilityTracker.current[sessionId];
    if (tracker) {
      tracker.tokenCount++;
    }
  }, []);

  const recordStreamRetry = useCallback(
    (sessionId: string, model?: string, provider?: string) => {
      const tracker = streamStabilityTracker.current[sessionId];
      if (tracker) {
        tracker.retries++;

        // Send retry metrics to Sentry
        Sentry.addBreadcrumb({
          category: 'chat-stream',
          message: 'Stream retry',
          level: 'warning',
          data: {
            sessionId,
            model,
            provider,
            retries: tracker.retries,
            duration: performance.now() - tracker.startTime,
          },
        });

        // Track in analytics
        analytics.trackEvent('chat_stream_retry', {
          sessionId,
          model,
          provider,
          retries: tracker.retries,
          duration: performance.now() - tracker.startTime,
        });
      }
    },
    [analytics]
  );

  // Effect to persist processingModels to sessionStorage
  useEffect(() => {
    try {
      const modelsArray = Array.from(processingModels.current);
      sessionStorage.setItem(
        PROCESSING_MODELS_STORAGE_KEY,
        JSON.stringify(modelsArray)
      );
    } catch (error) {
      log.error(
        { err: error },
        'Error writing processingModels to sessionStorage'
      );
    }
  }, [processingModelsArray]);

  // Cleanup for abandoned streams - only timeout-based cleanup for current sessions
  useEffect(() => {
    const interval = setInterval(() => {
      const now = Date.now();
      const timeoutThreshold = 60_000; // 60 seconds

      setChatSessions((prev) =>
        prev.map((session) => {
          const lastActivity = lastActivityRef.current[session.id];
          const isProcessing = processingModels.current.has(session.id);

          if (
            isProcessing &&
            lastActivity &&
            now - lastActivity > timeoutThreshold
          ) {
            log.warn(`Stream timeout for session ${session.id}`);
            removeProcessing(session.id);

            // Clean up batched updates for timed-out session
            if (batchedUpdates.current[session.id]) {
              delete batchedUpdates.current[session.id];
            }

            return { ...session, conversationState: 'error' as const };
          }
          return session;
        })
      );

      // Clean up batched updates for sessions that no longer exist
      const activeSessionIds = new Set(
        chatSessionsRef.current.map((session) => session.id)
      );

      Object.keys(batchedUpdates.current).forEach((sessionId) => {
        if (!activeSessionIds.has(sessionId)) {
          log.debug(
            `Cleaning up stale batched updates for session ${sessionId}`
          );
          delete batchedUpdates.current[sessionId];
        }
      });
    }, 30_000); // Check every 30 seconds

    return () => clearInterval(interval);
  }, [removeProcessing, setChatSessions]);

  // Batched update flushing
  const flushUpdates = useCallback(() => {
    const updates = batchedUpdates.current;
    if (Object.keys(updates).length === 0) return;

    setChatSessions((prev) => {
      let hasChanges = false;
      const newState = prev.map((session) => {
        const sessionUpdates = updates[session.id];
        if (!sessionUpdates) return session;

        hasChanges = true;
        let updatedNode = session.parentMessageNode;

        // Apply all batched updates for this session
        Object.entries(sessionUpdates).forEach(([messageId, update]) => {
          const nodeUpdates: Partial<MessageNode> = {};

          if (update.content !== undefined) {
            nodeUpdates.content = update.content;
          }
          if (update.attachments !== undefined) {
            nodeUpdates.attachments = update.attachments;
          }
          if (update.annotations !== undefined) {
            nodeUpdates.annotations = update.annotations;
          }
          if (update.metadata !== undefined) {
            nodeUpdates.metadata = update.metadata;
          }

          if (Object.keys(nodeUpdates).length > 0) {
            updatedNode = updateMessageInTree(
              updatedNode,
              messageId,
              nodeUpdates
            );
          }
        });

        return { ...session, parentMessageNode: updatedNode };
      });

      return hasChanges ? newState : prev;
    });

    // IMPORTANT: Do NOT clear batchedUpdates here. If new delta chunks arrive
    // before React has committed the state update triggered above, relying on
    // the rendered tree for the existing content can result in lost tokens
    // (e.g. the first few characters of a message).  By preserving the latest
    // batched content we can build upon it safely in the next frame.  The
    // per-message cache is now cleaned up in the `onDone` callback once the
    // stream finishes.
    // batchedUpdates.current = {};
    rafId.current = null;
  }, [setChatSessions]);

  const scheduleFlush = useCallback(() => {
    if (rafId.current === null) {
      rafId.current = requestAnimationFrame(flushUpdates);
    }
  }, [flushUpdates]);

  // Improved updateSessionState with better change detection
  const updateSessionState = useCallback(
    (
      sessionId: string,
      updateFn: (prevSession: ChatSession) => Partial<ChatSession>
    ) => {
      setChatSessions((prev) => {
        let hasChanges = false;
        const newState = prev.map((session) => {
          if (session.id !== sessionId) return session;

          const calculatedUpdate = updateFn(session);
          // If the update function returns an empty object or null, skip the update
          if (!calculatedUpdate || Object.keys(calculatedUpdate).length === 0) {
            return session;
          }

          hasChanges = true;
          return { ...session, ...calculatedUpdate };
        });

        // Only return new state if there were actual changes
        return hasChanges ? newState : prev;
      });
    },
    [setChatSessions]
  );

  // Create unified callbacks for both SSE and NDJSON streaming with batching
  const makeStreamCallbacks = useCallback(
    (
      sessionId: string,
      assistantMessageId: string,
      model?: string,
      provider?: string
    ) => {
      let currentAssistantId = assistantMessageId;

      const onDelta = (
        text: string,
        replace?: boolean,
        attachments?: unknown[]
      ) => {
        // Update last activity
        lastActivityRef.current[sessionId] = Date.now();

        // Record TTFT when first token arrives (only if text has meaningful content)
        if (text && text.trim() && model && provider) {
          recordTTFT(sessionId, model, provider);
        }

        // Record streaming token for stability tracking
        if (text && text.trim()) {
          recordStreamToken(sessionId);
        }

        // Ensure batch structure exists
        const sessionBatch = (batchedUpdates.current[sessionId] ||= {});
        const messageBatch = (sessionBatch[currentAssistantId] ||= {});

        // Safety check: limit total number of sessions in batch to prevent memory issues
        // Previous implementation removed the first session indiscriminately, which could
        // discard in-flight streams and lead to data loss. We now delete only **inactive**
        // sessions (i.e. those not currently found in `processingModels`). If all sessions
        // are active we skip the cleanup and merely log a warning so that correctness is
        // never compromised by this defensive guard.
        const maxConcurrentSessions = 10;
        const currentSessionCount = Object.keys(batchedUpdates.current).length;
        if (currentSessionCount > maxConcurrentSessions) {
          // Look for the oldest session that is no longer processing/streaming.
          const candidateSessionId = Object.keys(batchedUpdates.current).find(
            (id) => !processingModels.current.has(id)
          );

          if (candidateSessionId) {
            log.warn(
              `Too many concurrent batched sessions (${currentSessionCount}). Removing stale session ${candidateSessionId}`
            );
            delete batchedUpdates.current[candidateSessionId];
          } else {
            // All sessions are still active; avoid deleting any to prevent data loss.
            log.warn(
              `Too many concurrent batched sessions (${currentSessionCount}) but all are active. Skipping cleanup to avoid potential data loss.`
            );
          }
        }

        // Build content from already-batched data first, fall back to rendered tree
        const baseContent = replace
          ? ''
          : (messageBatch.content ?? // Check batched content first
            (() => {
              const currentSession = chatSessionsRef.current.find(
                (s) => s.id === sessionId
              );
              if (currentSession) {
                const currentNode = findNodeById(
                  currentSession.parentMessageNode,
                  currentAssistantId
                );
                return currentNode?.content || '';
              }
              return '';
            })());

        // Update content by building on base content
        messageBatch.content = replace ? text : baseContent + text;

        // Handle attachments if provided
        if (attachments !== undefined) {
          messageBatch.attachments = attachments as AttachmentPayload[];
        }

        scheduleFlush();
      };

      const onAnnotation = (annotation: UrlCitationAnnotation) => {
        lastActivityRef.current[sessionId] = Date.now();

        // Ensure batch structure exists
        const sessionBatch = (batchedUpdates.current[sessionId] ||= {});
        const messageBatch = (sessionBatch[currentAssistantId] ||= {});

        // Build annotations from already-batched data first, fall back to rendered tree
        const baseAnnotations =
          messageBatch.annotations ?? // Check batched annotations first
          (() => {
            const currentSession = chatSessionsRef.current.find(
              (s) => s.id === sessionId
            );
            const currentNode = currentSession
              ? findNodeById(
                  currentSession.parentMessageNode,
                  currentAssistantId
                )
              : null;
            return currentNode?.annotations || [];
          })();

        // Append new annotation to base annotations
        messageBatch.annotations = [...baseAnnotations, annotation];

        scheduleFlush();
      };

      const onMetadata = (metadata: StreamMetadataClientData) => {
        lastActivityRef.current[sessionId] = Date.now();

        // Batch the update
        if (!batchedUpdates.current[sessionId]) {
          batchedUpdates.current[sessionId] = {};
        }

        batchedUpdates.current[sessionId][currentAssistantId] = {
          ...batchedUpdates.current[sessionId][currentAssistantId],
          metadata: metadata as Record<string, unknown>,
        };

        scheduleFlush();
      };

      const onError = (error: Error) => {
        // Check if this is an abort error (user cancelled streaming)
        const isAbortError =
          error.name === 'AbortError' ||
          error.message.includes('BodyStreamBuffer was aborted') ||
          error.message.includes('aborted');

        if (isAbortError) {
          // User cancelled streaming - this is normal, just log it for debugging
          log.info({ sessionId }, 'Stream cancelled by user');
        } else {
          // Real error - log and show to user
          log.error({ err: error }, 'Stream error');
          toast.error(`Error: ${error.message}`);

          // Record stream error for stability tracking (only for real errors)
          recordStreamError(sessionId, error, model, provider);
        }

        // Apply any pending batched updates before we clear them so no tokens are lost
        flushUpdates();

        // For abort errors, keep conversation healthy since it was user-initiated
        updateSessionState(sessionId, () => ({
          conversationState: isAbortError ? 'healthy' : 'error',
        }));

        // Ensure processing is removed even if called multiple times
        if (processingModels.current.has(sessionId)) {
          removeProcessing(sessionId);
        }

        // Clean up cached batched content for this assistant message now that
        // streaming has errored out. This mirrors the cleanup performed in
        // onDone to avoid leaking memory when a stream fails.
        if (batchedUpdates.current[sessionId]) {
          delete batchedUpdates.current[sessionId][currentAssistantId];
          if (Object.keys(batchedUpdates.current[sessionId]).length === 0) {
            delete batchedUpdates.current[sessionId];
          }
        }
      };

      const onDone = () => {
        log.info('Stream complete');

        // Record stream success for stability tracking
        recordStreamSuccess(sessionId, model, provider);

        // Flush any remaining batched updates synchronously before cleanup
        flushUpdates();

        updateSessionState(sessionId, () => ({ conversationState: 'healthy' }));
        // Ensure processing is removed even if called multiple times
        if (processingModels.current.has(sessionId)) {
          removeProcessing(sessionId);
        }

        // ✨ NEW: Clean up streaming registration so the sidebar indicator
        // fully disappears and cannot resurface when navigating away and back.
        // We reuse the provider-level `abortStream` helper which removes the
        // session from `streamingSessionIds` and disposes of the controller.
        abortStream(sessionId);

        // Clean up cached batched content for this assistant message now that
        // streaming has finished. This keeps memory usage bounded and ensures
        // subsequent messages start with a fresh slate.
        if (batchedUpdates.current[sessionId]) {
          delete batchedUpdates.current[sessionId][currentAssistantId];
          if (Object.keys(batchedUpdates.current[sessionId]).length === 0) {
            delete batchedUpdates.current[sessionId];
          }
        }
      };

      const setAssistantId = (newId: string) => {
        currentAssistantId = newId;
      };

      return {
        onDelta,
        onAnnotation,
        onError,
        onDone,
        onMetadata,
        setAssistantId,
      };
    },
    [
      updateSessionState,
      scheduleFlush,
      removeProcessing,
      recordTTFT,
      recordStreamToken,
      recordStreamError,
      recordStreamSuccess,
      flushUpdates,
      abortStream,
    ]
  );

  const handleAddSessionResponse = useCallback(
    async (
      session: ChatSession,
      index: number,
      messageText: string,
      lastMessageId: string | null,
      passedGroupConversationId: string,
      useWebSearch: boolean,
      useImageGeneration: boolean
    ) => {
      if (!session.model) {
        removeProcessing(session.id);
        return;
      }

      // Capture workspaceId at call time to prevent race conditions
      const stableWorkspaceId = workspaceId;

      try {
        const { newAssistantMessage, newUserMessage } = addNewMessageToModels(
          session.id,
          messageText,
          lastMessageId,
          attachments
        );

        // DEV: mark the moment the user clicked "Send" for full TTFT measurement
        if (
          process.env.NODE_ENV !== 'production' &&
          typeof performance !== 'undefined' &&
          performance.mark
        ) {
          performance.mark(`send_clicked_${session.id}`);
        }

        // Check if unified stream is enabled
        if (canUseUnifiedStream) {
          // Use unified stream approach
          const {
            onDelta,
            onAnnotation,
            onError,
            onDone,
            onMetadata,
            setAssistantId,
          } = makeStreamCallbacks(
            session.id,
            newAssistantMessage.id, // placeholder
            session.model.display_name,
            session.model.provider?.name
          );

          // Start TTFT tracking when message is sent
          startTTFTTracking(session.id);

          // Start stream stability tracking
          startStreamStabilityTracking(session.id);

          const abortStreamFn = createUnifiedStream(
            {
              message: messageText,
              model: session.model.id,
              conversationId: session.conversationId,
              comparisonIndex: chatSessions.length > 1 ? index : null,
              isComparison: chatSessions.length > 1,
              groupConversationId: passedGroupConversationId,
              isTestMode,
              parentMessageId: lastMessageId ?? undefined,
              attachments,
              useWebSearch,
              useImageGeneration,
              isTemporary,
              workspaceId: stableWorkspaceId,
            },
            {
              onDelta,
              onAnnotation,
              onError: (err) => {
                onError(err);
                abortStream(session.id);
              },
              onDone: () => {
                onDone();
              },
              onMetadata,
              onInit: (initData) => {
                if (initData.assistantMessageId) {
                  setAssistantId(initData.assistantMessageId);
                }
                // Update our local state so future messages reuse this group ID
                if (initData.groupConversationId) {
                  // Persist the server-assigned group conversation ID on the local session
                  updateSessionState(session.id, () => ({
                    groupConversationId: initData.groupConversationId,
                  }));
                  setLocalGroupConversationId(initData.groupConversationId);
                }

                onMessageSent?.();

                // Sync IDs with server-assigned ones
                if (initData.userMessageId !== newUserMessage.id) {
                  updateSessionState(session.id, (prevSession) => ({
                    parentMessageNode: updateMessageInTree(
                      prevSession.parentMessageNode,
                      newUserMessage.id,
                      { id: initData.userMessageId }
                    ),
                  }));
                }
                if (initData.assistantMessageId !== newAssistantMessage.id) {
                  updateSessionState(session.id, (prevSession) => ({
                    parentMessageNode: updateMessageInTree(
                      prevSession.parentMessageNode,
                      newAssistantMessage.id,
                      { id: initData.assistantMessageId }
                    ),
                  }));
                }

                // Handle new conversation navigation
                if (
                  initData.isNewConversation &&
                  initData.groupConversationId
                ) {
                  updateSessionState(session.id, () => ({
                    conversationId: initData.conversationId,
                  }));
                  if (!initData.isTemporary && !stableWorkspaceId) {
                    window.history.replaceState(
                      {},
                      '',
                      `/chat/${initData.groupConversationId}`
                    );
                  }
                }
              },
            },
            canUseUnifiedStream
          );

          registerStream(session.id, {
            abort: abortStreamFn,
            groupConversationId: passedGroupConversationId,
          });

          // For unified streams, we manage the connection lifecycle internally
          // The createUnifiedStream function handles the EventSource management
        } else {
          // Use legacy dual-request approach
          const {
            conversationId: newConversationId,
            groupConversationId: newGroupId,
            assistantMessageId,
            userMessageId,
            isNewConversation,
            isTemporary: isTemp,
          } = await initSse(
            {
              message: messageText,
              model: session.model.id,
              conversationId: session.conversationId,
              comparisonIndex: chatSessions.length > 1 ? index : null,
              isComparison: chatSessions.length > 1,
              groupConversationId: passedGroupConversationId,
              isTestMode,
              parentMessageId: lastMessageId ?? undefined,
              attachments,
              useWebSearch,
              useImageGeneration,
              isTemporary,
              workspaceId: stableWorkspaceId,
            },
            canUseUnifiedStream
          );

          // Update our local state so future messages reuse this group ID, if provided
          if (newGroupId) {
            setLocalGroupConversationId(newGroupId);
          }

          onMessageSent?.();

          // Sync IDs with server-assigned ones
          if (userMessageId !== newUserMessage.id) {
            updateSessionState(session.id, (prevSession) => ({
              parentMessageNode: updateMessageInTree(
                prevSession.parentMessageNode,
                newUserMessage.id,
                { id: userMessageId }
              ),
            }));
          }
          if (assistantMessageId !== newAssistantMessage.id) {
            updateSessionState(session.id, (prevSession) => ({
              parentMessageNode: updateMessageInTree(
                prevSession.parentMessageNode,
                newAssistantMessage.id,
                { id: assistantMessageId }
              ),
            }));
          }

          // Handle new conversation navigation
          if (isNewConversation && newGroupId) {
            // Persist server-assigned IDs locally
            updateSessionState(session.id, () => ({
              conversationId: newConversationId,
              groupConversationId: newGroupId,
            }));
            if (!isTemp && !stableWorkspaceId) {
              window.history.replaceState({}, '', `/chat/${newGroupId}`);
            }
          }

          // Start TTFT tracking when message is sent
          startTTFTTracking(session.id);

          // Start stream stability tracking
          startStreamStabilityTracking(session.id);

          // Subscribe to SSE with unified callbacks including TTFT tracking
          const { onDelta, onAnnotation, onError, onDone, onMetadata } =
            makeStreamCallbacks(
              session.id,
              assistantMessageId,
              session.model.display_name,
              session.model.provider?.name
            );
          subscribeToStream(
            assistantMessageId,
            newConversationId!,
            session.model.id,
            isTestMode,
            useWebSearch,
            useImageGeneration,
            onDelta,
            onAnnotation,
            onError,
            onDone,
            onMetadata,
            stableWorkspaceId
          );
        }
      } catch (error) {
        log.error({ err: error }, 'Error sending message');
        toast.error('Sorry, there was an error processing your request.');

        // Ensure processing is removed on error
        removeProcessing(session.id);

        setChatSessions((prev) => {
          const updated = [...prev];
          updated[index].conversationState = 'error';
          return updated;
        });

        setChatSessions((prev) => {
          const updated = [...prev];
          const currentSession =
            updated.find((s) => s.id === session.id) ?? updated[0];

          const currentBranch = getFlattenedMessages(
            currentSession.parentMessageNode
          );
          const lastMessage = currentBranch[currentBranch.length - 1];

          if (lastMessage?.role === 'assistant' && lastMessage.content === '') {
            const newTree = removeMessageFromTree(
              currentSession.parentMessageNode,
              lastMessage.id
            );
            const index = updated.findIndex((s) => s.id === currentSession.id);
            updated[index] = { ...currentSession, parentMessageNode: newTree };
          }

          return updated;
        });
      } finally {
        clearAttachments();
        // Track message sent event
        analytics.trackMessageSent(
          passedGroupConversationId,
          uuidv4(), // Generate a message ID since we don't have it yet
          chatSessions[0]?.model.display_name || 'unknown',
          messageText.length
        );
      }
    },
    [
      chatSessions,
      setChatSessions,
      getFlattenedMessages,
      attachments,
      useWebSearch,
      useImageGeneration,
      isTemporary,
      analytics,
      subscribeToStream,
      updateSessionState,
      workspaceId,
      makeStreamCallbacks,
      removeProcessing,
      onMessageSent,
      clearAttachments,
      setLocalGroupConversationId,
      startTTFTTracking,
      startStreamStabilityTracking,
      registerStream,
      abortStream,
    ]
  );

  const handleUpdateSessionResponse = useCallback(
    async (
      session: ChatSession,
      lastUserMessageId: string,
      newContent?: string,
      useWebSearch?: boolean,
      useImageGeneration?: boolean,
      modelToRetry?: Model
    ) => {
      if (!session.model) {
        removeProcessing(session.id);
        return;
      }

      try {
        const assistantMessageId = uuidv4();
        const newAssistantMessage = {
          id: assistantMessageId,
          content: '',
          role: 'assistant',
          created_at: new Date().toISOString(),
          children: [],
          ...messageNodeOtherFields,
        };

        updateSessionState(session.id, (prevSession) => ({
          parentMessageNode: updateMessageInTree(
            prevSession.parentMessageNode,
            lastUserMessageId,
            {
              children: [newAssistantMessage],
            }
          ),
        }));

        const finalModelToRetry = modelToRetry ?? session.model;

        // DEV: mark retry/edit send click for full TTFT measurement
        if (
          process.env.NODE_ENV !== 'production' &&
          typeof performance !== 'undefined' &&
          performance.mark
        ) {
          performance.mark(`send_clicked_${session.id}`);
        }

        // Common metrics setup
        startTTFTTracking(session.id);
        startStreamStabilityTracking(session.id);

        // Record this as a retry
        recordStreamRetry(
          session.id,
          finalModelToRetry.display_name,
          finalModelToRetry.provider?.name
        );

        const stableWorkspaceId = workspaceId;

        if (canUseUnifiedStream) {
          // ------------------------------
          // Unified streaming path
          // ------------------------------
          const {
            onDelta,
            onAnnotation,
            onError,
            onDone,
            onMetadata,
            setAssistantId,
          } = makeStreamCallbacks(
            session.id,
            assistantMessageId, // placeholder
            finalModelToRetry.display_name,
            finalModelToRetry.provider?.name
          );

          const abortStreamFn = createUnifiedStream(
            {
              messageId: lastUserMessageId,
              model: finalModelToRetry.id,
              conversationId: session.conversationId,
              isTestMode,
              newContent,
              isEdit: !!newContent,
              useWebSearch: useWebSearch ?? false,
              useImageGeneration: useImageGeneration ?? false,
              workspaceId: stableWorkspaceId,
            },
            {
              onDelta,
              onAnnotation,
              onError: (err) => {
                onError(err);
                abortStream(session.id);
              },
              onDone: () => {
                onDone();
              },
              onMetadata,
              onInit: (initData) => {
                if (initData.assistantMessageId) {
                  setAssistantId(initData.assistantMessageId);
                }

                // Sync IDs with server-assigned ones
                if (initData.groupConversationId) {
                  updateSessionState(session.id, () => ({
                    groupConversationId: initData.groupConversationId,
                  }));
                }
                if (initData.assistantMessageId !== assistantMessageId) {
                  updateSessionState(session.id, (prevSession) => ({
                    parentMessageNode: updateMessageInTree(
                      prevSession.parentMessageNode,
                      assistantMessageId,
                      { id: initData.assistantMessageId }
                    ),
                  }));
                }
              },
            },
            canUseUnifiedStream
          );

          registerStream(session.id, {
            abort: abortStreamFn,
            // Use the correct group conversation ID (do not fall back to conversationId)
            groupConversationId: session.groupConversationId || '',
          });
        } else {
          // ------------------------------
          // Legacy SSE fallback path
          // ------------------------------
          const updateRes = await updateSse(
            {
              messageId: lastUserMessageId,
              model: finalModelToRetry.id,
              isTestMode,
              newContent,
              isEdit: !!newContent,
              useWebSearch: useWebSearch ?? false,
              useImageGeneration: useImageGeneration ?? false,
            },
            canUseUnifiedStream
          );

          const {
            assistantMessageId: serverAssistantId,
            conversationId: newConversationId,
          } = updateRes;

          // Sync IDs if they differ
          if (serverAssistantId !== assistantMessageId) {
            updateSessionState(session.id, (prevSession) => ({
              parentMessageNode: updateMessageInTree(
                prevSession.parentMessageNode,
                assistantMessageId,
                { id: serverAssistantId }
              ),
            }));
          }

          const { onDelta, onAnnotation, onError, onDone, onMetadata } =
            makeStreamCallbacks(
              session.id,
              serverAssistantId,
              finalModelToRetry.display_name,
              finalModelToRetry.provider?.name
            );

          subscribeToStream(
            serverAssistantId,
            newConversationId ?? session.conversationId!,
            finalModelToRetry.id,
            isTestMode,
            useWebSearch ?? false,
            useImageGeneration ?? false,
            onDelta,
            onAnnotation,
            onError,
            onDone,
            onMetadata,
            stableWorkspaceId
          );
        }
      } catch (error) {
        log.error({ err: error }, 'Error sending message');
        toast.error('Sorry, there was an error processing your request.');

        // Ensure processing is removed on error
        removeProcessing(session.id);

        setChatSessions((prev) => {
          const updated = [...prev];
          const session =
            updated.find((s) => s.id === modelToRetry?.id) ?? updated[0];
          session.conversationState = 'error';
          return updated;
        });

        setChatSessions((prev) => {
          const updated = [...prev];
          const currentSession =
            updated.find((s) => s.id === session.id) ?? updated[0];

          const currentBranch = getFlattenedMessages(
            currentSession.parentMessageNode
          );
          const lastMessage = currentBranch[currentBranch.length - 1];

          if (lastMessage?.role === 'assistant' && lastMessage.content === '') {
            const newTree = removeMessageFromTree(
              currentSession.parentMessageNode,
              lastMessage.id
            );
            const index = updated.findIndex((s) => s.id === currentSession.id);
            updated[index] = { ...currentSession, parentMessageNode: newTree };
          }

          return updated;
        });
      } finally {
        clearAttachments();
        // Track message sent event
        analytics.trackMessageRetried(
          lastUserMessageId,
          session.model.display_name
        );
      }
    },
    [
      chatSessions,
      groupConversationId,
      setChatSessions,
      getFlattenedMessages,
      providers,
      useWebSearch,
      subscribeToStream,
      updateSessionState,
      makeStreamCallbacks,
      removeProcessing,
      clearAttachments,
      analytics,
      startTTFTTracking,
      startStreamStabilityTracking,
      recordStreamRetry,
      updateSse,
      workspaceId,
      registerStream,
      abortStream,
    ]
  );

  const addNewMessageToModels = useCallback(
    (
      sessionId: string,
      messageText: string,
      lastMessageId: string | null,
      attachments?: AttachmentPayload[]
    ): { newAssistantMessage: MessageNode; newUserMessage: MessageNode } => {
      const newAssistantMessage: MessageNode = {
        id: uuidv4(),
        content: '',
        role: 'assistant',
        created_at: new Date().toISOString(),
        children: [],
        ...messageNodeOtherFields,
      };

      let newMessage: MessageNode;

      const newUserMessage: MessageNode = {
        id: uuidv4(),
        content: messageText,
        role: 'user',
        created_at: new Date().toISOString(),
        children: [newAssistantMessage],
        ...messageNodeOtherFields,
        attachments: attachments || [],
      };

      // if lastMessageId is undefined, create a new dummy root message and use the newUserMessage as the first child
      if (!lastMessageId) {
        newMessage = {
          id: DUMMY_ROOT_MESSAGE_ID,
          content: '',
          role: 'system',
          created_at: new Date().toISOString(),
          children: [newUserMessage],
          ...messageNodeOtherFields,
          attachments: [],
        };
      } else {
        newMessage = newUserMessage;
      }

      setChatSessions((prev) =>
        prev.map((s) => {
          if (s.id !== sessionId) return s;

          const withUserMessage = addMessageToTree(
            s.parentMessageNode,
            lastMessageId ?? undefined,
            newMessage
          );

          return {
            ...s,
            parentMessageNode: withUserMessage,
          };
        })
      );

      return { newAssistantMessage, newUserMessage };
    },
    [setChatSessions, attachments]
  );

  const postProcessSuccess = async (session: ChatSession) => {
    updateSessionState(session.id, () => ({
      conversationState: 'healthy',
    }));
  };

  // Handle sending a message
  const handleSendMessage = useCallback(
    async (inputMessage: string) => {
      const trimmedMessage = inputMessage.trim();
      if (
        (!trimmedMessage && (!attachments || attachments.length === 0)) ||
        chatSessions.length === 0
      )
        return;

      // Check basic message sending permission (free tier daily limits)
      if (!canSendMessage) {
        toast.error(
          'You have reached your daily message limit. Please upgrade your plan or try again tomorrow.'
        );
        return;
      }

      // Check token quota for paid tiers
      const currentPlan = getPlan();
      if (currentPlan !== 'free' && quotaStatus?.tokens) {
        const { remaining, total } = quotaStatus.tokens;
        const usagePercent =
          total > 0 ? ((total - remaining) / total) * 100 : 0;

        if (remaining <= 0) {
          if (currentPlan === 'starter') {
            toast.error(
              'Token quota exceeded. Please use GPT-3.5 or upgrade to Premium for more tokens.'
            );
            return;
          } else if (currentPlan === 'premium') {
            toast.error(
              'Token quota exceeded. You can purchase additional tokens in billing settings.'
            );
            return;
          }
        } else if (usagePercent >= 90) {
          toast.warning(
            `Token quota warning: ${remaining.toLocaleString()} tokens remaining (${Math.round(
              100 - usagePercent
            )}%)`
          );
        }
      }

      // Check comparison mode restrictions
      const isComparisonMessage = chatSessions.length > 1;
      if (isComparisonMessage && !canUseComparison) {
        if (currentPlan !== 'premium') {
          toast.error(
            'Side-by-side comparison requires Premium plan. Please upgrade to continue.'
          );
          return;
        } else {
          toast.error(
            'Daily comparison limit reached (50/day). Try again tomorrow.'
          );
          return;
        }
      }

      setInputMessage('');

      const lastMessages: MessageNode[] = chatSessions.map(
        (session) =>
          getFlattenedMessages(session.parentMessageNode).at(-1) as MessageNode
      );

      // Add all sessions to processing
      chatSessions.forEach((session) => addProcessing(session.id));

      // Determine if this is a new conversation by checking if any session has messages
      const isNewConversation = chatSessions.every((session) => {
        const messages = getFlattenedMessages(session.parentMessageNode);
        return messages.length === 0;
      });

      // Generate a new group ID for new conversations, otherwise use existing one
      const currentGroupId = isNewConversation
        ? uuidv4()
        : (localGroupConversationId ?? uuidv4());

      const sessionPromises = chatSessions.map((session, index) =>
        handleAddSessionResponse(
          session,
          index,
          trimmedMessage,
          lastMessages[index]?.id ?? null,
          currentGroupId,
          useWebSearch,
          useImageGeneration
        )
      );

      await Promise.all(sessionPromises);
    },
    [
      localGroupConversationId,
      chatSessions,
      handleAddSessionResponse,
      getFlattenedMessages,
      attachments,
      useWebSearch,
      useImageGeneration,
      setInputMessage,
      canSendMessage,
      quotaStatus,
      canUseComparison,
      getPlan,
      addProcessing,
    ]
  );

  const handleRetryMessage = useCallback(
    async (sessionId: string, modelToRetry?: Model) => {
      // Avoid unnecessary re-render by checking if already processing
      const activeSession =
        chatSessions.find((session) => session.id === sessionId) ??
        chatSessions[0];
      if (activeSession) {
        addProcessing(activeSession.id);
      }

      const flattenedMessages = getFlattenedMessages(
        activeSession?.parentMessageNode
      );
      const lastUserMessage = flattenedMessages
        .slice()
        .reverse()
        .find((message) => message.role === 'user');
      if (!lastUserMessage) return;

      if (modelToRetry && modelToRetry.id !== activeSession.model.id) {
        updateSessionState(activeSession.id, () => ({
          model: modelToRetry,
        }));
      }

      // Process only the selected session instead of all sessions
      await handleUpdateSessionResponse(
        activeSession,
        lastUserMessage.id,
        undefined,
        useWebSearch,
        useImageGeneration,
        modelToRetry
      );
    },
    [
      chatSessions,
      handleUpdateSessionResponse,
      getFlattenedMessages,
      providers,
      updateSessionState,
      useWebSearch,
      useImageGeneration,
      addProcessing,
    ]
  );

  const editMessage = useCallback(
    async (sessionId: string, messageId: string, newContent: string) => {
      // Assuming edits happen on the primary model/branch for now
      const targetSession = chatSessions.find(
        (session) => session.id === sessionId
      );
      if (!targetSession) return;

      log.info({ messageId, sessionId: targetSession.id }, 'Editing message');
      addProcessing(targetSession.id);

      const flattenedMessages = getFlattenedMessages(
        targetSession?.parentMessageNode
      );

      const selectedMessageIndex = flattenedMessages.findIndex(
        (message) => message.id === messageId
      );

      if (selectedMessageIndex === -1) return;

      const selectedMessageParentId =
        findParentId(targetSession.parentMessageNode, messageId) ??
        targetSession.parentMessageNode?.id ??
        DUMMY_ROOT_MESSAGE_ID;

      const newAssistantMessageId = uuidv4();
      const placeholderAssistantMessage: MessageNode = {
        id: newAssistantMessageId,
        content: '',
        role: 'assistant',
        created_at: new Date().toISOString(),
        children: [],
        ...messageNodeOtherFields,
      };

      const newUserMessage: MessageNode = {
        id: uuidv4(),
        content: newContent,
        role: 'user',
        created_at: new Date().toISOString(),
        children: [placeholderAssistantMessage],
        ...messageNodeOtherFields,
      };

      // 1. Add the new user message as a sibling (i.e., child of the original parent)
      updateSessionState(targetSession.id, (prevSession) => ({
        parentMessageNode: addMessageToTree(
          prevSession.parentMessageNode,
          selectedMessageParentId,
          newUserMessage
        ),
      }));

      try {
        // Metrics setup
        startTTFTTracking(targetSession.id);
        startStreamStabilityTracking(targetSession.id);

        const stableWorkspaceId = workspaceId;

        if (canUseUnifiedStream) {
          // ----------------------------
          // Unified streaming path
          // ----------------------------
          const {
            onDelta,
            onAnnotation,
            onError,
            onDone,
            onMetadata,
            setAssistantId,
          } = makeStreamCallbacks(
            targetSession.id,
            newAssistantMessageId, // placeholder
            targetSession.model.display_name,
            targetSession.model.provider?.name
          );

          const abortStreamFn = createUnifiedStream(
            {
              messageId: messageId,
              model: targetSession.model.id,
              conversationId: targetSession.conversationId,
              isTestMode,
              newContent,
              isEdit: true,
              useWebSearch,
              useImageGeneration,
              workspaceId: stableWorkspaceId,
            },
            {
              onDelta,
              onAnnotation,
              onError: (err) => {
                onError(err);
                abortStream(targetSession.id);
              },
              onDone: () => {
                onDone();
              },
              onMetadata,
              onInit: (initData) => {
                if (initData.assistantMessageId) {
                  setAssistantId(initData.assistantMessageId);
                }

                // Persist groupConversationId when server returns it
                if (initData.groupConversationId) {
                  updateSessionState(targetSession.id, () => ({
                    groupConversationId: initData.groupConversationId,
                  }));
                }

                // Sync IDs with server-assigned ones
                if (initData.assistantMessageId !== newAssistantMessageId) {
                  updateSessionState(targetSession.id, (prevSession) => ({
                    parentMessageNode: updateMessageInTree(
                      prevSession.parentMessageNode,
                      newAssistantMessageId,
                      { id: initData.assistantMessageId }
                    ),
                  }));
                }

                // Sync the user message ID as well (important for re-editing)
                if (
                  initData.userMessageId &&
                  initData.userMessageId !== newUserMessage.id
                ) {
                  updateSessionState(targetSession.id, (prevSession) => ({
                    parentMessageNode: updateMessageInTree(
                      prevSession.parentMessageNode,
                      newUserMessage.id,
                      { id: initData.userMessageId }
                    ),
                  }));
                }
              },
            },
            canUseUnifiedStream
          );

          registerStream(targetSession.id, {
            abort: abortStreamFn,
            // Attach the exact groupConversationId if available – avoid falling back to conversationId.
            groupConversationId: targetSession.groupConversationId || '',
          });
        } else {
          // ----------------------------
          // Legacy SSE fallback path
          // ----------------------------
          const updateResEdit = await updateSse(
            {
              messageId: messageId,
              model: targetSession.model.id,
              isTestMode,
              newContent,
              isEdit: true,
              useWebSearch,
              useImageGeneration,
            },
            canUseUnifiedStream
          );

          const {
            assistantMessageId: serverAssistantId,
            conversationId: newConversationId,
          } = updateResEdit;

          const serverUserMessageId = (
            updateResEdit as { userMessageId?: string }
          ).userMessageId;

          // Sync IDs if they differ
          if (serverAssistantId !== newAssistantMessageId) {
            updateSessionState(targetSession.id, (prevSession) => ({
              parentMessageNode: updateMessageInTree(
                prevSession.parentMessageNode,
                newAssistantMessageId,
                { id: serverAssistantId }
              ),
            }));
          }

          // Sync user message ID if server returned one
          if (
            serverUserMessageId &&
            serverUserMessageId !== newUserMessage.id
          ) {
            updateSessionState(targetSession.id, (prevSession) => ({
              parentMessageNode: updateMessageInTree(
                prevSession.parentMessageNode,
                newUserMessage.id,
                { id: serverUserMessageId }
              ),
            }));
          }

          const { onDelta, onAnnotation, onError, onDone, onMetadata } =
            makeStreamCallbacks(
              targetSession.id,
              serverAssistantId,
              targetSession.model.display_name,
              targetSession.model.provider?.name
            );

          subscribeToStream(
            serverAssistantId,
            newConversationId ?? targetSession.conversationId!,
            targetSession.model.id,
            isTestMode,
            useWebSearch,
            useImageGeneration,
            onDelta,
            onAnnotation,
            onError,
            onDone,
            onMetadata,
            stableWorkspaceId
          );
        }
      } catch (error) {
        log.error({ err: error }, 'Error editing message');
        toast.error('Sorry, there was an error editing your message.');
        // TODO: Revert local changes or show error state
        updateSessionState(targetSession.id, () => ({
          conversationState: 'error',
        }));
      } finally {
        clearAttachments();
        // Mark this session as no longer processing
        removeProcessing(targetSession.id);
      }
    },
    [
      chatSessions,
      setChatSessions,
      updateSessionState,
      postProcessSuccess,
      isTestMode,
      useWebSearch,
      useImageGeneration,
      getFlattenedMessages,
      subscribeToStream,
      makeStreamCallbacks,
      addProcessing,
      removeProcessing,
      clearAttachments,
      startTTFTTracking,
      startStreamStabilityTracking,
      updateSse,
      workspaceId,
      registerStream,
      abortStream,
    ]
  );

  // Cleanup RAF and batched updates on unmount
  useEffect(() => {
    return () => {
      if (rafId.current !== null) {
        cancelAnimationFrame(rafId.current);
      }
      batchedUpdates.current = {};
    };
  }, []);

  return {
    handleSendMessage,
    processingModels: processingModelsArray,
    handleRetryMessage,
    editMessage,
    handleUpload,
    isUploading,
    handleRemoveFile,
  };
};

// Helper function to find a node in the tree (implement or import)
function findNodeById(
  node: MessageNode | null,
  id: string
): MessageNode | null {
  if (!node) return null;
  if (node.id === id) return node;
  if (node.children) {
    for (const child of node.children) {
      const found = findNodeById(child, id);
      if (found) return found;
    }
  }
  return null;
}

// Helper function to find the parent ID of a given message within the tree.
// Returns `null` if the target is the root or cannot be found.
function findParentId(
  node: MessageNode | null,
  targetId: string,
  parentId: string | null = null
): string | null {
  if (!node) return null;
  if (node.id === targetId) {
    return parentId;
  }
  if (node.children) {
    for (const child of node.children) {
      const found = findParentId(child, targetId, node.id);
      if (found) return found;
    }
  }
  return null;
}
