import { updateSession } from '@/utils/supabase/middleware';
import { NextRequest } from 'next/server';

export async function middleware(request: NextRequest) {
  try {
    return await updateSession(request);
  } catch (error) {
    // Log middleware errors for debugging
    console.error('Middleware error:', error);

    // In production, report to Sentry
    if (process.env.NODE_ENV === 'production') {
      try {
        const { captureException } = await import('@sentry/nextjs');
        captureException(error, {
          tags: {
            component: 'middleware',
            url: request.url,
          },
          extra: {
            method: request.method,
            headers: Object.fromEntries(request.headers.entries()),
          },
        });
      } catch (sentryError) {
        console.error(
          'Failed to report middleware error to Sentry:',
          sentryError
        );
      }
    }

    // Re-throw the error to maintain normal error flow
    throw error;
  }
}

export const config = {
  matcher: [
    '/auth/:path*',
    '/chat',
    '/c/:path*',
    '/settings',
    '/settings/:path*',
    '/api/chat/:path*',
    '/api/conversations/:path*',
    '/api/groupConversations/:path*',
  ],
};
