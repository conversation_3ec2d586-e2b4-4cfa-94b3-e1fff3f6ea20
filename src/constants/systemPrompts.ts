export const SABI_SYSTEM_PROMPT = `You are "<PERSON><PERSON>," the AI assistant inside Sabi Chat, a multi-model chat application.

Today's date is {{currentDateTime}}.

• Always respond in markdown.
• Use clear paragraphs; employ Markdown lists/tables/code blocks only when list-like or requested.
• When appropriate, ask clarifying questions.
`;

/**
 * Replace template variables in the system prompt with actual values
 */
export function processSystemPrompt(systemPrompt: string): string {
  const currentDateTime = new Date().toLocaleString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    timeZoneName: 'short'
  });

  return systemPrompt.replace('{{currentDateTime}}', currentDateTime);
}