import { renderHook, waitFor } from '@testing-library/react';
import { useFeatureFlag, useFeatureFlags, invalidateFeatureFlagsCache } from '../useFeatureFlag';
import { UserContext } from '@/providers/AuthProvider';
import React from 'react';
import { Json } from '@/types/database.types';

// Mock fetch
global.fetch = jest.fn();

const mockFetch = fetch as jest.MockedFunction<typeof fetch>;

// Mock user context
const mockUserContext = {
  me: {
    preferences: {
      id: 1,
      user_id: 'user-123',
      default_model_id: null,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
      explicitly_hidden_model_ids: [] as <PERSON><PERSON>,
      explicitly_shown_model_ids: [] as <PERSON><PERSON>,
      hidden_model_ids: null as Json | null,
    },
  },
  user: null,
  isAuthenticated: true,
  signOut: jest.fn(),
  fetchMe: jest.fn(),
};

const wrapper = ({ children }: { children: React.ReactNode }) => (
  <UserContext.Provider value={mockUserContext}>
    {children}
  </UserContext.Provider>
);

describe('useFeatureFlag', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockFetch.mockClear();
    invalidateFeatureFlagsCache();
  });

  it('should return false and not loading when user is not authenticated', async () => {
    const unauthenticatedWrapper = ({
      children,
    }: {
      children: React.ReactNode;
    }) => (
      <UserContext.Provider value={{
        me: null,
        user: null,
        isAuthenticated: false,
        signOut: jest.fn(),
        fetchMe: jest.fn(),
      }}>
        {children}
      </UserContext.Provider>
    );

    const { result } = renderHook(() => useFeatureFlag('test-flag'), {
      wrapper: unauthenticatedWrapper,
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.isEnabled).toBe(false);
    expect(result.current.error).toBe(null);
    expect(mockFetch).not.toHaveBeenCalled();
  });

  it('should fetch and return flag status for authenticated user', async () => {
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        isEnabled: true,
        error: null,
      }),
    } as Response);

    const { result } = renderHook(() => useFeatureFlag('test-flag'), {
      wrapper,
    });

    expect(result.current.isLoading).toBe(true);

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.isEnabled).toBe(true);
    expect(result.current.error).toBe(null);
    expect(mockFetch).toHaveBeenCalledWith('/api/feature-flags/test-flag', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });

  it('should handle API errors gracefully', async () => {
    mockFetch.mockResolvedValueOnce({
      ok: false,
      status: 500,
    } as Response);

    const { result } = renderHook(() => useFeatureFlag('test-flag'), {
      wrapper,
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.isEnabled).toBe(false);
    expect(result.current.error).toBeInstanceOf(Error);
  });

  it('should handle network errors gracefully', async () => {
    mockFetch.mockRejectedValueOnce(new Error('Network error'));

    const { result } = renderHook(() => useFeatureFlag('test-flag'), {
      wrapper,
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.isEnabled).toBe(false);
    expect(result.current.error).toBeInstanceOf(Error);
  });
});

describe('useFeatureFlags', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockFetch.mockClear();
    invalidateFeatureFlagsCache();
  });

  it('should return empty flags when user is not authenticated', async () => {
    const unauthenticatedWrapper = ({
      children,
    }: {
      children: React.ReactNode;
    }) => (
      <UserContext.Provider value={{
        me: null,
        user: null,
        isAuthenticated: false,
        signOut: jest.fn(),
        fetchMe: jest.fn(),
      }}>
        {children}
      </UserContext.Provider>
    );

    const { result } = renderHook(() => useFeatureFlags(), {
      wrapper: unauthenticatedWrapper,
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.flags).toEqual({});
    expect(result.current.error).toBe(null);
    expect(mockFetch).not.toHaveBeenCalled();
  });

  it('should fetch and return all flags for authenticated user', async () => {
    const mockFlags = {
      'flag-1': true,
      'flag-2': false,
      'flag-3': true,
    };

    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        flags: mockFlags,
        error: null,
      }),
    } as Response);

    const { result } = renderHook(() => useFeatureFlags(), {
      wrapper,
    });

    expect(result.current.isLoading).toBe(true);

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.flags).toEqual(mockFlags);
    expect(result.current.error).toBe(null);
    expect(mockFetch).toHaveBeenCalledWith('/api/feature-flags', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });

  it('should filter flags when specific flag names are provided', async () => {
    const mockFlags = {
      'flag-1': true,
      'flag-2': false,
      'flag-3': true,
    };

    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        flags: mockFlags,
        error: null,
      }),
    } as Response);

    const { result } = renderHook(() => useFeatureFlags(['flag-1', 'flag-3']), {
      wrapper,
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.flags).toEqual({
      'flag-1': true,
      'flag-3': true,
    });
  });

  it('should handle missing flags in filter', async () => {
    const mockFlags = {
      'flag-1': true,
    };

    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        flags: mockFlags,
        error: null,
      }),
    } as Response);

    const { result } = renderHook(
      () => useFeatureFlags(['flag-1', 'non-existent-flag']),
      {
        wrapper,
      }
    );

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.flags).toEqual({
      'flag-1': true,
      'non-existent-flag': false, // Should default to false
    });
  });

  it('should handle API errors gracefully', async () => {
    mockFetch.mockResolvedValueOnce({
      ok: false,
      status: 500,
    } as Response);

    const { result } = renderHook(() => useFeatureFlags(), {
      wrapper,
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.flags).toEqual({});
    expect(result.current.error).toBeInstanceOf(Error);
  });
});
