import React from 'react';
import { renderHook, waitFor } from '@testing-library/react';
import {
  useFeatureFlag,
  useFeatureFlags,
} from '@/hooks/useFeatureFlag';
import {
  FeatureFlagsProvider,
  __TEST_ONLY_resetCache,
} from '@/providers/FeatureFlagProvider';
import { UserContext } from '@/providers/AuthProvider';
import { Json } from '@/types/database.types';

// Mock fetch
global.fetch = jest.fn();
const mockFetch = fetch as jest.MockedFunction<typeof fetch>;

// Mock user context
const mockUserContext = {
  me: {
    preferences: {
      id: 1,
      user_id: 'user-123',
      default_model_id: null,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
      explicitly_hidden_model_ids: [] as Json,
      explicitly_shown_model_ids: [] as Json,
      hidden_model_ids: null as J<PERSON> | null,
    },
  },
  user: null,
  isAuthenticated: true,
  signOut: jest.fn(),
  fetchMe: jest.fn(),
};

const unauthenticatedUserContext = {
  me: null,
  user: null,
  isAuthenticated: false,
  signOut: jest.fn(),
  fetchMe: jest.fn(),
};

// Wrapper component that includes all necessary providers
const AllTheProviders = ({
  children,
  userContext = mockUserContext,
}: {
  children: React.ReactNode;
  userContext?: typeof mockUserContext | typeof unauthenticatedUserContext;
}) => (
  <UserContext.Provider value={userContext}>
    <FeatureFlagsProvider>{children}</FeatureFlagsProvider>
  </UserContext.Provider>
);

describe('Feature Flag Hooks with Provider', () => {
  beforeEach(() => {
    mockFetch.mockClear();
    __TEST_ONLY_resetCache();
  });

  it('should fetch flags and update the context', async () => {
    const mockFlags = { 'unified-streaming': true, 'new-ui': false };
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ flags: mockFlags }),
    } as Response);

    const { result } = renderHook(() => useFeatureFlags(), {
      wrapper: AllTheProviders,
    });

    expect(result.current.isLoading).toBe(true);

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.flags).toEqual(mockFlags);
  });

  it('useFeatureFlag should return the correct value for a specific flag', async () => {
    const mockFlags = { 'unified-streaming': true, 'new-ui': false };
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ flags: mockFlags }),
    } as Response);

    const { result } = renderHook(() => useFeatureFlag('unified-streaming'), {
      wrapper: AllTheProviders,
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.isEnabled).toBe(true);
  });

  it('should handle unauthenticated users', async () => {
    const { result } = renderHook(() => useFeatureFlags(), {
      wrapper: (props) => (
        <AllTheProviders {...props} userContext={unauthenticatedUserContext} />
      ),
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.flags).toEqual({});
    expect(mockFetch).not.toHaveBeenCalled();
  });
});
