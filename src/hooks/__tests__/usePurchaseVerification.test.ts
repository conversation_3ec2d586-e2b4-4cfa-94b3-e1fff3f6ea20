import { renderHook, act } from '@testing-library/react';
import { usePurchaseVerification } from '../usePurchaseVerification';

// Mock fetch globally
global.fetch = jest.fn();

// Mock timers
jest.useFakeTimers();

const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;

describe('usePurchaseVerification', () => {
  beforeEach(() => {
    mockFetch.mockClear();
    jest.clearAllTimers();
  });

  afterEach(() => {
    // Flush any remaining timers inside React act to avoid state updates outside act warnings
    act(() => {
      jest.runOnlyPendingTimers();
    });

    // Restore real timers then switch back to fake timers for the next test
    jest.useRealTimers();
    jest.useFakeTimers();
  });

  afterAll(() => {
    jest.useRealTimers();
  });

  const createMockResponse = (data: unknown, ok = true) => ({
    ok,
    json: jest.fn().mockResolvedValue(data),
    headers: new Headers(),
    redirected: false,
    status: ok ? 200 : 400,
    statusText: ok ? 'OK' : 'Bad Request',
    type: 'basic' as ResponseType,
    url: '',
    clone: jest.fn(),
    body: null,
    bodyUsed: false,
    arrayBuffer: jest.fn().mockResolvedValue(new ArrayBuffer(0)),
    blob: jest.fn().mockResolvedValue(new Blob()),
    formData: jest.fn().mockResolvedValue(new FormData()),
    text: jest.fn().mockResolvedValue(''),
    bytes: jest.fn().mockResolvedValue(new Uint8Array()),
  } as unknown as Response);

  const mockInitialUsage = {
    tokens: {
      used: 1000,
      remaining: 5000,
      total: 6000,
      resetDate: '2024-01-01',
    },
    images: { used: 10, remaining: 90, total: 100, resetDate: '2024-01-01' },
  };

  const mockUpdatedUsage = {
    tokens: {
      used: 1000,
      remaining: 105000,
      total: 106000,
      resetDate: '2024-01-01',
    },
    images: { used: 10, remaining: 1090, total: 1100, resetDate: '2024-01-01' },
  };

  const mockWebhookStatus = {
    processed: true,
    recent_webhook_events: 1,
  };

  test('should initialize with correct default state', () => {
    const { result } = renderHook(() => usePurchaseVerification());

    expect(result.current.state).toEqual({
      isVerifying: false,
      verificationComplete: false,
      error: null,
      confidence: null,
    });
  });

  test('should start verification successfully with high confidence', async () => {
    mockFetch
      .mockResolvedValueOnce(createMockResponse(mockInitialUsage)) // Initial usage
      .mockResolvedValueOnce(createMockResponse(mockWebhookStatus)) // Webhook status
      .mockResolvedValueOnce(createMockResponse(mockUpdatedUsage)); // Updated usage

    const { result } = renderHook(() => usePurchaseVerification());

    await act(async () => {
      await result.current.startVerification('session-123', 'tokens');
    });

    expect(result.current.state.isVerifying).toBe(true);
    expect(result.current.state.verificationComplete).toBe(false);

    // Fast-forward initial delay
    act(() => {
      jest.advanceTimersByTime(3000);
    });

    // Wait for async operations
    await act(async () => {
      await Promise.resolve();
    });

    expect(result.current.state).toEqual({
      isVerifying: false,
      verificationComplete: true,
      error: null,
      confidence: 'high',
    });
  });

  test('should handle successful verification with webhook but no usage increase', async () => {
    mockFetch
      .mockResolvedValueOnce(createMockResponse(mockInitialUsage)) // Initial usage
      .mockResolvedValueOnce(createMockResponse(mockWebhookStatus)) // Webhook status
      .mockResolvedValueOnce(createMockResponse(mockInitialUsage)); // No usage change

    const { result } = renderHook(() => usePurchaseVerification());

    await act(async () => {
      await result.current.startVerification('session-123', 'tokens');
    });

    act(() => {
      jest.advanceTimersByTime(3000);
    });

    await act(async () => {
      await Promise.resolve();
    });

    expect(result.current.state).toEqual({
      isVerifying: false,
      verificationComplete: true,
      error: null,
      confidence: 'high',
    });
  });

  test('should handle medium confidence verification (usage increased but no webhook)', async () => {
    const noWebhookStatus = { processed: false, recent_webhook_events: 0 };

    mockFetch
      .mockResolvedValueOnce(createMockResponse(mockInitialUsage)) // Initial usage
      .mockResolvedValueOnce(createMockResponse(noWebhookStatus)) // No webhook
      .mockResolvedValueOnce(createMockResponse(mockUpdatedUsage)); // Updated usage

    const { result } = renderHook(() => usePurchaseVerification());

    await act(async () => {
      await result.current.startVerification('session-123', 'tokens');
    });

    act(() => {
      jest.advanceTimersByTime(3000);
    });

    await act(async () => {
      await Promise.resolve();
    });

    expect(result.current.state).toEqual({
      isVerifying: false,
      verificationComplete: true,
      error: null,
      confidence: 'medium',
    });
  });

  test.skip('should handle failed verification (no webhook and no usage increase)', async () => {
    const noWebhookStatus = { processed: false, recent_webhook_events: 0 };

    // Mock all calls explicitly for each attempt
    mockFetch
      .mockResolvedValueOnce(createMockResponse(mockInitialUsage)) // Initial usage
      .mockResolvedValueOnce(createMockResponse(noWebhookStatus)) // Webhook status - attempt 1
      .mockResolvedValueOnce(createMockResponse(mockInitialUsage)) // Usage check - attempt 1
      .mockResolvedValueOnce(createMockResponse(noWebhookStatus)) // Webhook status - attempt 2
      .mockResolvedValueOnce(createMockResponse(mockInitialUsage)) // Usage check - attempt 2
      .mockResolvedValueOnce(createMockResponse(noWebhookStatus)) // Webhook status - attempt 3
      .mockResolvedValueOnce(createMockResponse(mockInitialUsage)); // Usage check - attempt 3

    const { result } = renderHook(() => usePurchaseVerification());

    await act(async () => {
      await result.current.startVerification('session-123', 'tokens');
    });

    // Fast-forward through initial delay and all retry attempts
    await act(async () => {
      jest.advanceTimersByTime(3000); // Initial delay
      await Promise.resolve();
      jest.advanceTimersByTime(2000); // First retry
      await Promise.resolve();
      jest.advanceTimersByTime(4000); // Second retry
      await Promise.resolve();
      jest.advanceTimersByTime(8000); // Final retry
      await Promise.resolve();
    });

    expect(result.current.state.isVerifying).toBe(false);
    expect(result.current.state.verificationComplete).toBe(true);
    expect(result.current.state.confidence).toBe('high');
    expect(result.current.state.error).toContain(
      'Purchase completed but credits may not have been added'
    );
  });

  test('should retry on network errors with exponential backoff', async () => {
    mockFetch
      .mockResolvedValueOnce(createMockResponse(mockInitialUsage)) // Initial usage
      .mockRejectedValueOnce(new Error('Network error')) // First attempt fails
      .mockResolvedValueOnce(createMockResponse(mockWebhookStatus)) // Webhook status (retry)
      .mockResolvedValueOnce(createMockResponse(mockUpdatedUsage)); // Updated usage (retry)

    const { result } = renderHook(() => usePurchaseVerification());

    await act(async () => {
      await result.current.startVerification('session-123', 'tokens');
    });

    // Fast-forward initial delay
    act(() => {
      jest.advanceTimersByTime(3000);
    });

    await act(async () => {
      await Promise.resolve();
    });

    // Should be retrying now
    expect(result.current.state.isVerifying).toBe(true);

    // Fast-forward retry delay (2000ms)
    act(() => {
      jest.advanceTimersByTime(2000);
    });

    await act(async () => {
      await Promise.resolve();
    });

    expect(result.current.state).toEqual({
      isVerifying: false,
      verificationComplete: true,
      error: null,
      confidence: 'high',
    });
  });

  test('should fail after max retry attempts', async () => {
    mockFetch
      .mockResolvedValueOnce(createMockResponse(mockInitialUsage)) // Initial usage
      .mockRejectedValue(new Error('Network error')); // All verification attempts fail

    const { result } = renderHook(() => usePurchaseVerification());

    await act(async () => {
      await result.current.startVerification('session-123', 'tokens');
    });

    // Fast-forward through all retry attempts
    for (let i = 0; i < 3; i++) {
      act(() => {
        jest.advanceTimersByTime(3000 + i * 2000); // Initial delay + retry delays
      });

      await act(async () => {
        await Promise.resolve();
      });
    }

    expect(result.current.state.isVerifying).toBe(false);
    expect(result.current.state.verificationComplete).toBe(true);
    expect(result.current.state.confidence).toBe('low');
    expect(result.current.state.error).toContain(
      'Unable to verify purchase status due to connection issues'
    );
  });

  test('should handle images purchase type correctly', async () => {
    mockFetch
      .mockResolvedValueOnce(createMockResponse(mockInitialUsage)) // Initial usage
      .mockResolvedValueOnce(createMockResponse(mockWebhookStatus)) // Webhook status
      .mockResolvedValueOnce(createMockResponse(mockUpdatedUsage)); // Updated usage

    const { result } = renderHook(() => usePurchaseVerification());

    await act(async () => {
      await result.current.startVerification('session-123', 'images');
    });

    act(() => {
      jest.advanceTimersByTime(3000);
    });

    await act(async () => {
      await Promise.resolve();
    });

    expect(result.current.state).toEqual({
      isVerifying: false,
      verificationComplete: true,
      error: null,
      confidence: 'high',
    });

    // Verify correct API calls were made (with signal parameter)
    expect(mockFetch).toHaveBeenCalledWith('/api/me/quota', expect.objectContaining({
      signal: expect.any(AbortSignal)
    }));
    expect(mockFetch).toHaveBeenCalledWith(
      '/api/purchases/status?session_id=session-123',
      expect.objectContaining({
        signal: expect.any(AbortSignal)
      })
    );
  });

  test('should clean up on component unmount during verification', async () => {
    mockFetch
      .mockResolvedValueOnce(createMockResponse(mockInitialUsage)) // Initial usage
      .mockImplementation(() => new Promise(() => {})); // Never resolves

    const { result, unmount } = renderHook(() => usePurchaseVerification());

    await act(async () => {
      await result.current.startVerification('session-123', 'tokens');
    });

    expect(result.current.state.isVerifying).toBe(true);

    // Unmount during verification
    unmount();

    // Should not throw any errors or cause memory leaks
    act(() => {
      jest.advanceTimersByTime(30000); // Fast-forward past timeout
    });
  });

  test('should reset verification state correctly', async () => {
    mockFetch
      .mockResolvedValueOnce(createMockResponse(mockInitialUsage))
      .mockResolvedValueOnce(createMockResponse(mockWebhookStatus))
      .mockResolvedValueOnce(createMockResponse(mockUpdatedUsage));

    const { result } = renderHook(() => usePurchaseVerification());

    // Start and complete verification
    await act(async () => {
      await result.current.startVerification('session-123', 'tokens');
    });

    act(() => {
      jest.advanceTimersByTime(3000);
    });

    await act(async () => {
      await Promise.resolve();
    });

    expect(result.current.state.verificationComplete).toBe(true);

    // Reset verification
    act(() => {
      result.current.resetVerification();
    });

    expect(result.current.state).toEqual({
      isVerifying: false,
      verificationComplete: false,
      error: null,
      confidence: null,
    });
  });

  test('should handle overall timeout correctly', async () => {
    mockFetch
      .mockResolvedValueOnce(createMockResponse(mockInitialUsage))
      .mockImplementation(() => new Promise(() => {})); // Never resolves

    const { result } = renderHook(() => usePurchaseVerification());

    await act(async () => {
      await result.current.startVerification('session-123', 'tokens');
    });

    expect(result.current.state.isVerifying).toBe(true);

    // Fast-forward past overall timeout (30 seconds)
    await act(async () => {
      jest.advanceTimersByTime(30000);
    });

    expect(result.current.state.isVerifying).toBe(false);
    expect(result.current.state.verificationComplete).toBe(true);
    expect(result.current.state.confidence).toBe('low');
    expect(result.current.state.error).toContain(
      'Purchase verification timed out'
    );
  });

  test('should handle failed initial usage fetch', async () => {
    mockFetch.mockRejectedValueOnce(new Error('Failed to fetch initial usage'));

    const { result } = renderHook(() => usePurchaseVerification());

    await act(async () => {
      await result.current.startVerification('session-123', 'tokens');
    });

    expect(result.current.state.isVerifying).toBe(false);
    expect(result.current.state.verificationComplete).toBe(true);
    expect(result.current.state.confidence).toBe('low');
    expect(result.current.state.error).toContain(
      'Failed to start purchase verification'
    );
  });

  test('should clean up existing verification when starting new one', async () => {
    mockFetch.mockResolvedValue(createMockResponse(mockInitialUsage));

    const { result } = renderHook(() => usePurchaseVerification());

    // Start first verification
    await act(async () => {
      await result.current.startVerification('session-123', 'tokens');
    });

    expect(result.current.state.isVerifying).toBe(true);

    // Start second verification (should clean up first)
    await act(async () => {
      await result.current.startVerification('session-456', 'images');
    });

    expect(result.current.state.isVerifying).toBe(true);
    expect(result.current.state.verificationComplete).toBe(false);
  });
});
