'use client';

import { useEffect } from 'react';
import { usePathname, useSearchParams } from 'next/navigation';
import { useNavigationHistory } from '@/providers/NavigationHistoryProvider';

/**
 * Custom hook to automatically track navigation changes
 * This can be used in components that need to ensure navigation history is tracked
 */
export function useNavigationTracking() {
  const { setPreviousPath } = useNavigationHistory();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  useEffect(() => {
    const currentPath = pathname + (searchParams.toString() ? `?${searchParams.toString()}` : '');
    
    // Only track non-settings routes to avoid tracking internal settings navigation
    if (!pathname.startsWith('/settings')) {
      setPreviousPath(currentPath);
    }
  }, [pathname, searchParams, setPreviousPath]);

  return { pathname, searchParams };
}

/**
 * Hook specifically for settings navigation
 * Provides utilities for settings page navigation
 */
export function useSettingsNavigation() {
  const { getPreviousPathForSettings } = useNavigationHistory();
  
  const getBackPath = () => {
    return getPreviousPathForSettings();
  };

  return { getBackPath };
}
