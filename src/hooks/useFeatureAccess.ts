import { useSubscription } from '@/providers/SubscriptionProvider';
import { SUBSCRIPTION_PLANS } from '@/lib/supabase/types';

export interface FeatureAccess {
  canCreateWorkspace: boolean;
  canUseComparison: boolean;
  canUsePromptLibrary: boolean;
  canUseWebSearch: boolean;
  maxWorkspaces: number;
  currentWorkspaceCount: number;
  workspaceLimit: {
    reached: boolean;
    remaining: number;
  };
}

export function useFeatureAccess(): FeatureAccess {
  const {
    subscription,
    workspaceCount,
    canCreateWorkspace,
    canUseComparison,
    canUsePromptLibrary
  } = useSubscription();

  // Get current plan details
  const currentPlan = subscription?.plan || 'free';
  const isActive = subscription
    ? ['active', 'trialing'].includes(subscription.status)
    : false;

  // If subscription exists but isn't active, treat as free
  const effectivePlan = subscription && !isActive ? 'free' : currentPlan;
  const planDetails = SUBSCRIPTION_PLANS[effectivePlan];

  // Sentinel value representing an "unlimited" workspace allowance.
  const UNLIMITED_WORKSPACES = 999999999;
  const maxWorkspaces = planDetails.maxWorkspaces;
  const workspaceLimit = {
    reached: maxWorkspaces !== UNLIMITED_WORKSPACES && workspaceCount >= maxWorkspaces,
    // For UI purposes, expose Infinity instead of the sentinel value
    remaining:
      maxWorkspaces === UNLIMITED_WORKSPACES
        ? Infinity
        : Math.max(0, maxWorkspaces - workspaceCount),
  };

  return {
    canCreateWorkspace,
    canUseComparison,
    canUsePromptLibrary,
    canUseWebSearch: planDetails.allowsWebSearch,
    maxWorkspaces,
    currentWorkspaceCount: workspaceCount,
    workspaceLimit,
  };
}
