import { useState, useEffect, useCallback, useContext, useMemo } from 'react';
import { UserContext } from '@/providers/AuthProvider';
import {
  UseFeatureFlagResult,
  UseFeatureFlagsResult, FEATURE_FLAG_CACHE_TTL
} from '@/lib/feature-flags/types';

// Simple in-memory cache for feature flags
interface FeatureFlagCache {
  flags: Record<string, boolean>;
  timestamp: number;
  userId: string;
}

let cache: FeatureFlagCache | null = null;

/**
 * Check if cache is valid for the current user
 */
function isCacheValid(userId: string): boolean {
  if (!cache || cache.userId !== userId) {
    return false;
  }

  const now = Date.now();
  return now - cache.timestamp < FEATURE_FLAG_CACHE_TTL;
}

/**
 * Fetch feature flags from the API
 */
async function fetchFeatureFlags(
  userId: string
): Promise<Record<string, boolean>> {
  try {
    const response = await fetch('/api/feature-flags', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (data.error) {
      throw new Error(data.error);
    }

    // Update cache
    cache = {
      flags: data.flags || {},
      timestamp: Date.now(),
      userId,
    };

    return data.flags || {};
  } catch (error) {
    console.error('Error fetching feature flags:', error);
    throw error; // Re-throw the error so the hook can handle it
  }
}

/**
 * Hook to check if a specific feature flag is enabled
 * @param flagName - The name of the feature flag to check
 * @returns Object with isEnabled, isLoading, and error
 */
export function useFeatureFlag(flagName: string): UseFeatureFlagResult {
  const { me, isAuthenticated } = useContext(UserContext);
  const [isEnabled, setIsEnabled] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  const checkFlag = useCallback(async () => {
    if (!isAuthenticated || !me) {
      // User not authenticated, flag is disabled
      setIsEnabled(false);
      setIsLoading(false);
      setError(null);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      // Try to get from cache first
      if (isCacheValid(me.preferences.user_id)) {
        const cachedValue = cache?.flags[flagName] ?? false;
        setIsEnabled(cachedValue);
        setIsLoading(false);
        return;
      }

      // Fetch individual flag if not in cache
      const response = await fetch(
        `/api/feature-flags/${encodeURIComponent(flagName)}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.error) {
        throw new Error(data.error);
      }

      setIsEnabled(data.isEnabled || false);
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error');
      setError(error);
      setIsEnabled(false); // Default to disabled on error
    } finally {
      setIsLoading(false);
    }
  }, [flagName, me, isAuthenticated]);

  useEffect(() => {
    checkFlag();
  }, [checkFlag]);

  return {
    isEnabled,
    isLoading,
    error,
  };
}

/**
 * Hook to get all feature flags for the current user
 * @param flagNames - Optional array of specific flag names to fetch
 * @returns Object with flags, isLoading, and error
 */
export function useFeatureFlags(flagNames?: string[]): UseFeatureFlagsResult {
  const { me, isAuthenticated } = useContext(UserContext);
  const [flags, setFlags] = useState<Record<string, boolean>>({});
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  // Memoize flagNames to prevent unnecessary re-renders
  const memoizedFlagNames = useMemo(() => flagNames, [flagNames?.join(',')]);

  const fetchFlags = useCallback(async () => {
    if (!isAuthenticated || !me) {
      // User not authenticated, all flags are disabled
      setFlags({});
      setIsLoading(false);
      setError(null);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      // Check cache first
      if (isCacheValid(me.preferences.user_id)) {
        const cachedFlags = cache?.flags || {};

        // If specific flag names requested, filter the cache
        if (memoizedFlagNames) {
          const filteredFlags: Record<string, boolean> = {};
          memoizedFlagNames.forEach((name) => {
            filteredFlags[name] = cachedFlags[name] || false;
          });
          setFlags(filteredFlags);
        } else {
          setFlags(cachedFlags);
        }

        setIsLoading(false);
        return;
      }

      // Fetch from API
      const fetchedFlags = await fetchFeatureFlags(me.preferences.user_id);

      // If specific flag names requested, filter the results
      if (memoizedFlagNames) {
        const filteredFlags: Record<string, boolean> = {};
        memoizedFlagNames.forEach((name) => {
          filteredFlags[name] = fetchedFlags[name] || false;
        });
        setFlags(filteredFlags);
      } else {
        setFlags(fetchedFlags);
      }
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error');
      setError(error);
      setFlags({}); // Default to empty on error
    } finally {
      setIsLoading(false);
    }
  }, [memoizedFlagNames, me, isAuthenticated]);

  useEffect(() => {
    fetchFlags();
  }, [fetchFlags]);

  return {
    flags,
    isLoading,
    error,
  };
}

/**
 * Utility function to invalidate the feature flags cache
 * Useful when you know flags have been updated and want to force a refresh
 */
export function invalidateFeatureFlagsCache(): void {
  cache = null;
}

/**
 * Utility function to preload feature flags
 * Useful for prefetching flags before they're needed
 */
export async function preloadFeatureFlags(): Promise<void> {
  try {
    // Note: preloadFeatureFlags requires user context to work properly
    throw new Error('preloadFeatureFlags cannot be called without user context. Use the hooks instead.');
  } catch (error) {
    console.error('Error preloading feature flags:', error);
  }
}
