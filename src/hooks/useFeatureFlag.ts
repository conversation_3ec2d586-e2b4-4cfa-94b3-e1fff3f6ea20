import { useContext, useMemo } from 'react';
import {
  FeatureFlagsContext,
} from '@/providers/FeatureFlagProvider';
import {
  UseFeatureFlagResult,
  UseFeatureFlagsResult,
} from '@/lib/feature-flags/types';

/**
 * Hook to check if a specific feature flag is enabled
 * @param flagName - The name of the feature flag to check
 * @returns Object with isEnabled, isLoading, and error
 */
export function useFeatureFlag(flagName: string): UseFeatureFlagResult {
  const { flags, isLoading, error } = useContext(FeatureFlagsContext);

  const isEnabled = useMemo(
    () => flags[flagName] || false,
    [flags, flagName]
  );

  return {
    isEnabled,
    isLoading,
    error,
  };
}

/**
 * Hook to get all feature flags for the current user
 * @returns Object with flags, isLoading, and error
 */
export function useFeatureFlags(): UseFeatureFlagsResult {
  return useContext(FeatureFlagsContext);
}

/**
 * Utility function to invalidate the feature flags cache
 * This can be achieved by calling the `refetch` function from `useFeatureFlags`
 */
export function invalidateFeatureFlagsCache(): void {
  // This function is now a no-op, refetch is handled by the provider
  console.warn(
    'invalidateFeatureFlagsCache is deprecated. Use the `refetch` function from `useFeatureFlags` instead.'
  );
}
