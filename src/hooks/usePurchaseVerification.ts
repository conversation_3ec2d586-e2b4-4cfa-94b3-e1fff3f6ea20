import { useState, useCallback, useRef, useEffect } from 'react';

export interface UsageData {
  tokens?: {
    used: number;
    remaining: number;
    total: number;
    resetDate: string;
  };
  images?: {
    used: number;
    remaining: number;
    total: number;
    resetDate: string;
  };
  comparisons?: {
    used: number;
    remaining: number;
    total: number;
    resetDate: string;
  };
}

export interface PurchaseVerificationState {
  isVerifying: boolean;
  verificationComplete: boolean;
  error: string | null;
  confidence: 'high' | 'medium' | 'low' | null;
}

export interface VerificationContext {
  sessionId: string;
  purchaseType: 'tokens' | 'images';
  startTime: number;
  initialUsage: UsageData;
  attempts: number;
  maxAttempts: number;
}

export interface UsePurchaseVerificationReturn {
  state: PurchaseVerificationState;
  startVerification: (
    sessionId: string,
    purchaseType: 'tokens' | 'images'
  ) => Promise<void>;
  resetVerification: () => void;
}

const VERIFICATION_TIMEOUT = 30000; // 30 seconds
const INITIAL_DELAY = 3000; // 3 seconds initial delay
const MAX_ATTEMPTS = 3;
const RETRY_DELAYS = [2000, 4000, 8000]; // Exponential backoff

export function usePurchaseVerification(): UsePurchaseVerificationReturn {
  const [state, setState] = useState<PurchaseVerificationState>({
    isVerifying: false,
    verificationComplete: false,
    error: null,
    confidence: null,
  });

  const abortControllerRef = useRef<AbortController | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const overallTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const verificationContextRef = useRef<VerificationContext | null>(null);

  // Cleanup function
  const cleanup = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    if (overallTimeoutRef.current) {
      clearTimeout(overallTimeoutRef.current);
      overallTimeoutRef.current = null;
    }
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  const resetVerification = useCallback(() => {
    cleanup();
    setState({
      isVerifying: false,
      verificationComplete: false,
      error: null,
      confidence: null,
    });
    verificationContextRef.current = null;
  }, [cleanup]);

  const fetchWithTimeout = useCallback(
    async (url: string, options: RequestInit = {}) => {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout per request

      try {
        const response = await fetch(url, {
          ...options,
          signal: controller.signal,
        });
        clearTimeout(timeoutId);
        return response;
      } catch (error) {
        clearTimeout(timeoutId);
        throw error;
      }
    },
    []
  );

  const fetchInitialUsage = useCallback(async (): Promise<UsageData> => {
    const response = await fetchWithTimeout('/api/me/quota');
    if (!response.ok) {
      throw new Error('Failed to fetch initial usage data');
    }
    return response.json();
  }, [fetchWithTimeout]);

  const checkWebhookStatus = useCallback(
    async (sessionId: string) => {
      const response = await fetchWithTimeout(
        `/api/purchases/status?session_id=${sessionId}`
      );
      if (!response.ok) {
        throw new Error('Failed to check webhook status');
      }
      return response.json();
    },
    [fetchWithTimeout]
  );

  const fetchUpdatedUsage = useCallback(async (): Promise<UsageData> => {
    const response = await fetchWithTimeout('/api/me/quota');
    if (!response.ok) {
      throw new Error('Failed to fetch updated usage data');
    }
    return response.json();
  }, [fetchWithTimeout]);

  const performVerification = useCallback(
    async (context: VerificationContext) => {
      try {
        // Check webhook status
        const webhookStatus = await checkWebhookStatus(context.sessionId);

        // Fetch updated usage data
        const updatedUsage = await fetchUpdatedUsage();

        // Determine verification result based on multiple indicators
        const hasWebhookEvents =
          webhookStatus.processed && webhookStatus.recent_webhook_events > 0;

        let usageIncreased = false;
        let confidence: 'high' | 'medium' | 'low' = 'low';

        if (context.purchaseType === 'tokens') {
          const initialRemaining = context.initialUsage.tokens?.remaining || 0;
          const updatedRemaining = updatedUsage.tokens?.remaining || 0;
          usageIncreased = updatedRemaining > initialRemaining;
        } else if (context.purchaseType === 'images') {
          const initialRemaining = context.initialUsage.images?.remaining || 0;
          const updatedRemaining = updatedUsage.images?.remaining || 0;
          usageIncreased = updatedRemaining > initialRemaining;
        }

        // Determine confidence and success
        if (hasWebhookEvents && usageIncreased) {
          confidence = 'high';
          return { success: true, confidence, updatedUsage };
        } else if (hasWebhookEvents && !usageIncreased) {
          // Webhook processed but no usage increase - likely processed before initial fetch
          confidence = 'high';
          return { success: true, confidence, updatedUsage };
        } else if (!hasWebhookEvents && usageIncreased) {
          // Usage increased but no webhook events - possible timing issue
          confidence = 'medium';
          return { success: true, confidence, updatedUsage };
        } else if (!hasWebhookEvents && !usageIncreased) {
          // No webhook events and no usage increase - likely failed
          confidence = 'high';
          return { success: false, confidence, updatedUsage };
        }

        // Fallback case
        confidence = 'low';
        return { success: false, confidence, updatedUsage };
      } catch (error) {
        if (process.env.NODE_ENV !== 'test') {
          console.error('Verification attempt failed:', error);
        }
        throw error;
      }
    },
    [checkWebhookStatus, fetchUpdatedUsage]
  );

  const attemptVerification = useCallback(
    async (context: VerificationContext): Promise<void> => {
      try {
        const result = await performVerification(context);

        if (result.success) {
          setState((prev) => ({
            ...prev,
            isVerifying: false,
            verificationComplete: true,
            confidence: result.confidence,
            error: null,
          }));
          return;
        }

        // If verification failed and we have attempts left, retry
        if (context.attempts < context.maxAttempts) {
          const delay =
            RETRY_DELAYS[context.attempts - 1] ||
            RETRY_DELAYS[RETRY_DELAYS.length - 1];

          timeoutRef.current = setTimeout(() => {
            if (verificationContextRef.current) {
              verificationContextRef.current.attempts += 1;
              attemptVerification(verificationContextRef.current);
            }
          }, delay);
          return;
        }

        // All attempts exhausted
        setState((prev) => ({
          ...prev,
          isVerifying: false,
          verificationComplete: true,
          confidence: result.confidence,
          error:
            result.confidence === 'high'
              ? 'Purchase completed but credits may not have been added due to a processing error. Please contact support if your credits don\'t appear within 5 minutes.'
              : 'Unable to verify purchase status. Please check your usage or contact support if your credits don\'t appear within 10 minutes.',
        }));
      } catch {
        // Network or other error - retry if attempts left
        if (context.attempts < context.maxAttempts) {
          const delay =
            RETRY_DELAYS[context.attempts - 1] ||
            RETRY_DELAYS[RETRY_DELAYS.length - 1];

          timeoutRef.current = setTimeout(() => {
            if (verificationContextRef.current) {
              verificationContextRef.current.attempts += 1;
              attemptVerification(verificationContextRef.current);
            }
          }, delay);
          return;
        }

        // All attempts exhausted with errors
        setState((prev) => ({
          ...prev,
          isVerifying: false,
          verificationComplete: true,
          confidence: 'low',
          error:
            'Unable to verify purchase status due to connection issues. Please refresh the page or contact support if your credits don\'t appear within 10 minutes.',
        }));
      }
    },
    [performVerification]
  );

  const startVerification = useCallback(
    async (sessionId: string, purchaseType: 'tokens' | 'images') => {
      // Clean up any existing verification
      cleanup();

      setState({
        isVerifying: true,
        verificationComplete: false,
        error: null,
        confidence: null,
      });

      try {
        // Create new abort controller for this verification
        abortControllerRef.current = new AbortController();

        // Fetch initial usage data
        const initialUsage = await fetchInitialUsage();

        // Set up verification context
        const context: VerificationContext = {
          sessionId,
          purchaseType,
          startTime: Date.now(),
          initialUsage,
          attempts: 1,
          maxAttempts: MAX_ATTEMPTS,
        };

        verificationContextRef.current = context;

        // Start verification after initial delay
        timeoutRef.current = setTimeout(() => {
          if (verificationContextRef.current) {
            attemptVerification(verificationContextRef.current);
          }
        }, INITIAL_DELAY);

        // Set overall timeout and store in ref for proper cleanup
        overallTimeoutRef.current = setTimeout(() => {
          if (verificationContextRef.current) {
            cleanup();
            setState((prev) => ({
              ...prev,
              isVerifying: false,
              verificationComplete: true,
              confidence: 'low',
              error:
                'Purchase verification timed out. Please check your usage or contact support if your credits don\'t appear within 10 minutes.',
            }));
          }
        }, VERIFICATION_TIMEOUT);
      } catch (error) {
        if (process.env.NODE_ENV !== 'test') {
          console.error('Failed to start verification:', error);
        }
        setState((prev) => ({
          ...prev,
          isVerifying: false,
          verificationComplete: true,
          confidence: 'low',
          error:
            'Failed to start purchase verification. Please refresh the page or contact support.',
        }));
      }
    },
    [cleanup, fetchInitialUsage, attemptVerification]
  );

  return {
    state,
    startVerification,
    resetVerification,
  };
}
