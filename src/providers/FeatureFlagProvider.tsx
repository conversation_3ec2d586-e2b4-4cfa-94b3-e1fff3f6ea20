'use client';
import React, {
  createContext,
  useState,
  useEffect,
  useCallback,
  useContext,
  useMemo,
} from 'react';
import { UserContext } from './AuthProvider';
import {
  UseFeatureFlagsResult,
  FEATURE_FLAG_CACHE_TTL,
} from '@/lib/feature-flags/types';

interface FeatureFlagCache {
  flags: Record<string, boolean>;
  timestamp: number;
  userId: string;
}

let cache: FeatureFlagCache | null = null;

interface FeatureFlagsContextType extends UseFeatureFlagsResult {
  refetch: () => Promise<void>;
}

export const FeatureFlagsContext = createContext<FeatureFlagsContextType>({
  flags: {},
  isLoading: true,
  error: null,
  refetch: async () => {},
});

async function fetchFeatureFlags(): Promise<Record<string, boolean>> {
  try {
    const response = await fetch('/api/feature-flags');
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    if (data.error) {
      throw new Error(data.error);
    }
    return data.flags || {};
  } catch (error) {
    console.error('Error fetching feature flags:', error);
    throw error;
  }
}

function isCacheValid(userId: string): boolean {
  if (!cache || cache.userId !== userId) {
    return false;
  }
  const now = Date.now();
  return now - cache.timestamp < FEATURE_FLAG_CACHE_TTL;
}

export function FeatureFlagsProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const { me, isAuthenticated } = useContext(UserContext);
  const [flags, setFlags] = useState<Record<string, boolean>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchFlags = useCallback(async () => {
    if (!isAuthenticated || !me) {
      setFlags({});
      setIsLoading(false);
      return;
    }

    const userId = me.preferences.user_id;

    if (isCacheValid(userId)) {
      setFlags(cache!.flags);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    try {
      const fetchedFlags = await fetchFeatureFlags();
      setFlags(fetchedFlags);
      cache = {
        flags: fetchedFlags,
        timestamp: Date.now(),
        userId,
      };
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Unknown error'));
      setFlags({});
    } finally {
      setIsLoading(false);
    }
  }, [me, isAuthenticated]);

  useEffect(() => {
    fetchFlags();
  }, [fetchFlags]);

  const value = useMemo(
    () => ({
      flags,
      isLoading,
      error,
      refetch: fetchFlags,
    }),
    [flags, isLoading, error, fetchFlags]
  );

  return (
    <FeatureFlagsContext.Provider value={value}>
      {children}
    </FeatureFlagsContext.Provider>
  );
}

export function __TEST_ONLY_resetCache() {
  cache = null;
}
