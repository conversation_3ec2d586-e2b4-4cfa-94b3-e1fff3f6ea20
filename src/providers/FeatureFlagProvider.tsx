'use client';
import React, {
  createContext,
  useState,
  useEffect,
  useCallback,
  useContext,
  useMemo,
  useRef,
} from 'react';
import { UserContext } from './AuthProvider';
import {
  UseFeatureFlagsResult,
  FEATURE_FLAG_CACHE_TTL,
} from '@/lib/feature-flags/types';

interface FeatureFlagCache {
  flags: Record<string, boolean>;
  timestamp: number;
  userId: string;
}

interface FeatureFlagsContextType extends UseFeatureFlagsResult {
  refetch: () => Promise<void>;
}

export const FeatureFlagsContext = createContext<FeatureFlagsContextType>({
  flags: {},
  isLoading: true,
  error: null,
  refetch: async () => {},
});

async function fetchFeatureFlags(): Promise<Record<string, boolean>> {
  try {
    const response = await fetch('/api/feature-flags');
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    if (data.error) {
      throw new Error(data.error);
    }
    return data.flags || {};
  } catch (error) {
    console.error('Error fetching feature flags:', error);
    throw error;
  }
}

export function FeatureFlagsProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const { me, isAuthenticated } = useContext(UserContext);
  const [flags, setFlags] = useState<Record<string, boolean>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  // Use ref to store cache scoped to this provider instance
  const cacheRef = useRef<FeatureFlagCache | null>(null);

  // Move isCacheValid inside provider to access cacheRef
  const isCacheValid = useCallback((userId: string): boolean => {
    if (!cacheRef.current || cacheRef.current.userId !== userId) {
      return false;
    }
    const now = Date.now();
    return now - cacheRef.current.timestamp < FEATURE_FLAG_CACHE_TTL;
  }, []);

  const fetchFlags = useCallback(async () => {
    if (!isAuthenticated || !me) {
      setFlags({});
      setIsLoading(false);
      return;
    }

    const userId = me.preferences.user_id;

    if (isCacheValid(userId)) {
      setFlags(cacheRef.current!.flags);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    try {
      const fetchedFlags = await fetchFeatureFlags();
      setFlags(fetchedFlags);
      cacheRef.current = {
        flags: fetchedFlags,
        timestamp: Date.now(),
        userId,
      };
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Unknown error'));
      setFlags({});
    } finally {
      setIsLoading(false);
    }
  }, [me, isAuthenticated, isCacheValid]);

  useEffect(() => {
    fetchFlags();
  }, [fetchFlags]);

  // Cleanup cache on unmount to prevent memory leaks
  useEffect(() => {
    return () => {
      cacheRef.current = null;
    };
  }, []);

  const value = useMemo(
    () => ({
      flags,
      isLoading,
      error,
      refetch: fetchFlags,
    }),
    [flags, isLoading, error, fetchFlags]
  );

  return (
    <FeatureFlagsContext.Provider value={value}>
      {children}
    </FeatureFlagsContext.Provider>
  );
}

// NOTE: Test cache reset is no longer needed since cache is now scoped
// to each provider instance and gets cleaned up automatically
