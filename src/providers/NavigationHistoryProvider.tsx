'use client';

import React, { createContext, useContext, useState, useEffect, Suspense } from 'react';
import { usePathname, useSearchParams } from 'next/navigation';

interface NavigationHistoryContextType {
  previousPath: string | null;
  setPreviousPath: (path: string) => void;
  getPreviousPathForSettings: () => string;
  isOnSettingsPage: boolean;
}

const NavigationHistoryContext = createContext<NavigationHistoryContextType>({
  previousPath: null,
  setPreviousPath: () => {},
  getPreviousPathForSettings: () => '/',
  isOnSettingsPage: false,
});

// Separate component that uses useSearchParams
function NavigationTracker({
  onNavigationChange,
}: {
  onNavigationChange: (currentPath: string, isOnSettings: boolean) => void;
}) {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  useEffect(() => {
    const currentPath = pathname + (searchParams.toString() ? `?${searchParams.toString()}` : '');
    const currentlyOnSettings = pathname === '/settings';
    onNavigationChange(currentPath, currentlyOnSettings);
  }, [pathname, searchParams, onNavigationChange]);

  return null;
}

export function NavigationHistoryProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const [previousPath, setPreviousPathState] = useState<string | null>(null);
  const [settingsEntryPath, setSettingsEntryPath] = useState<string | null>(null);
  const [isOnSettings, setIsOnSettings] = useState(false);

  // Handle navigation changes from the tracker component
  const handleNavigationChange = React.useCallback((currentPath: string, currentlyOnSettings: boolean) => {
    // If we're navigating to settings from a non-settings page
    if (currentlyOnSettings && !isOnSettings && previousPath && !previousPath.startsWith('/settings')) {
      // Store the path we came from when entering settings
      setSettingsEntryPath(previousPath);
    }

    // Update previous path for non-settings routes only
    // This ensures we don't track internal settings navigation (tab switching)
    if (!currentlyOnSettings) {
      setPreviousPathState(currentPath);
    }

    // Update settings state
    setIsOnSettings(currentlyOnSettings);
  }, [previousPath, isOnSettings]);

  const setPreviousPath = (path: string) => {
    setPreviousPathState(path);
  };

  const getPreviousPathForSettings = (): string => {
    // Return the path we entered settings from, or fallback to previous path, or home
    const fallbackPath = settingsEntryPath || previousPath || '/';

    // Handle edge cases where the fallback might be invalid
    if (fallbackPath.startsWith('/settings')) {
      // If somehow we're trying to go back to settings, go to home instead
      return '/';
    }

    // Handle auth routes - redirect to home instead
    if (fallbackPath.startsWith('/auth/')) {
      return '/';
    }

    return fallbackPath;
  };

  const value = {
    previousPath,
    setPreviousPath,
    getPreviousPathForSettings,
    isOnSettingsPage: isOnSettings,
  };

  return (
    <NavigationHistoryContext.Provider value={value}>
      <Suspense fallback={null}>
        <NavigationTracker onNavigationChange={handleNavigationChange} />
      </Suspense>
      {children}
    </NavigationHistoryContext.Provider>
  );
}

export function useNavigationHistory() {
  const context = useContext(NavigationHistoryContext);
  if (!context) {
    throw new Error('useNavigationHistory must be used within a NavigationHistoryProvider');
  }
  return context;
}
