'use client';
import { User } from '@supabase/supabase-js';
import { createContext, useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { authService } from '@/lib/supabase/auth';
import { SubscriptionProvider } from './SubscriptionProvider';
import { Me } from '@/lib/supabase/types';
import { useAnalytics } from '@/hooks/useAnalytics';

export const UserContext = createContext<{
  user: User | null;
  isAuthenticated: boolean;
  signOut: () => Promise<void>;
  me: Me | null;
  fetchMe: () => Promise<void>;
}>({
  user: null,
  isAuthenticated: false,
  signOut: async () => {},
  me: null,
  fetchMe: async () => {},
});

export default function AuthProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const [user, setUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const router = useRouter();
  const [me, setMe] = useState<Me | null>(null);
  const analytics = useAnalytics();

  const fetchMe = async () => {
    const response = await fetch('/api/me');
    const data = await response.json();
    setMe(data.me);
  };

  useEffect(() => {
    fetchMe();
  }, []);

  // Helper function to verify user session separately
  const verifyUserSession = async (sessionUser: User) => {
    try {
      const { data, error } = await authService.getUser();

      // Only logout if there's a clear authentication error, not network issues
      if (error && (error.message?.includes('JWT') || error.message?.includes('expired'))) {
        console.warn('Session verification failed due to invalid/expired token:', error);
        setUser(null);
        setIsAuthenticated(false);
        return;
      }

      // If getUser succeeds, use the fresh user data
      if (data.user && !error) {
        setUser(data.user);
        setIsAuthenticated(true);
      } else if (!error) {
        // Only set to null if there's no error but also no user (shouldn't happen normally)
        setUser(sessionUser);
        setIsAuthenticated(true);
      }
      // For network errors or other temporary issues, keep the session user
      else {
        console.warn('User verification failed (likely network issue), keeping session:', error);
        setUser(sessionUser);
        setIsAuthenticated(true);
      }
    } catch (error) {
      // Handle any unhandled promise rejections
      console.error('Error verifying user session:', error);
      // On unexpected errors, keep the session to avoid unnecessary logouts
      setUser(sessionUser);
      setIsAuthenticated(true);
    }
  };

  useEffect(() => {
    const {
      data: { subscription },
    } = authService.onAuthStateChange((event, session) => {
      if (session?.user) {
        // Set user immediately from session for responsive UI
        setUser(session.user);
        setIsAuthenticated(true);

        // Verify in background without blocking the UI
        verifyUserSession(session.user);
      } else {
        setUser(null);
        setIsAuthenticated(false);
      }
    });

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const signOut = async () => {
    if (user) {
      // Track sign-out event before actually signing out
      analytics.trackEvent('User Signed Out');
    }

    await authService.signOut();
    router.push('/auth/login');
  };

  return (
    <UserContext.Provider
      value={{ user, isAuthenticated, signOut, me, fetchMe }}
    >
      <SubscriptionProvider>{children}</SubscriptionProvider>
    </UserContext.Provider>
  );
}
