import {
  ImageGenerationService,
  ImageGenerationOptions,
} from '@/services/llm/image';
import { QuotaService } from '@/services/quota';
import { DatabaseService } from '@/lib/supabase/db';
import { logger } from '@/lib/logger';
import { AppError, ErrorCode } from '@/lib/error';
import { SupabaseClient, User } from '@supabase/supabase-js';
import type { AttachmentPayload } from '@/lib/supabase/types';

export interface ImageHandlerResult {
  success: boolean;
  imageUrl?: string;
  error?: string;
  messageId: string;
}

export class ImageHandler {
  private static instance: ImageHandler;
  private imageService: ImageGenerationService;
  private quotaService: QuotaService;
  private log = logger.child({ service: 'ImageHandler' });
  private supabase: SupabaseClient;

  private constructor(supabase: SupabaseClient) {
    this.imageService = ImageGenerationService.getInstance();
    this.quotaService = QuotaService.getInstance();
    this.supabase = supabase;
  }

  public static getInstance(supabase: SupabaseClient): ImageHandler {
    if (!ImageHandler.instance) {
      ImageHandler.instance = new ImageHandler(supabase);
    }
    return ImageHandler.instance;
  }

  private convertSizeToAspectRatio(): '1:1' {
    // All the old sizes were square, so they all map to 1:1
    return '1:1';
  }

  async handleImageToolCall(
    user: User,
    messageId: string,
    prompt: string,
    size: '512x512' | '768x768' | '1024x1024' = '768x768',
    streamController?: (data: {
      id: string;
      replace: boolean;
      content: string;
      attachments: unknown[] | null;
    }) => void
  ): Promise<ImageHandlerResult> {
    this.log.info('Handling image tool call', {
      userId: user.id,
      messageId,
      prompt: prompt.substring(0, 100),
      size,
    });

    try {
      // 1. Check quota first
      const quotaCheck = await this.quotaService.checkImageQuota(user, size);
      if (!quotaCheck.allowed) {
        const errorMessage = quotaCheck.reason || 'Image quota exceeded';
        this.log.warn('Image generation blocked by quota', {
          userId: user.id,
          reason: quotaCheck.reason,
          usage: quotaCheck.usage,
        });

        // Update message with quota error
        await this.updateMessageWithError(messageId, errorMessage);

        // Stream error delta
        if (streamController) {
          streamController({
            id: messageId,
            replace: true,
            content: errorMessage,
            attachments: null,
          });
        }

        return {
          success: false,
          error: errorMessage,
          messageId,
        };
      }

      // 2. Increment usage count atomically (reserve the credit)
      await this.quotaService.incrementImageUsage(user.id);

      try {
        // 3. Generate the image
        const imageOptions: ImageGenerationOptions = {
          prompt,
          aspectRatio: this.convertSizeToAspectRatio(),
          service: 'core', // Default to core service
        };
        const imageResult = await this.imageService.generateImage(imageOptions);

        // 4. Upload to Supabase Storage
        const imageUrl = await this.uploadImageToStorage(
          user.id,
          messageId,
          imageResult.base64
        );

        // 5. Save image record to database
        await this.saveImageRecord(
          user.id,
          messageId,
          prompt,
          imageUrl,
          imageResult.width,
          imageResult.height,
          imageResult.safetyScore,
          imageResult.providerJobId
        );

        // 6. Update message with image attachment
        await this.updateMessageWithImage(messageId, imageUrl);

        // 7. Stream final delta
        if (streamController) {
          const attachments: AttachmentPayload[] = [
            {
              name: 'Generated Image',
              type: 'image_url',
              url: imageUrl,
              image_url: {
                url: imageUrl,
                detail: 'auto',
              },
            },
          ];

          streamController({
            id: messageId,
            replace: true,
            content: 'Here\'s the image you requested.',
            attachments,
          });
        }

        this.log.info('Image generated successfully', {
          userId: user.id,
          messageId,
          imageUrl,
          size,
        });

        return {
          success: true,
          imageUrl,
          messageId,
        };
      } catch (imageError) {
        // Refund credit on failure
        await this.quotaService.decrementImageUsage(user.id);
        throw imageError;
      }
    } catch (error) {
      this.log.error({
        err: error,
        userId: user.id,
        messageId,
      }, 'Error handling image tool call');

      const errorMessage = 'Image generation failed. Please try again.';

      // Update message with error
      await this.updateMessageWithError(messageId, errorMessage);

      // Stream error delta
      if (streamController) {
        streamController({
          id: messageId,
          replace: true,
          content: errorMessage,
          attachments: null,
        });
      }

      return {
        success: false,
        error: errorMessage,
        messageId,
      };
    }
  }

  private async uploadImageToStorage(
    userId: string,
    messageId: string,
    base64Data: string
  ): Promise<string> {
    try {
      // Convert base64 to buffer
      const buffer = Buffer.from(base64Data, 'base64');

      // Generate file path
      const fileName = `${messageId}.png`;
      const filePath = `${userId}/images/${fileName}`;

      // Upload to Supabase Storage
      const { error } = await this.supabase.storage
        .from('attachments')
        .upload(filePath, buffer, {
          contentType: 'image/png',
        });

      if (error) {
        throw new AppError(
          `Failed to upload image: ${error.message}`,
          ErrorCode.INTERNAL,
          500
        );
      }

      // Get public URL
      const { data: urlData } = this.supabase.storage
        .from('attachments')
        .getPublicUrl(filePath);

      return urlData.publicUrl;
    } catch (error) {
      this.log.error({
        err: error,
        userId,
        messageId,
      }, 'Error uploading image to storage');
      throw new AppError(
        'Failed to upload image to storage',
        ErrorCode.INTERNAL,
        500
      );
    }
  }

  private async saveImageRecord(
    userId: string,
    messageId: string,
    prompt: string,
    url: string,
    width: number,
    height: number,
    safetyScore?: number,
    providerJobId?: string
  ): Promise<void> {
    try {
      const { error } = await this.supabase.from('images').insert({
        user_id: userId,
        message_id: messageId,
        prompt,
        url,
        width,
        height,
        safety_score: safetyScore,
        provider_job_id: providerJobId,
      });

      if (error) {
        throw new AppError(
          `Failed to save image record: ${error.message}`,
          ErrorCode.INTERNAL,
          500
        );
      }
    } catch (error) {
      this.log.error({ err: error, userId, messageId }, 'Error saving image record');
      throw error;
    }
  }

  private async updateMessageWithImage(
    messageId: string,
    imageUrl: string
  ): Promise<void> {
    try {
      const db = DatabaseService.getInstance(this.supabase);

      const attachments: AttachmentPayload[] = [
        {
          name: 'Generated Image',
          type: 'image_url',
          url: imageUrl,
          image_url: {
            url: imageUrl,
            detail: 'auto',
          },
        },
      ];

      await db.updateMessage(messageId, {
        content: 'Here\'s the image you requested.',
        attachments,
      });
    } catch (error) {
      this.log.error({
        err: error,
        messageId,
        imageUrl,
      }, 'Error updating message with image');
      throw error;
    }
  }

  private async updateMessageWithError(
    messageId: string,
    errorMessage: string
  ): Promise<void> {
    try {
      const db = DatabaseService.getInstance(this.supabase);

      await db.updateMessage(messageId, {
        content: errorMessage,
        attachments: null,
      });
    } catch (error) {
      this.log.error({
        err: error,
        messageId,
        errorMessage,
      }, 'Error updating message with error');
      // Don't throw here - we don't want to fail the whole request
    }
  }
}
