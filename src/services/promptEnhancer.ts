import { logger } from '@/lib/logger';
import { AppError, ErrorCode } from '@/lib/error';

export interface PromptEnhancementConfig {
  model: string;
  temperature?: number;
  maxTokens?: number;
  timeoutMs?: number;
}

export interface PromptEnhancementResponse {
  enhancedPrompt: string;
  originalPrompt: string;
  success: boolean;
  error?: string;
}

export class PromptEnhancer {
  private apiKey: string;
  private baseUrl: string;
  private siteUrl?: string;
  private appName?: string;
  private log = logger.child({ service: 'PromptEnhancer' });

  constructor(apiKey: string) {
    this.apiKey = apiKey;
    this.baseUrl = 'https://openrouter.ai/api/v1';
    this.siteUrl = process.env.OPENROUTER_SITE_URL;
    this.appName = process.env.OPENROUTER_APP_NAME || 'SabiChat';
  }

  private getRequestHeaders(): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${this.apiKey}`,
    };

    if (this.siteUrl) {
      headers['HTTP-Referer'] = this.siteUrl;
    }

    if (this.appName) {
      headers['X-Title'] = this.appName;
    }

    return headers;
  }

  private getEnhancementPrompt(originalPrompt: string): string {
    return `You are an expert prompt engineer. Your task is to enhance the following user prompt to make it clearer, more specific, and more effective while preserving the user's original intent.

Guidelines for enhancement:
1. Make the prompt more specific and detailed
2. Add context where it would be helpful
3. Improve clarity and remove ambiguity
4. Maintain the user's original intent and tone
5. Add relevant examples or constraints if beneficial
6. Ensure the enhanced prompt is still natural and conversational
7. If the original prompt is already well-structured, make minimal improvements

Important: Only return the enhanced prompt. Do not include any explanations, metadata, or additional text, do not wrap the enhanced prompt in any special characters or quotes.

Original prompt: "${originalPrompt}"

Enhanced prompt:`;
  }

  public async enhancePrompt(
    originalPrompt: string,
    config: PromptEnhancementConfig
  ): Promise<PromptEnhancementResponse> {
    try {
      if (!originalPrompt?.trim()) {
        throw new AppError(
          'Original prompt cannot be empty',
          ErrorCode.API_VALIDATION,
          400
        );
      }

      this.log.info('Starting prompt enhancement', {
        originalLength: originalPrompt.length,
        model: config.model,
      });

      const enhancementPrompt = this.getEnhancementPrompt(originalPrompt);

      const requestBody = {
        model: config.model,
        messages: [
          {
            role: 'user',
            content: enhancementPrompt,
          },
        ],
        temperature: config.temperature ?? 0.7,
        max_tokens: config.maxTokens ?? 1000,
        stream: false,
      };

      const controller = new AbortController();
      const timeoutId = setTimeout(() => {
        controller.abort();
      }, config.timeoutMs ?? 30000);

      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: this.getRequestHeaders(),
        body: JSON.stringify(requestBody),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        let errorMessage = 'Unknown error occurred';

        try {
          const errorData = JSON.parse(errorText);
          errorMessage = errorData.error?.message || errorMessage;
        } catch {
          errorMessage = errorText || errorMessage;
        }

        throw new AppError(
          `OpenRouter API error: ${errorMessage}`,
          ErrorCode.AI_COMPLETION,
          response.status
        );
      }

      const data = await response.json();

      if (!data.choices?.[0]?.message?.content) {
        throw new AppError(
          'Invalid response from OpenRouter API',
          ErrorCode.AI_COMPLETION,
          500
        );
      }

      const enhancedPrompt = data.choices[0].message.content.trim();

      this.log.info('Prompt enhancement completed', {
        originalLength: originalPrompt.length,
        enhancedLength: enhancedPrompt.length,
        model: config.model,
      });

      return {
        enhancedPrompt,
        originalPrompt,
        success: true,
      };
    } catch (error) {
      this.log.error({ err: error }, 'Error enhancing prompt');

      if (error instanceof AppError) {
        return {
          enhancedPrompt: originalPrompt,
          originalPrompt,
          success: false,
          error: error.message,
        };
      }

      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';

      return {
        enhancedPrompt: originalPrompt,
        originalPrompt,
        success: false,
        error: `Failed to enhance prompt: ${errorMessage}`,
      };
    }
  }
}

export const createPromptEnhancer = (apiKey: string): PromptEnhancer => {
  return new PromptEnhancer(apiKey);
};
