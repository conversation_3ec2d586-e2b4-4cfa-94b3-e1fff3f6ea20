import { Tiktoken } from 'js-tiktoken/lite';
import o200k_base from 'js-tiktoken/ranks/o200k_base';
import claude from './claude.json';
import { logger } from '@/lib/logger';

const log = logger.child({ module: 'tokenizer' });

export type SupportedModel =
  | 'gpt-4o-2024-08-06'
  | 'claude-3-haiku-20240307'
  | 'gpt-4.5-preview-2025-02-27'
  | 'o1-preview-2024-09-12'
  | 'o1-mini-2024-09-12'
  | 'gpt-4o-mini-2024-07-18'
  | 'claude-3-7-sonnet-20250219'
  | 'claude-3-5-sonnet-20241022'
  | 'gemini-2.5-pro-preview-03-25'
  | 'gemini-2.0-flash'
  | 'gemini-2.0-flash-lite'
  | 'gemini-1.5-flash'
  | 'gemini-1.5-flash-8b'
  | 'gemini-1.5-pro'
  | 'deepseek-reasoner'
  | 'deepseek-chat';

export class TokenizerService {
  private static instance: TokenizerService;
  private openAIEncoder: Tiktoken;
  private claudeTokenizer: Tiktoken;

  private constructor() {
    log.info('Initializing TokenizerService');
    this.openAIEncoder = new Tiktoken(o200k_base);
    this.claudeTokenizer = new Tiktoken({
      bpe_ranks: claude.bpe_ranks,
      special_tokens: claude.special_tokens,
      pat_str: claude.pat_str,
    });
    log.info('Tokenizers initialized successfully');
  }

  public static getInstance(): TokenizerService {
    if (!TokenizerService.instance) {
      log.info('Creating a new instance of TokenizerService');
      TokenizerService.instance = new TokenizerService();
    }
    return TokenizerService.instance;
  }

  public countTokens(text: string, model: SupportedModel): number {
    try {
      log.debug({ text, model }, 'Counting tokens');
      let tokenCount;
      if (
        model.startsWith('gpt') ||
        model.startsWith('o') ||
        model.startsWith('gemini') ||
        model.startsWith('deepseek')
      ) {
        tokenCount = this.openAIEncoder.encode(text).length;
      } else if (model.startsWith('claude')) {
        tokenCount = this.claudeTokenizer.encode(text).length;
      } else {
        log.warn({ model }, 'Unsupported model, returning 0 tokens');
        tokenCount = 0;
      }
      log.debug({ tokenCount, model }, 'Token count computed');
      return tokenCount;
    } catch (error) {
      log.error({ err: error, model }, 'Token counting failed');
      throw error;
    }
  }

  public async countStreamTokens(
    stream: ReadableStream<Uint8Array>,
    model: SupportedModel
  ): Promise<number> {
    try {
      log.info({ model }, 'Starting token count for stream');
      let totalTokens = 0;
      const reader = stream.getReader();
      const decoder = new TextDecoder();

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const text = decoder.decode(value);
        const tokenCount = this.countTokens(text, model);
        totalTokens += tokenCount;
        log.debug(
          { chunkSize: value.length, tokenCount },
          'Processed stream chunk'
        );
      }

      log.info({ totalTokens, model }, 'Total tokens counted for stream');
      return totalTokens;
    } catch (error) {
      log.error({ err: error, model }, 'Stream token counting failed');
      throw error;
    }
  }
}
