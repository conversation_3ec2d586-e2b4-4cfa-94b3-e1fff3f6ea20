import { DatabaseService, Model } from '@/lib/supabase/db';

/**
 * In-memory LRU cache with TTL for Supabase LLM models.
 * Sharing this helper avoids code duplication and means we hit the DB
 * at most once per `CACHE_TTL` for the same model.
 */

interface CachedModel {
  model: Model;
  timestamp: number; // epoch millis when the model was cached
}

// 60 seconds is enough to cover bursts while still reflecting updates quickly.
const CACHE_TTL = 60 * 1_000;
// Hard cap to keep memory bounded – oldest entry is evicted when exceeded.
const MAX_CACHE_ENTRIES = 200;

const cache = new Map<string, CachedModel>();

function evictIfNeeded() {
  if (cache.size <= MAX_CACHE_ENTRIES) return;
  // The Map keeps insertion order – the first key is the LRU entry.
  const lruKey = cache.keys().next().value as string | undefined;
  if (lruKey) cache.delete(lruKey);
}

/**
 * Returns the model from cache or fetches from DB on a cache miss / TTL expiry.
 *
 * The function is intentionally side-effect-free outside its own module so it
 * can be imported safely from multiple files without duplicating state.
 */
export async function getCachedModel(
  db: DatabaseService,
  modelId: string
): Promise<Model | null> {
  const now = Date.now();
  const cached = cache.get(modelId);

  if (cached && now - cached.timestamp < CACHE_TTL) {
    // Promote to most-recently used by reinserting.
    cache.delete(modelId);
    cache.set(modelId, { ...cached });
    return cached.model;
  }

  const model = await db.getModel(modelId);
  if (model) {
    cache.set(modelId, { model, timestamp: now });
    evictIfNeeded();
  }

  return model;
}