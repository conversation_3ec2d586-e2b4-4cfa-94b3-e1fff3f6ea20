import { Model } from '@/lib/supabase/db';

export interface ChatMessage {
  role: string;
  content: string;
}

export type ImageUrlDetail = 'low' | 'high' | 'auto';

export interface ImageUrl {
  url: string;
  detail?: ImageUrlDetail;
}

export interface TextContentPart {
  type: 'text';
  text: string;
}

export interface ImageContentPart {
  type: 'image_url';
  image_url: ImageUrl;
}

export interface FileContentPart {
  type: 'file';
  filename: string;
  mime_type: string;
  url?: string;
  base64_data?: string;
}

export type MessageContentPart =
  | TextContentPart
  | ImageContentPart
  | FileContentPart;

export interface Message {
  role: 'user' | 'assistant' | 'system';
  content: string | MessageContentPart[];
  name?: string;
  created_at?: Date;
  id?: string;
  file_annotations?: unknown[] | null;
}


export interface CompletionOptions {
  model: Model;
  stream?: boolean;
  temperature?: number;
  topP?: number;
  maxTokens?: number;
  systemPrompt?: string;
  messageHistory?: Message[];
  stopSequences?: string[];
  useWebSearch?: boolean;
  useImageGeneration?: boolean;
  pdfParsingEngine?: string;
}

export interface CompletionResponse {
  text: string;
  usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  modelId: string;
  finishReason?: string;
}
