import { CompletionOptions, Message } from './types';
import { ErrorCode } from '@/lib/error';
import { AppError } from '@/lib/error';
import { Model } from '@/lib/supabase/db';
import { OpenRouterClient } from './clients/openrouter';
import { logger } from '@/lib/logger';
import {
  SABI_SYSTEM_PROMPT,
  processSystemPrompt,
} from '@/constants/systemPrompts';
import { recordSpanTiming, getTracer } from '@/lib/otel';
// add more random tesxt, make it longer dont repeat the same text
// const simulatedResponse = `Hello, world! This is a simulated response for testing purposes.

// I'm designed to help test the streaming functionality of our application. When in test mode, this text will be streamed back to the client in small chunks to simulate a real AI response.

// The streaming implementation allows for a more interactive experience, as users can see the response being generated in real-time rather than waiting for the entire response to be completed.

// This particular text is just a placeholder and doesn't contain any meaningful information. It's simply here to demonstrate how the streaming works and to help developers test the functionality without making actual API calls.

// In a production environment, this would be replaced with actual AI-generated content from providers like OpenAI or Gemini. The streaming mechanism would remain the same, but the content would be dynamically generated based on the user's input.

// Testing is an important part of software development, and simulated responses like this one help ensure that our application behaves correctly under various conditions.`;

const simulatedResponse = `Hello, world! This is a simulated response for testing purposes.

I'm designed to help test the streaming functionality of our application. When in test mode, this text will be streamed back to the client in small chunks to simulate a real AI response.`;

const completionResponse = 'This is a text response';

interface StreamingConfig {
  defaultMaxTokens: number;
  defaultTemperature: number;
  simulationDelayMs: number;
  simulationInitialDelayMs: number;
}

const DEFAULT_CONFIG: StreamingConfig = {
  defaultMaxTokens: 4000,
  defaultTemperature: 0.7,
  simulationDelayMs: 50,
  simulationInitialDelayMs: 2500,
};

export class LLMService {
  private static instance: LLMService;
  private openRouterClient: OpenRouterClient | null = null;
  private log = logger.child({ service: 'LLMService' });

  private constructor() {
    // OpenRouter is used by default
  }

  public static getInstance(): LLMService {
    if (!LLMService.instance) {
      LLMService.instance = new LLMService();
    }
    return LLMService.instance;
  }

  // OpenRouter is always used now
  private shouldUseOpenRouter(): boolean {
    return !!process.env.OPENROUTER_API_KEY;
  }

  // Get or create OpenRouter client
  private getOpenRouterClient(): OpenRouterClient {
    if (!this.openRouterClient) {
      const apiKey = process.env.OPENROUTER_API_KEY;
      if (!apiKey) {
        throw new AppError(
          'OpenRouter API key is not configured',
          ErrorCode.INTERNAL,
          500
        );
      }
      this.openRouterClient = new OpenRouterClient(apiKey);
    }
    return this.openRouterClient;
  }

  async generateCompletion(
    providerName: string,
    messages: Message[],
    options: CompletionOptions,
    isTestMode?: boolean
  ) {
    const tracer = getTracer('llm-service');
    const span = tracer.startSpan('generateCompletion', {
      attributes: {
        'llm.router': 'openrouter',
        'llm.provider': options.model.provider?.name || 'unknown', // The actual underlying provider
        'llm.model': options.model.name,
        'llm.model_id': options.model.id,
        'llm.test_mode': isTestMode || false,
        'llm.message_count': messages.length,
        'llm.streaming': false,
      },
    });

    try {
      const startTime = performance.now();

      // Handle test mode first
      if (isTestMode) {
        return completionResponse;
      }

      // Always use OpenRouter
      span.setAttribute('llm.router', 'openrouter');
      const openRouterClient = this.getOpenRouterClient();
      const completionOptions = this.configureCompletionOptions(options);

      // Add runtime completion options to telemetry
      span.setAttributes({
        'llm.max_tokens': completionOptions.maxTokens || 0,
        'llm.temperature': completionOptions.temperature || 0,
        'llm.system_prompt_enabled': !!completionOptions.systemPrompt,
        'llm.use_web_search': options.useWebSearch || false,
        'llm.use_image_generation': options.useImageGeneration || false,
      });

      const result = await openRouterClient.generateCompletion(
        messages,
        completionOptions
      );

      recordSpanTiming(span, 'llm.completion', startTime);
      span.setAttribute('llm.success', true);

      return result;
    } catch (error) {
      span.recordException(error as Error);
      span.setAttribute('llm.success', false);
      span.setAttribute('llm.error_type', 'openrouter_error');
      throw error;
    } finally {
      span.end();
    }
  }

  async generateGroupConversationTitle(
    message: string,
    isTestMode: boolean
  ): Promise<string | null> {
    // truncate message to 1000 characters
    const truncatedMessage = message.slice(0, 1000);
    if (!truncatedMessage) {
      return null;
    }
    const model = 'gpt-4.1-nano-2025-04-14';
    const messages: Message[] = [
      {
        role: 'system',
        content: `You are a helpful assistant. You are given a user query in an AI app and you need to generate a short title for it. Be as concise as possible.
          If for example the user query is "What is the capital of France?", the title should be "France Capital". Title should not be more than 4 words.
          Give a title based on context of the query.
          `,
      },
      {
        role: 'user',
        content: `Generate a short title for this message: ${truncatedMessage}`,
      },
    ];
    const options = {
      model: {
        id: model,
        name: model,
        max_tokens: 100,
        provider_id: 'openai',
        openrouter_name: model,
        provider: {
          name: 'openai',
          openrouter_name: 'openai',
        },
      } as Model,
    };
    const completion = await this.generateCompletion(
      'openai',
      messages,
      options,
      isTestMode
    );
    return completion;
  }

  async generateStreamingCompletion(
    providerName: string,
    messages: Message[],
    options: CompletionOptions,
    isTestMode: boolean
  ): Promise<ReadableStream> {
    const tracer = getTracer('llm-service');
    const span = tracer.startSpan('generateStreamingCompletion', {
      attributes: {
        'llm.router': 'openrouter',
        'llm.provider': options.model.provider?.name || 'unknown', // The actual underlying provider
        'llm.model': options.model.name,
        'llm.model_id': options.model.id,
        'llm.test_mode': isTestMode,
        'llm.message_count': messages.length,
        'llm.streaming': true,
      },
    });

    try {
      const startTime = performance.now();

      // Handle test mode
      if (isTestMode) {
        span.setAttribute('llm.response_type', 'simulated');
        const stream = this.createSimulatedStream();
        recordSpanTiming(span, 'llm.completion', startTime);
        return stream;
      }

      // Always use OpenRouter
      span.setAttribute('llm.router', 'openrouter');
      this.log.info('Using OpenRouter for streaming completion');

      const openRouterClient = this.getOpenRouterClient();
      const completionOptions = this.configureCompletionOptions(options);

      // Add runtime completion options to telemetry
      span.setAttributes({
        'llm.max_tokens': completionOptions.maxTokens || 0,
        'llm.temperature': completionOptions.temperature || 0,
        'llm.system_prompt_enabled': !!completionOptions.systemPrompt,
        'llm.use_web_search': options.useWebSearch || false,
        'llm.use_image_generation': options.useImageGeneration || false,
      });

      const stream = await openRouterClient.generateStreamingCompletion(
        messages,
        completionOptions
      );

      recordSpanTiming(span, 'llm.completion', startTime);
      span.setAttribute('llm.success', true);

      return stream;
    } catch (error) {
      span.recordException(error as Error);
      span.setAttribute('llm.success', false);
      span.setAttribute('llm.error_type', 'openrouter_error');
      throw error;
    } finally {
      span.end();
    }
  }

  private configureCompletionOptions(
    options: CompletionOptions
  ): CompletionOptions {
    const envMaxTokens = process.env.MAX_TOKENS
      ? parseInt(process.env.MAX_TOKENS)
      : undefined;

    const optionsMaxTokens = options.model.max_tokens;

    const maxTokens =
      optionsMaxTokens || envMaxTokens || DEFAULT_CONFIG.defaultMaxTokens;

    const temperature = process.env.TEMPERATURE
      ? parseFloat(process.env.TEMPERATURE)
      : DEFAULT_CONFIG.defaultTemperature;

    // Use the provided system prompt, or default to Sabi system prompt
    const systemPrompt =
      process.env.DISABLE_SYSTEM_PROMPT !== 'true'
        ? options.systemPrompt || processSystemPrompt(SABI_SYSTEM_PROMPT)
        : undefined;

    return {
      ...options,
      temperature,
      maxTokens,
      systemPrompt,
    };
  }

  // delay before first chunk is sent
  private createSimulatedStream(): ReadableStream {
    // Maintain references to timers so they can be cleared from both `start` and `cancel`.
    let timeoutId: NodeJS.Timeout | null = null;
    let intervalId: NodeJS.Timeout | null = null;
    let isControllerClosed = false;

    // Centralized cleanup helper
    const cleanup = () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }
      if (intervalId) {
        clearInterval(intervalId);
        intervalId = null;
      }
    };

    return new ReadableStream({
      start(controller) {
        try {
          // Start streaming immediately with JSON-formatted chunks to match real API
          const chunks = simulatedResponse.split(' ');
          let index = 0;

          // Send initial delay, then start streaming
          timeoutId = setTimeout(() => {
            if (isControllerClosed) return;

            intervalId = setInterval(() => {
              if (isControllerClosed) {
                cleanup();
                return;
              }

              try {
                if (index < chunks.length) {
                  // Format as JSON to match real streaming API
                  const deltaChunk =
                    JSON.stringify({
                      type: 'delta',
                      content: chunks[index] + ' ',
                    }) + '\n';

                  controller.enqueue(new TextEncoder().encode(deltaChunk));
                  index++;
                } else {
                  // Send completion signal
                  const doneChunk =
                    JSON.stringify({
                      type: 'done',
                    }) + '\n';
                  controller.enqueue(new TextEncoder().encode(doneChunk));

                  cleanup();
                  if (!isControllerClosed) {
                    controller.close();
                    isControllerClosed = true;
                  }
                }
              } catch (error) {
                cleanup();
                if (!isControllerClosed) {
                  controller.error(error);
                  isControllerClosed = true;
                }
              }
            }, DEFAULT_CONFIG.simulationDelayMs);
          }, DEFAULT_CONFIG.simulationInitialDelayMs);
        } catch (error) {
          cleanup();
          if (!isControllerClosed) {
            controller.error(error);
            isControllerClosed = true;
          }
        }
      },
      cancel() {
        // Ensure any active timers are cleared when the consumer cancels the stream.
        cleanup();
        isControllerClosed = true;
      },
    });
  }

  async countTokens(text: string): Promise<number> {
    // With OpenRouter, we can't reliably count tokens in advance
    // Use a simple approximation (4 characters ≈ 1 token as a rough estimate)
    this.log.warn('Token counting with OpenRouter is approximate');
    return Math.ceil(text.length / 4);
  }

  async getModels(): Promise<{ id: string; name: string }[]> {
    // With OpenRouter, models are managed through the database/API
    // This method is kept for compatibility but should use database queries
    throw new AppError(
      'Model listing should use database queries with OpenRouter',
      ErrorCode.AI_STREAMING,
      500
    );
  }

  getAvailableProviders(): string[] {
    return ['openrouter'];
  }

  async getProvidersAndModels(): Promise<
    {
      id: string;
      name: string;
      models: { id: string; name: string }[];
    }[]
  > {
    // With OpenRouter, providers and models are managed through the database
    // This method is kept for compatibility but should use database queries
    return [
      {
        id: 'openrouter',
        name: 'OpenRouter',
        models: [],
      },
    ];
  }
}
