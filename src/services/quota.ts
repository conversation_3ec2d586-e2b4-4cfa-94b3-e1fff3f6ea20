import { createClient } from '@/utils/supabase/server';
import { logger } from '@/lib/logger';
import { AppError, ErrorCode } from '@/lib/error';
import * as crypto from 'crypto';
import {
  SubscriptionPlan,
  SUBSCRIPTION_PLANS,
  SubscriptionData,
} from '@/lib/supabase/types';
import { User } from '@supabase/supabase-js';

// Constants for quota limits
const QUOTA_CONSTANTS = {
  FREE_DAILY_TOKENS: 10000,
  FREE_MAX_IMAGE_SIZE: '512x512' as const,
  WARNING_THRESHOLD_80: 80,
  WARNING_THRESHOLD_95: 95,
} as const;

export interface QuotaUsage {
  imagesUsed: number;
  imagesRemaining: number;
  maxImages: number;
  resetDate: string;
  purchasedCredits: number; // Add visibility into purchased balance
}

export interface ComparisonQuotaUsage {
  comparisonsUsed: number;
  comparisonsRemaining: number;
  maxComparisons: number;
  resetDate: string;
}

export interface TokenQuotaStatus {
  canUse: boolean;
  tokensUsed: number;
  tokensRemaining: number;
  tokenQuota: number | null;
  purchasedTokens: number; // Add visibility into purchased balance
  resetDate: string;
}

export interface ModelRecommendation {
  modelId: string | null;
  reason: string;
  options?: string[];
}

export interface PersistentBalance {
  tokensAvailable: number;
  tokensPurchasedTotal: number;
  tokensUsedTotal: number;
  creditsAvailable: number;
  creditsPurchasedTotal: number;
  creditsUsedTotal: number;
}

export class QuotaService {
  private static instance: QuotaService;
  private log = logger.child({ service: 'QuotaService' });
  // NOTE: We no longer keep token reservations in memory because that breaks in
  // multi-process / server-restart scenarios.  Reservations are now persisted
  // to Postgres via the `token_reservations` table so we always have an
  // authoritative record that survives restarts and scales across workers.

  private constructor() {}

  public static getInstance(): QuotaService {
    if (!QuotaService.instance) {
      QuotaService.instance = new QuotaService();
    }
    return QuotaService.instance;
  }

  private getTodayDateString(): string {
    // Use UTC to ensure consistent date keys across timezones
    return new Date().toISOString().split('T')[0];
  }

  /**
   * Get effective subscription plan - CRITICAL FIX for subscription status
   * Inactive/past_due subscriptions are treated as free tier
   */
  async getEffectiveSubscriptionPlan(
    userId: string
  ): Promise<SubscriptionPlan> {
    try {
      const subscription = await this.getSubscription(userId);

      // CRITICAL: Only active subscriptions get paid benefits
      if (subscription.status !== 'active') {
        this.log.info('User has inactive subscription, treating as free', {
          userId,
          plan: subscription.plan,
          status: subscription.status,
        });
        return 'free';
      }

      return subscription.plan;
    } catch (error) {
      this.log.warn('Failed to get user subscription, defaulting to free', {
        userId,
        error,
      });
      return 'free';
    }
  }

  /**
   * Get user's persistent balance (non-expiring purchases)
   * Replaces the problematic monthly purchase tracking
   */
  async getPersistentBalance(userId: string): Promise<PersistentBalance> {
    try {
      const supabase = await createClient();

      const { data, error } = await supabase.rpc(
        'get_user_persistent_balance',
        {
          p_user_id: userId,
        }
      );

      if (error) {
        this.log.error(
          { err: error, userId },
          'Error fetching persistent balance'
        );
        throw new AppError('Failed to fetch balance', ErrorCode.INTERNAL, 500);
      }

      const balance = data?.[0];
      if (!balance) {
        // No balance record = user has no purchases
        return {
          tokensAvailable: 0,
          tokensPurchasedTotal: 0,
          tokensUsedTotal: 0,
          creditsAvailable: 0,
          creditsPurchasedTotal: 0,
          creditsUsedTotal: 0,
        };
      }

      return {
        tokensAvailable: balance.tokens_available || 0,
        tokensPurchasedTotal: balance.tokens_purchased_total || 0,
        tokensUsedTotal: balance.tokens_used_total || 0,
        creditsAvailable: balance.credits_available || 0,
        creditsPurchasedTotal: balance.credits_purchased_total || 0,
        creditsUsedTotal: balance.credits_used_total || 0,
      };
    } catch (error) {
      if (error instanceof AppError) throw error;

      this.log.error(
        { err: error, userId },
        'Error fetching persistent balance'
      );
      throw new AppError('Failed to fetch balance', ErrorCode.INTERNAL, 500);
    }
  }

  /**
   * FIXED: Image quota check with proper subscription status enforcement
   */
  async checkImageQuota(
    user: User,
    imageSize: '512x512' | '768x768' | '1024x1024'
  ): Promise<{ allowed: boolean; usage: QuotaUsage; reason?: string }> {
    try {
      // Get effective subscription plan (fixes inactive subscription bug)
      const effectivePlan = await this.getEffectiveSubscriptionPlan(user.id);

      // Check if requested image size is allowed for user's plan
      if (
        effectivePlan === 'free' &&
        imageSize !== QUOTA_CONSTANTS.FREE_MAX_IMAGE_SIZE
      ) {
        const usage = await this.getQuotaUsage(user.id);
        return {
          allowed: false,
          usage,
          reason: `Image size ${imageSize} not available for free plan. Maximum allowed: ${QUOTA_CONSTANTS.FREE_MAX_IMAGE_SIZE}`,
        };
      }

      // Get current usage and balances
      const usage = await this.getQuotaUsage(user.id);
      const canGenerate = usage.imagesRemaining > 0;

      if (!canGenerate) {
        return {
          allowed: false,
          usage,
          reason: `Monthly image limit of ${usage.maxImages} reached`,
        };
      }

      return {
        allowed: true,
        usage,
      };
    } catch (error) {
      this.log.error(
        { err: error, userId: user.id },
        'Error checking image quota'
      );
      throw new AppError(
        'Failed to check image quota',
        ErrorCode.INTERNAL,
        500
      );
    }
  }

  /**
   * FIXED: Image usage increment with correct deduction order
   * CRITICAL FIX #4: Consume subscription quota FIRST, then purchased balance
   */
  async incrementImageUsage(userId: string): Promise<void> {
    try {
      const supabase = await createClient();
      const effectivePlan = await this.getEffectiveSubscriptionPlan(userId);

      if (effectivePlan === 'free') {
        // FIXED: Free tier now uses atomic quota checking to prevent race conditions
        const yearMonth = this.getCurrentYearMonth();
        const freeQuota = SUBSCRIPTION_PLANS.free.imageCreditsMonthly; // 25 credits

        const { data: quotaResult, error: quotaError } = await supabase.rpc(
          'atomic_image_check_and_increment_safe',
          {
            p_user_id: userId,
            p_credits: 1,
            p_year_month: yearMonth,
            p_quota_limit: freeQuota,
          }
        );

        if (quotaError) {
          this.log.error(
            {
              err: quotaError,
              userId,
            },
            'Error with atomic image quota operation (free tier)'
          );
          throw new AppError(
            'Failed to check image quota',
            ErrorCode.INTERNAL,
            500
          );
        }

        const quotaUsed = quotaResult?.[0]?.success;
        if (!quotaUsed) {
          // Free tier quota exhausted - hard limit with upgrade CTA
          throw new AppError(
            'Monthly image limit reached (25 credits). Upgrade to Starter for 400 credits/month.',
            ErrorCode.QUOTA_EXCEEDED,
            429
          );
        }
      } else {
        // FIXED: Paid tier - try subscription quota FIRST, then purchased balance
        const yearMonth = this.getCurrentYearMonth();
        const baseQuota = SUBSCRIPTION_PLANS[effectivePlan].imageCreditsMonthly;

        // Try to use subscription quota first (atomic operation)
        const { data: quotaResult, error: quotaError } = await supabase.rpc(
          'atomic_image_check_and_increment_safe',
          {
            p_user_id: userId,
            p_credits: 1,
            p_year_month: yearMonth,
            p_quota_limit: baseQuota,
          }
        );

        if (quotaError) {
          this.log.error(
            {
              err: quotaError,
              userId,
            },
            'Error with atomic image quota operation'
          );
          throw new AppError(
            'Failed to check image quota',
            ErrorCode.INTERNAL,
            500
          );
        }

        const quotaUsed = quotaResult?.[0]?.success;

        if (!quotaUsed) {
          // Subscription quota exhausted, try purchased balance
          const { data: balanceResult, error: balanceError } =
            await supabase.rpc('use_persistent_credit_balance', {
              p_user_id: userId,
              p_credits: 1,
            });

          if (balanceError) {
            this.log.error(
              {
                err: balanceError,
                userId,
              },
              'Error using persistent credit balance'
            );
            throw new AppError(
              'Failed to use credit balance',
              ErrorCode.INTERNAL,
              500
            );
          }

          const balanceUsed = balanceResult?.[0]?.success;
          if (!balanceUsed) {
            // Both subscription quota and purchased balance exhausted
            throw new AppError(
              'Image quota exhausted. No credits remaining.',
              ErrorCode.QUOTA_EXCEEDED,
              429
            );
          }

          this.log.info('Image usage charged to purchased balance', { userId });
        } else {
          this.log.info('Image usage charged to subscription quota', {
            userId,
          });
        }
      }

      this.log.info('Image usage incremented', { userId });
    } catch (error) {
      if (error instanceof AppError) throw error;

      this.log.error({ err: error, userId }, 'Error incrementing image usage');
      throw new AppError(
        'Failed to update image usage',
        ErrorCode.INTERNAL,
        500
      );
    }
  }

  /**
   * FIXED: Quota usage calculation with persistent balance integration
   */
  async getQuotaUsage(userId: string): Promise<QuotaUsage> {
    try {
      const supabase = await createClient();
      const effectivePlan = await this.getEffectiveSubscriptionPlan(userId);
      const yearMonth = this.getCurrentYearMonth();

      // Get monthly usage
      const { data: usage } = await supabase
        .from('user_monthly_usage')
        .select('image_credits_used')
        .eq('user_id', userId)
        .eq('year_month', yearMonth)
        .single();

      // Get persistent balance
      const persistentBalance = await this.getPersistentBalance(userId);

      // Calculate total quota = subscription base + purchased credits
      const baseQuota = SUBSCRIPTION_PLANS[effectivePlan].imageCreditsMonthly;
      const totalQuota = baseQuota + persistentBalance.creditsAvailable;

      // Calculate actual usage from subscription quota only
      const subscriptionUsage = Math.max(0, usage?.image_credits_used || 0);
      const creditsRemaining = Math.max(0, totalQuota - subscriptionUsage);

      // Check usage thresholds for warnings
      await this.checkUsageThresholds(
        userId,
        'images',
        subscriptionUsage,
        baseQuota,
        'monthly'
      );

      return {
        imagesUsed: subscriptionUsage,
        imagesRemaining: creditsRemaining,
        maxImages: totalQuota,
        purchasedCredits: persistentBalance.creditsAvailable,
        resetDate: this.getMonthlyResetDate(),
      };
    } catch (error) {
      this.log.error({ err: error, userId }, 'Error getting quota usage');
      throw new AppError('Failed to get quota usage', ErrorCode.INTERNAL, 500);
    }
  }

  private getCurrentYearMonth(): string {
    return new Date().toISOString().slice(0, 7); // '2024-03' format
  }

  private getMonthlyResetDate(): string {
    const now = new Date();
    const nextMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1);
    return nextMonth.toISOString().split('T')[0];
  }

  async getSubscription(userId: string): Promise<SubscriptionData> {
    const supabase = await createClient();
    const { data: subscription } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', userId)
      .single();

    return subscription || { plan: 'free', status: 'active' };
  }

  async getMonthlyTokenUsage(userId: string) {
    const supabase = await createClient();
    const yearMonth = this.getCurrentYearMonth();

    const { data: usage } = await supabase
      .from('user_monthly_usage')
      .select('*')
      .eq('user_id', userId)
      .eq('year_month', yearMonth)
      .single();

    return (
      usage || { tokens_used: 0, image_credits_used: 0, comparison_count: 0 }
    );
  }

  /**
   * FIXED: Token quota check with proper subscription status and atomic operations
   */
  async checkTokenQuota(userId: string): Promise<TokenQuotaStatus> {
    try {
      const effectivePlan = await this.getEffectiveSubscriptionPlan(userId);

      if (effectivePlan === 'free') {
        // Free tier uses daily token limits (10K/day)
        const result = await this.checkDailyTokenQuota(userId);
        await this.checkUsageThresholds(
          userId,
          'tokens',
          result.tokensUsed,
          result.tokenQuota || 0,
          'daily'
        );
        return {
          ...result,
          purchasedTokens: 0, // Free tier has no purchased tokens
        };
      }

      // Paid tier: combine subscription quota + purchased balance
      const usage = await this.getMonthlyTokenUsage(userId);
      const persistentBalance = await this.getPersistentBalance(userId);

      const baseQuota = SUBSCRIPTION_PLANS[effectivePlan].tokenQuota || 0;
      const totalQuota = baseQuota + persistentBalance.tokensAvailable;

      // Calculate actual usage from subscription quota only
      const subscriptionUsage = Math.max(0, usage.tokens_used || 0);
      const tokensRemaining = Math.max(0, totalQuota - subscriptionUsage);

      // Check usage thresholds for warnings
      await this.checkUsageThresholds(
        userId,
        'tokens',
        subscriptionUsage,
        baseQuota,
        'monthly'
      );

      return {
        canUse: tokensRemaining > 0,
        tokensUsed: subscriptionUsage,
        tokensRemaining,
        tokenQuota: totalQuota,
        purchasedTokens: persistentBalance.tokensAvailable,
        resetDate: this.getMonthlyResetDate(),
      };
    } catch (error) {
      this.log.error({ err: error, userId }, 'Error checking token quota');
      throw new AppError(
        'Failed to check token quota',
        ErrorCode.INTERNAL,
        500
      );
    }
  }

  async checkDailyTokenQuota(userId: string): Promise<TokenQuotaStatus> {
    try {
      const supabase = await createClient();
      const today = this.getTodayDateString();

      // Get current daily token usage for Free plan
      const { data: dailyUsage } = await supabase
        .from('user_daily_usage')
        .select('tokens_used')
        .eq('user_id', userId)
        .eq('date', today)
        .single();

      const tokensUsed = dailyUsage?.tokens_used || 0;
      const dailyQuota = QUOTA_CONSTANTS.FREE_DAILY_TOKENS;
      const tokensRemaining = Math.max(0, dailyQuota - tokensUsed);

      return {
        canUse: tokensUsed < dailyQuota,
        tokensUsed,
        tokensRemaining,
        tokenQuota: dailyQuota,
        purchasedTokens: 0, // Free tier has no purchased tokens
        resetDate: this.getTomorrowDateString(),
      };
    } catch (error) {
      this.log.error(
        { err: error, userId },
        'Error checking daily token quota'
      );
      throw new AppError(
        'Failed to check daily token quota',
        ErrorCode.INTERNAL,
        500
      );
    }
  }

  private getTomorrowDateString(): string {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    return tomorrow.toISOString().split('T')[0];
  }

  /**
   * FIXED: Token usage tracking with correct deduction order
   * CRITICAL FIX #4: Consume subscription quota FIRST, then purchased balance
   */
  async trackTokenUsage(
    userId: string,
    tokens: number,
    modelId: string
  ): Promise<void> {
    try {
      const effectivePlan = await this.getEffectiveSubscriptionPlan(userId);

      if (effectivePlan === 'free') {
        // Free plan uses daily token tracking
        await this.trackDailyTokenUsage(userId, tokens);
      } else {
        // FIXED: Paid tier - try subscription quota FIRST, then purchased balance
        const supabase = await createClient();
        const yearMonth = this.getCurrentYearMonth();
        const baseQuota = SUBSCRIPTION_PLANS[effectivePlan].tokenQuota || 0;

        // Try to use subscription quota first (atomic operation)
        const { data: quotaResult, error: quotaError } = await supabase.rpc(
          'atomic_token_check_and_increment_safe',
          {
            p_user_id: userId,
            p_tokens: tokens,
            p_year_month: yearMonth,
            p_quota_limit: baseQuota,
          }
        );

        if (quotaError) {
          this.log.error(
            {
              err: quotaError,
              userId,
              tokens,
            },
            'Error with atomic token quota operation'
          );
          throw new AppError(
            'Failed to check token quota',
            ErrorCode.INTERNAL,
            500
          );
        }

        const quotaUsed = quotaResult?.[0]?.success;

        if (!quotaUsed) {
          // Subscription quota exhausted, try purchased balance
          const { data: balanceResult, error: balanceError } =
            await supabase.rpc('use_persistent_token_balance', {
              p_user_id: userId,
              p_tokens: tokens,
            });

          if (balanceError) {
            this.log.error(
              {
                err: balanceError,
                userId,
              },
              'Error using persistent token balance'
            );
            throw new AppError(
              'Failed to use token balance',
              ErrorCode.INTERNAL,
              500
            );
          }

          const balanceUsed = balanceResult?.[0]?.success;
          if (!balanceUsed) {
            // Both subscription quota and purchased balance exhausted
            const remaining = quotaResult?.[0]?.remaining || 0;
            throw new AppError(
              `Token quota exceeded. ${remaining} tokens remaining.`,
              ErrorCode.QUOTA_EXCEEDED,
              429
            );
          }

          this.log.info('Token usage charged to purchased balance', {
            userId,
            tokens,
            modelId,
          });
        } else {
          this.log.info('Token usage charged to subscription quota', {
            userId,
            tokens,
            modelId,
          });
        }
      }
    } catch (error) {
      if (error instanceof AppError) throw error;

      this.log.error(
        { err: error, userId, tokens },
        'Error tracking token usage'
      );
      throw new AppError(
        'Failed to track token usage',
        ErrorCode.INTERNAL,
        500
      );
    }
  }

  async trackDailyTokenUsage(userId: string, tokens: number): Promise<void> {
    try {
      const supabase = await createClient();
      const today = this.getTodayDateString();

      // Use new atomic function with quota checking for Free plan
      const { data: result, error } = await supabase.rpc(
        'increment_daily_token_usage_with_limit',
        {
          p_user_id: userId,
          p_date: today,
          p_tokens: tokens,
          p_daily_limit: QUOTA_CONSTANTS.FREE_DAILY_TOKENS,
        }
      );

      if (error) {
        this.log.error(
          {
            err: error,
            userId,
            tokens,
          },
          'Error tracking daily token usage'
        );
        throw new AppError(
          'Failed to track daily token usage',
          ErrorCode.INTERNAL,
          500
        );
      }

      const success = result?.[0]?.success;
      if (!success) {
        const remaining = result?.[0]?.remaining || 0;
        throw new AppError(
          `Daily token quota exceeded. ${remaining} tokens remaining.`,
          ErrorCode.QUOTA_EXCEEDED,
          429
        );
      }

      this.log.info('Daily token usage tracked', {
        userId,
        tokens,
        date: today,
        remaining: result?.[0]?.remaining,
      });
    } catch (error) {
      if (error instanceof AppError) throw error;

      this.log.error(
        {
          err: error,
          userId,
          tokens,
        },
        'Error tracking daily token usage'
      );
      throw new AppError(
        'Failed to track daily token usage',
        ErrorCode.INTERNAL,
        500
      );
    }
  }

  async handleTokenOverage(userId: string): Promise<ModelRecommendation> {
    try {
      const effectivePlan = await this.getEffectiveSubscriptionPlan(userId);

      if (effectivePlan === 'starter') {
        // Fallback to GPT-4o-mini for starter tier
        return {
          modelId: 'gpt-4o-mini',
          reason: 'quota_exceeded',
        };
      }

      if (effectivePlan === 'premium') {
        // Offer auto-purchase or manual top-up
        return {
          modelId: null,
          reason: 'overage_available',
          options: ['auto_purchase_100k', 'manual_topup'],
        };
      }

      return {
        modelId: null,
        reason: 'upgrade_required',
      };
    } catch (error) {
      this.log.error({ err: error, userId }, 'Error handling token overage');
      throw new AppError(
        'Failed to handle token overage',
        ErrorCode.INTERNAL,
        500
      );
    }
  }

  /**
   * FIXED: Comparison quota with proper subscription status enforcement
   */
  async checkComparisonQuota(
    userId: string
  ): Promise<{ canUse: boolean; count: number; limit: number }> {
    try {
      const effectivePlan = await this.getEffectiveSubscriptionPlan(userId);
      const planConfig = SUBSCRIPTION_PLANS[effectivePlan];
      const dailyLimit = planConfig.dailyComparisonLimit || 0;

      if (!planConfig.allowsComparison || dailyLimit === 0) {
        return { canUse: false, count: 0, limit: 0 };
      }

      const supabase = await createClient();
      const today = this.getTodayDateString();

      // Get current daily comparison usage
      const { data: dailyUsage } = await supabase
        .from('user_daily_usage')
        .select('comparison_count')
        .eq('user_id', userId)
        .eq('date', today)
        .single();

      const comparisonCount = dailyUsage?.comparison_count || 0;
      const limit = dailyLimit;

      return {
        canUse: comparisonCount < limit,
        count: comparisonCount,
        limit,
      };
    } catch (error) {
      this.log.error({ err: error, userId }, 'Error checking comparison quota');
      throw new AppError(
        'Failed to check comparison quota',
        ErrorCode.INTERNAL,
        500
      );
    }
  }

  async incrementComparisonUsage(userId: string): Promise<void> {
    try {
      const supabase = await createClient();
      const today = this.getTodayDateString();
      const effectivePlan = await this.getEffectiveSubscriptionPlan(userId);
      const planConfig = SUBSCRIPTION_PLANS[effectivePlan];
      const dailyLimit = planConfig.dailyComparisonLimit || 0;

      // FIXED: Use atomic function to prevent race conditions
      const { data, error } = await supabase.rpc(
        'atomic_comparison_check_and_increment_safe',
        {
          p_user_id: userId,
          p_date: today,
          p_daily_limit: dailyLimit,
        }
      );

      if (error) {
        this.log.error(
          {
            err: error,
            userId,
          },
          'Error with atomic comparison quota operation'
        );
        throw new AppError(
          'Failed to track comparison usage',
          ErrorCode.INTERNAL,
          500
        );
      }

      const success = data?.[0]?.success;
      if (!success) {
        const remaining = data?.[0]?.remaining || 0;
        throw new AppError(
          `Daily comparison limit reached (${dailyLimit}). ${remaining} remaining.`,
          ErrorCode.QUOTA_EXCEEDED,
          429
        );
      }

      this.log.info('Comparison usage incremented atomically', {
        userId,
        date: today,
        remaining: data?.[0]?.remaining,
        dailyLimit,
      });
    } catch (error) {
      if (error instanceof AppError) throw error;

      this.log.error(
        { err: error, userId },
        'Error incrementing comparison usage'
      );
      throw new AppError(
        'Failed to track comparison usage',
        ErrorCode.INTERNAL,
        500
      );
    }
  }

  async decrementComparisonUsage(userId: string): Promise<void> {
    try {
      const supabase = await createClient();
      const today = this.getTodayDateString();

      const { data, error } = await supabase.rpc(
        'decrement_user_comparison_count',
        {
          p_user_id: userId,
          p_date: today,
        }
      );

      if (error) {
        this.log.error(
          {
            err: error,
            userId,
          },
          'Error decrementing comparison usage'
        );
        throw new AppError(
          'Failed to refund comparison usage',
          ErrorCode.INTERNAL,
          500
        );
      }

      if (!data || !data[0]?.success) {
        this.log.warn('No rows affected when decrementing comparison usage', {
          userId,
          date: today,
          data,
        });
        throw new AppError(
          'Failed to refund comparison usage - no rows affected',
          ErrorCode.INTERNAL,
          500
        );
      }

      this.log.info('Comparison usage decremented (refunded)', {
        userId,
        date: today,
      });
    } catch (error) {
      if (error instanceof AppError) throw error;

      this.log.error(
        { err: error, userId },
        'Error decrementing comparison usage'
      );
      throw new AppError(
        'Failed to refund comparison usage',
        ErrorCode.INTERNAL,
        500
      );
    }
  }

  async getComparisonQuotaUsage(userId: string): Promise<ComparisonQuotaUsage> {
    try {
      const effectivePlan = await this.getEffectiveSubscriptionPlan(userId);
      const planConfig = SUBSCRIPTION_PLANS[effectivePlan];
      const dailyLimit = planConfig.dailyComparisonLimit || 0;

      if (!planConfig.allowsComparison || dailyLimit === 0) {
        return {
          comparisonsUsed: 0,
          comparisonsRemaining: 0,
          maxComparisons: 0,
          resetDate: this.getTodayDateString(),
        };
      }

      const supabase = await createClient();
      const today = this.getTodayDateString();

      // Get current daily comparison usage
      const { data: dailyUsage } = await supabase
        .from('user_daily_usage')
        .select('comparison_count')
        .eq('user_id', userId)
        .eq('date', today)
        .single();

      const currentComparisonCount = dailyUsage?.comparison_count || 0;
      const maxComparisons = dailyLimit;
      const comparisonsRemaining = Math.max(
        0,
        maxComparisons - currentComparisonCount
      );

      return {
        comparisonsUsed: currentComparisonCount,
        comparisonsRemaining,
        maxComparisons,
        resetDate: this.getTomorrowDateString(),
      };
    } catch (error) {
      this.log.error(
        { err: error, userId },
        'Error getting comparison quota usage'
      );
      throw new AppError(
        'Failed to get comparison quota usage',
        ErrorCode.INTERNAL,
        500
      );
    }
  }

  /**
   * Decrement image usage (for refunds/failures)
   */
  async decrementImageUsage(userId: string): Promise<void> {
    try {
      const supabase = await createClient();
      const yearMonth = this.getCurrentYearMonth();

      // Decrement monthly image usage (consistent with increment)
      const { error } = await supabase.rpc('decrement_monthly_image_usage', {
        p_user_id: userId,
        p_year_month: yearMonth,
        p_credits: 1,
      });

      if (error) {
        this.log.error(
          { err: error, userId },
          'Error decrementing image usage'
        );
        throw new AppError(
          'Failed to refund image usage',
          ErrorCode.INTERNAL,
          500
        );
      }

      this.log.info('Image usage decremented (refunded)', {
        userId,
        yearMonth,
      });
    } catch (error) {
      if (error instanceof AppError) throw error;

      this.log.error({ err: error, userId }, 'Error decrementing image usage');
      throw new AppError(
        'Failed to refund image usage',
        ErrorCode.INTERNAL,
        500
      );
    }
  }

  /**
   * Reserve tokens upfront before LLM request (reserve-and-commit pattern)
   * This prevents double-charging and enables proper refunds on failure
   */
  async reserveTokenUsage(
    userId: string,
    tokens: number,
    modelId: string
  ): Promise<{ reservationId: string; success: boolean }> {
    try {
      // Ensure the user still has quota BEFORE creating the reservation. We
      // only *check* here; tokens will be deducted on commit. This mirrors the
      // previous behaviour but keeps a durable audit trail.
      const quotaStatus = await this.checkTokenQuota(userId);
      if (
        quotaStatus.tokenQuota !== null &&
        tokens > quotaStatus.tokensRemaining
      ) {
        this.log.warn('Token reservation failed – insufficient balance', {
          userId,
          requested: tokens,
          remaining: quotaStatus.tokensRemaining,
        });
        return { reservationId: '', success: false };
      }

      const reservationId = `reserve_${Date.now()}_${crypto.randomUUID()}`;
      const supabase = await createClient();
      // Set reservation to expire in 30 minutes to avoid dangling locks
      const expiresAt = new Date(Date.now() + 30 * 60 * 1000).toISOString();

      const { error } = await supabase.from('token_reservations').insert({
        reservation_id: reservationId,
        user_id: userId,
        model_id: modelId,
        tokens_reserved: tokens, // maintain legacy column for backward compatibility
        estimated_tokens: tokens, // NEW: satisfy NOT NULL constraint in DB schema
        expires_at: expiresAt, // NEW: satisfy NOT NULL constraint in DB schema
        status: 'pending',
        created_at: new Date().toISOString(),
      });

      if (error) {
        this.log.error(
          {
            err: error,
            userId,
            modelId,
            tokens,
          },
          'Failed to persist token reservation'
        );
        return { reservationId: '', success: false };
      }

      this.log.info('Tokens reserved (pending commit)', {
        userId,
        tokens,
        modelId,
        reservationId,
      });

      return { reservationId, success: true };
    } catch (error) {
      this.log.error(
        {
          err: error,
          userId,
          tokens,
        },
        'Failed to create token reservation'
      );
      return { reservationId: '', success: false };
    }
  }

  /**
   * Commit token reservation (successful LLM request)
   * FIXED: Now properly reconciles estimated vs actual token usage
   */
  async commitTokenReservation(
    reservationId: string,
    actualTokensUsed?: number
  ): Promise<void> {
    try {
      const supabase = await createClient();

      // Fetch the reservation so we know who to bill.
      const { data: reservation, error: fetchError } = await supabase
        .from('token_reservations')
        .select('*')
        .eq('reservation_id', reservationId)
        .single();

      if (fetchError || !reservation) {
        this.log.warn(
          'Reservation not found when committing; skipping charge',
          {
            reservationId,
            fetchError,
          }
        );
        return;
      }

      if (reservation.status !== 'pending') {
        this.log.warn('Reservation already processed, ignoring commit', {
          reservationId,
          status: reservation.status,
        });
        return;
      }

      const tokensToCharge = actualTokensUsed ?? reservation.tokens_reserved;

      // Bill the user – this may throw (e.g. quota exceeded)
      await this.trackTokenUsage(
        reservation.user_id,
        tokensToCharge,
        reservation.model_id
      );

      // Mark reservation as committed
      const { error: updateError } = await supabase
        .from('token_reservations')
        .update({
          status: 'committed',
          actual_tokens: tokensToCharge,
          committed_at: new Date().toISOString(),
        })
        .eq('reservation_id', reservationId);

      if (updateError) {
        this.log.error(
          {
            err: updateError,
            reservationId,
          },
          'Failed to update reservation status to committed'
        );
        // IMPORTANT: Surface the failure so callers can decide to retry
        throw new AppError(
          'Failed to mark reservation committed',
          ErrorCode.INTERNAL,
          500
        );
      }

      this.log.info('Token reservation committed', {
        reservationId,
        userId: reservation.user_id,
        modelId: reservation.model_id,
        tokensCharged: tokensToCharge,
      });
    } catch (error) {
      try {
        const supabase = await createClient();
        await supabase
          .from('token_reservations')
          .update({
            status: 'failed',
            error: String(error),
            updated_at: new Date().toISOString(),
          })
          .eq('reservation_id', reservationId);
      } catch (updateErr) {
        this.log.error(
          {
            err: updateErr,
            reservationId,
          },
          'Failed to mark reservation as failed'
        );
      }

      this.log.error(
        {
          err: error,
          reservationId,
          actualTokensUsed,
        },
        'Error committing token reservation'
      );
      throw error;
    }
  }

  /**
   * Cancel a token reservation – used when the LLM request fails before completion.
   * No tokens are deducted; the reservation is simply discarded.
   */
  async cancelTokenReservation(reservationId: string): Promise<void> {
    try {
      const supabase = await createClient();
      const { error } = await supabase
        .from('token_reservations')
        .update({ status: 'cancelled', cancelled_at: new Date().toISOString() })
        .eq('reservation_id', reservationId)
        .eq('status', 'pending'); // only pending reservations can be cancelled

      if (error) {
        this.log.error(
          {
            err: error,
            reservationId,
          },
          'Failed to cancel token reservation'
        );
        throw new AppError(
          'Failed to cancel token reservation',
          ErrorCode.INTERNAL,
          500
        );
      }

      this.log.info('Token reservation cancelled – no tokens charged', {
        reservationId,
      });
    } catch (error) {
      this.log.error(
        {
          err: error,
          reservationId,
        },
        'Error cancelling token reservation'
      );
      throw error;
    }
  }

  /**
   * Refund tokens when LLM requests fail (rollback mechanism)
   * IMPORTANT: Only call this if tokens were pre-charged via trackTokenUsage
   */
  async refundTokenUsage(
    userId: string,
    tokens: number,
    reason: string = 'LLM request failed'
  ): Promise<void> {
    try {
      const supabase = await createClient();
      const effectivePlan = await this.getEffectiveSubscriptionPlan(userId);

      if (effectivePlan === 'free') {
        // Free plan uses daily token tracking
        const today = this.getTodayDateString();

        const { error } = await supabase.rpc('decrement_daily_token_usage', {
          p_user_id: userId,
          p_date: today,
          p_tokens: tokens,
        });

        if (error) {
          this.log.error(
            {
              err: error,
              userId,
              tokens,
            },
            'Error refunding daily token usage'
          );
          // Don't throw - refund failures shouldn't break the flow
        } else {
          this.log.info('Daily token usage refunded', {
            userId,
            tokens,
            reason,
            date: today,
          });
        }
      } else {
        // Paid tier: need to determine which pool to refund to
        // Try persistent balance first (most recent charges), then subscription
        const { data: balanceResult, error: balanceError } = await supabase.rpc(
          'refund_persistent_token_balance',
          {
            p_user_id: userId,
            p_tokens: tokens,
          }
        );

        if (balanceError || !balanceResult?.[0]?.success) {
          // No persistent balance to refund, try monthly subscription quota
          const yearMonth = this.getCurrentYearMonth();
          const { error: monthlyError } = await supabase.rpc(
            'decrement_monthly_token_usage',
            {
              p_user_id: userId,
              p_year_month: yearMonth,
              p_tokens: tokens,
            }
          );

          if (monthlyError) {
            this.log.error(
              {
                err: monthlyError,
                userId,
                tokens,
              },
              'Error refunding monthly token usage'
            );
          } else {
            this.log.info('Monthly token usage refunded', {
              userId,
              tokens,
              reason,
              yearMonth,
            });
          }
        } else {
          this.log.info('Persistent token balance refunded', {
            userId,
            tokens,
            reason,
          });
        }
      }
    } catch (error) {
      this.log.error(
        { err: error, userId, tokens },
        'Error refunding token usage'
      );
      // Don't throw - refund failures shouldn't break the flow
    }
  }

  async checkUsageThresholds(
    userId: string,
    quotaType: 'tokens' | 'images',
    used: number,
    total: number,
    period: 'daily' | 'monthly'
  ): Promise<void> {
    if (total === 0) return;

    const usagePercentage = (used / total) * 100;

    // Check for 80% threshold
    if (
      usagePercentage >= QUOTA_CONSTANTS.WARNING_THRESHOLD_80 &&
      usagePercentage < QUOTA_CONSTANTS.WARNING_THRESHOLD_95
    ) {
      await this.logUsageWarning(
        userId,
        quotaType,
        QUOTA_CONSTANTS.WARNING_THRESHOLD_80,
        usagePercentage,
        period
      );
    }

    // Check for 95% threshold
    if (usagePercentage >= QUOTA_CONSTANTS.WARNING_THRESHOLD_95) {
      await this.logUsageWarning(
        userId,
        quotaType,
        QUOTA_CONSTANTS.WARNING_THRESHOLD_95,
        usagePercentage,
        period
      );
    }
  }

  private async logUsageWarning(
    userId: string,
    quotaType: 'tokens' | 'images',
    threshold: number,
    actualPercentage: number,
    period: 'daily' | 'monthly'
  ): Promise<void> {
    try {
      this.log.warn('Usage threshold reached', {
        userId,
        quotaType,
        threshold: `${threshold}%`,
        actualPercentage: `${actualPercentage.toFixed(1)}%`,
        period,
      });

      // TODO: Wire this to email/in-app notification system
      // - Send email notifications
      // - Create in-app notifications
      // - Send to external monitoring services
      // For now, we're just logging the warning
    } catch (error) {
      this.log.error(
        {
          err: error,
          userId,
          quotaType,
          threshold,
        },
        'Error logging usage warning'
      );
    }
  }
}
