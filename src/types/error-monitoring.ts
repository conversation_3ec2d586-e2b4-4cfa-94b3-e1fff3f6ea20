/**
 * TypeScript type definitions for error monitoring services
 */

export interface ErrorMetadata {
  timestamp?: string;
  [key: string]: string | number | boolean | undefined;
}

export interface ErrorReportingOptions {
  tags?: Record<string, string>;
  extra?: Record<string, unknown>;
  context?: string;
  user?: {
    id?: string;
    email?: string;
    username?: string;
  };
}



export interface SentryOptions {
  tags?: Record<string, string>;
  extra?: Record<string, unknown>;
  contexts?: Record<string, unknown>;
  user?: {
    id?: string;
    email?: string;
    username?: string;
  };
}

// Environment variable types
declare global {
  interface ProcessEnv {
    APP_VERSION?: string;
    NEXT_PUBLIC_APP_VERSION?: string;
  }
}
