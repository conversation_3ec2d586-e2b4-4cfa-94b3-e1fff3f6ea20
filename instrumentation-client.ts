// This file configures the initialization of Sentry on the client.
// The config you add here will be used whenever a users loads a page in their browser.
// https://docs.sentry.io/platforms/javascript/guides/nextjs/

import * as Sentry from '@sentry/nextjs';
import { onCLS, onFCP, onINP, onLCP, onTTFB } from 'web-vitals';

export const onRouterTransitionStart = Sentry.captureRouterTransitionStart;

// Initialize web-vitals monitoring (vendor-agnostic)
function initWebVitals() {
  // Core Web Vitals
  onCLS((metric) => {
    Sentry.setMeasurement('CLS', metric.value, 'none');
    Sentry.addBreadcrumb({
      category: 'web-vitals',
      message: `CLS: ${metric.value}`,
      level: 'info',
      data: { metric },
    });
  });

  // Note: FID is deprecated in web-vitals v3+, replaced by INP
  onINP((metric) => {
    Sentry.setMeasurement('INP', metric.value, 'millisecond');
    Sentry.addBreadcrumb({
      category: 'web-vitals',
      message: `INP: ${metric.value}ms`,
      level: 'info',
      data: { metric },
    });
  });

  onLCP((metric) => {
    Sentry.setMeasurement('LCP', metric.value, 'millisecond');
    Sentry.addBreadcrumb({
      category: 'web-vitals',
      message: `LCP: ${metric.value}ms`,
      level: 'info',
      data: { metric },
    });
  });

  // Additional Performance Metrics
  onFCP((metric) => {
    Sentry.setMeasurement('FCP', metric.value, 'millisecond');
    Sentry.addBreadcrumb({
      category: 'web-vitals',
      message: `FCP: ${metric.value}ms`,
      level: 'info',
      data: { metric },
    });
  });

  onTTFB((metric) => {
    Sentry.setMeasurement('TTFB', metric.value, 'millisecond');
    Sentry.addBreadcrumb({
      category: 'web-vitals',
      message: `TTFB: ${metric.value}ms`,
      level: 'info',
      data: { metric },
    });
  });
}

// Only initialize Sentry in production environment
if (process.env.NODE_ENV === 'production') {
  Sentry.init({
    dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,

    // Enhanced integrations for comprehensive monitoring
    integrations: [
      Sentry.replayIntegration({
        // Capture more user interactions for better debugging
        maskAllText: false,
        maskAllInputs: true,
        blockAllMedia: true,
      }),
      Sentry.browserTracingIntegration({
        // Enhanced tracing for better performance monitoring
        markBackgroundSpan: true, // Fixed: was markBackgroundTransactions
        enableHTTPTimings: true,
        enableLongTask: true,
        enableInp: true,
      }),
    ],

    // Adjust sample rate for production - 10% for performance monitoring
    tracesSampleRate: 0.1,

    // Define how likely Replay events are sampled.
    // 1% for regular sessions to reduce quota usage
    replaysSessionSampleRate: 0.01,

    // 100% replay when an error occurs - this is critical for debugging
    replaysOnErrorSampleRate: 1.0,

    // Disable debug in production
    debug: false,

    // Set environment for better error tracking
    environment: process.env.NODE_ENV,

    // Add release information for better deployment tracking
    release:
      process.env.NEXT_PUBLIC_SENTRY_RELEASE ||
      process.env.VERCEL_GIT_COMMIT_SHA,

    // Enhanced error filtering
    beforeSend(event) {
      // Filter out noisy errors that aren't actionable
      if (event.exception) {
        const error = event.exception.values?.[0];
        if (error?.value?.includes('ResizeObserver loop limit exceeded')) {
          return null;
        }
        if (error?.value?.includes('Script error.')) {
          return null;
        }
        if (error?.value?.includes('Non-Error promise rejection captured')) {
          return null;
        }
        if (error?.value?.includes('Network request failed')) {
          return null;
        }
        if (error?.value?.includes('Load failed')) {
          return null;
        }
      }

      // Add context for better debugging
      if (event.tags) {
        event.tags.component = 'frontend';
        event.tags.runtime = 'browser';
      }

      return event;
    },

    // Enhanced transaction filtering for better performance data
    beforeSendTransaction(event) {
      // Filter out noisy transactions
      if (event.transaction?.includes('/_next/static')) {
        return null;
      }
      if (event.transaction?.includes('/favicon.ico')) {
        return null;
      }

      // Add context for better analysis
      if (event.tags) {
        event.tags.component = 'frontend';
        event.tags.runtime = 'browser';
      }

      return event;
    },

    // Set initial user context
    initialScope: {
      tags: {
        component: 'frontend',
        runtime: 'browser',
      },
    },
  });

  // Initialize web-vitals monitoring after Sentry is set up
  initWebVitals();
}
