/**
 * Feature Flags Usage Examples
 * 
 * This file demonstrates various ways to use the feature flag system
 * in the Sabi Chat application.
 */

import React from 'react';
import { useFeatureFlag, useFeatureFlags } from '@/hooks/useFeatureFlag';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

// Example 1: Simple feature toggle
export function ChatInterface() {
  const { isEnabled: isNewUIEnabled, isLoading } = useFeatureFlag('new-chat-ui');

  if (isLoading) {
    return <div className="animate-pulse">Loading chat interface...</div>;
  }

  return (
    <div>
      {isNewUIEnabled ? (
        <NewChatInterface />
      ) : (
        <LegacyChatInterface />
      )}
    </div>
  );
}

// Example 2: Multiple feature flags
export function Dashboard() {
  const { flags, isLoading, error } = useFeatureFlags([
    'advanced-analytics',
    'real-time-notifications',
    'export-data',
    'beta-features'
  ]);

  if (isLoading) {
    return <DashboardSkeleton />;
  }

  if (error) {
    console.error('Failed to load feature flags:', error);
    // Fallback to safe defaults
    return <BasicDashboard />;
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {/* Always visible */}
      <BasicStatsCard />
      
      {/* Conditionally visible based on flags */}
      {flags['advanced-analytics'] && <AdvancedAnalyticsCard />}
      {flags['real-time-notifications'] && <NotificationsPanel />}
      {flags['export-data'] && <ExportDataButton />}
      
      {/* Beta features section */}
      {flags['beta-features'] && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              Beta Features
              <Badge variant="secondary">Beta</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p>You have access to experimental features!</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

// Example 3: Feature flag with fallback behavior
export function SearchInterface() {
  const { isEnabled: hasAdvancedSearch } = useFeatureFlag('advanced-search');
  const { isEnabled: hasAISearch } = useFeatureFlag('ai-powered-search');

  return (
    <div className="search-container">
      {hasAISearch ? (
        <AISearchBox />
      ) : hasAdvancedSearch ? (
        <AdvancedSearchBox />
      ) : (
        <BasicSearchBox />
      )}
    </div>
  );
}

// Example 4: Conditional rendering with loading states
export function FeatureShowcase() {
  const newFeature = useFeatureFlag('experimental-feature');
  const premiumFeature = useFeatureFlag('premium-only-feature');

  return (
    <div className="space-y-4">
      <h2>Feature Showcase</h2>
      
      {/* Show loading state */}
      {newFeature.isLoading && (
        <div className="animate-pulse bg-gray-200 h-20 rounded"></div>
      )}
      
      {/* Show feature when loaded and enabled */}
      {!newFeature.isLoading && newFeature.isEnabled && (
        <Card>
          <CardHeader>
            <CardTitle>🚀 New Experimental Feature</CardTitle>
          </CardHeader>
          <CardContent>
            <p>This is a new feature currently in testing!</p>
          </CardContent>
        </Card>
      )}
      
      {/* Handle errors gracefully */}
      {premiumFeature.error && (
        <div className="text-red-500">
          Unable to load premium features. Using basic version.
        </div>
      )}
      
      {/* Premium feature with fallback */}
      {premiumFeature.isEnabled ? (
        <PremiumFeatureComponent />
      ) : (
        <BasicFeatureComponent />
      )}
    </div>
  );
}

// Example 5: Server-side feature flag usage (API route)
/*
// pages/api/data/export.ts
import { NextApiRequest, NextApiResponse } from 'next';
import { FeatureFlagService } from '@/lib/feature-flags/service';
import { getAuthenticatedUser } from '@/lib/auth';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const user = await getAuthenticatedUser(req);
    const featureFlagService = await FeatureFlagService.getInstance();
    
    // Check if export feature is enabled for this user
    const canExport = await featureFlagService.isFeatureEnabled('data-export', user.id);
    
    if (!canExport) {
      return res.status(403).json({ 
        error: 'Export feature not available for your account' 
      });
    }
    
    // Check for premium export features
    const hasPremiumExport = await featureFlagService.isFeatureEnabled('premium-export', user.id);
    
    if (hasPremiumExport) {
      return res.json(await generatePremiumExport(user.id));
    } else {
      return res.json(await generateBasicExport(user.id));
    }
  } catch (error) {
    res.status(500).json({ error: 'Internal server error' });
  }
}
*/

// Example 6: Bulk feature flag evaluation
export function UserPreferences() {
  const { flags } = useFeatureFlags(); // Get all flags
  
  const availableFeatures = [
    {
      key: 'dark-mode-v2',
      name: 'Enhanced Dark Mode',
      description: 'Improved dark mode with better contrast',
    },
    {
      key: 'keyboard-shortcuts',
      name: 'Keyboard Shortcuts',
      description: 'Navigate faster with keyboard shortcuts',
    },
    {
      key: 'voice-commands',
      name: 'Voice Commands',
      description: 'Control the app with voice commands',
    },
  ];

  return (
    <div className="space-y-4">
      <h3>Available Features</h3>
      {availableFeatures.map((feature) => (
        <Card key={feature.key}>
          <CardContent className="flex items-center justify-between p-4">
            <div>
              <h4 className="font-medium">{feature.name}</h4>
              <p className="text-sm text-gray-600">{feature.description}</p>
            </div>
            <Badge variant={flags[feature.key] ? 'default' : 'secondary'}>
              {flags[feature.key] ? 'Enabled' : 'Disabled'}
            </Badge>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

// Example 7: Feature flag with user feedback
export function BetaFeatureWrapper({ children, flagName, featureName }: {
  children: React.ReactNode;
  flagName: string;
  featureName: string;
}) {
  const { isEnabled, isLoading } = useFeatureFlag(flagName);

  if (isLoading) {
    return <div>Loading {featureName}...</div>;
  }

  if (!isEnabled) {
    return null; // Don't render anything if feature is disabled
  }

  return (
    <div className="relative">
      {/* Beta badge */}
      <div className="absolute top-2 right-2 z-10">
        <Badge variant="secondary">Beta</Badge>
      </div>
      
      {children}
      
      {/* Feedback section */}
      <div className="mt-4 p-3 bg-blue-50 rounded-lg">
        <p className="text-sm text-blue-800">
          You're using a beta feature! 
          <Button variant="link" className="p-0 ml-1 h-auto">
            Share feedback
          </Button>
        </p>
      </div>
    </div>
  );
}

// Example 8: A/B testing component
export function CallToActionButton() {
  const { isEnabled: useVariantB } = useFeatureFlag('cta-button-variant-b');

  return (
    <Button 
      variant={useVariantB ? 'default' : 'outline'}
      size={useVariantB ? 'lg' : 'default'}
      className={useVariantB ? 'bg-gradient-to-r from-blue-500 to-purple-600' : ''}
    >
      {useVariantB ? '🚀 Get Started Now!' : 'Get Started'}
    </Button>
  );
}

// Placeholder components for examples
function NewChatInterface() { return <div>New Chat UI</div>; }
function LegacyChatInterface() { return <div>Legacy Chat UI</div>; }
function DashboardSkeleton() { return <div>Loading...</div>; }
function BasicDashboard() { return <div>Basic Dashboard</div>; }
function BasicStatsCard() { return <div>Basic Stats</div>; }
function AdvancedAnalyticsCard() { return <div>Advanced Analytics</div>; }
function NotificationsPanel() { return <div>Notifications</div>; }
function ExportDataButton() { return <Button>Export Data</Button>; }
function AISearchBox() { return <div>AI Search</div>; }
function AdvancedSearchBox() { return <div>Advanced Search</div>; }
function BasicSearchBox() { return <div>Basic Search</div>; }
function PremiumFeatureComponent() { return <div>Premium Feature</div>; }
function BasicFeatureComponent() { return <div>Basic Feature</div>; }
