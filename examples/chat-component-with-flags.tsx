/**
 * Example: Adding Feature Flags to an Existing Chat Component
 *
 * This example shows how to integrate feature flags into an existing
 * chat component to enable gradual rollout of new features.
 */

import { useState } from 'react';
import { useFeatureFlag, useFeatureFlags } from '@/hooks/useFeatureFlag';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import {
  Send,
  Mic,
  Paperclip,
  Sparkles,
  Zap,
  MessageSquare,
  Bot,
} from 'lucide-react';

// Original chat component (before feature flags)
export function OriginalChatComponent() {
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState<
    Array<{ id: string; text: string; sender: 'user' | 'bot' }>
  >([]);

  const sendMessage = () => {
    if (!message.trim()) return;

    setMessages((prev) => [
      ...prev,
      {
        id: Date.now().toString(),
        text: message,
        sender: 'user',
      },
    ]);
    setMessage('');
  };

  return (
    <div className='flex flex-col h-96 border rounded-lg'>
      {/* Messages */}
      <div className='flex-1 p-4 overflow-y-auto space-y-2'>
        {messages.map((msg) => (
          <div
            key={msg.id}
            className={`flex ${msg.sender === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-xs px-3 py-2 rounded-lg ${
                msg.sender === 'user' ? 'bg-blue-500 text-white' : 'bg-gray-200'
              }`}
            >
              {msg.text}
            </div>
          </div>
        ))}
      </div>

      {/* Input */}
      <div className='p-4 border-t flex gap-2'>
        <Input
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          placeholder='Type a message...'
          onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
        />
        <Button onClick={sendMessage}>
          <Send className='h-4 w-4' />
        </Button>
      </div>
    </div>
  );
}

// Enhanced chat component with feature flags
export function EnhancedChatComponent() {
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState<
    Array<{
      id: string;
      text: string;
      sender: 'user' | 'bot';
      timestamp?: Date;
      isAI?: boolean;
    }>
  >([]);

  // Feature flags for different chat enhancements
  const { flags, isLoading } = useFeatureFlags([
    'chat-voice-input', // Voice message input
    'chat-file-upload', // File attachment support
    'chat-ai-suggestions', // AI-powered message suggestions
    'chat-enhanced-ui', // New UI design
    'chat-typing-indicator', // Show typing indicators
    'chat-message-reactions', // Message reactions
    'chat-smart-replies', // Quick reply suggestions
    'chat-message-search', // Search within chat history
  ]);

  // Individual flags for specific features
  const { isEnabled: hasRealTimeTyping } = useFeatureFlag(
    'chat-real-time-typing'
  );
  const { isEnabled: hasMessageThreads } = useFeatureFlag(
    'chat-message-threads'
  );

  const sendMessage = async () => {
    if (!message.trim()) return;

    const newMessage = {
      id: Date.now().toString(),
      text: message,
      sender: 'user' as const,
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, newMessage]);
    setMessage('');

    // AI-powered auto-response (if enabled)
    if (flags['chat-ai-suggestions']) {
      setTimeout(() => {
        setMessages((prev) => [
          ...prev,
          {
            id: (Date.now() + 1).toString(),
            text: 'This is an AI-generated response based on your message!',
            sender: 'bot',
            timestamp: new Date(),
            isAI: true,
          },
        ]);
      }, 1000);
    }
  };

  const handleVoiceInput = () => {
    // Voice input implementation
    console.log('Voice input activated');
  };

  const handleFileUpload = () => {
    // File upload implementation
    console.log('File upload activated');
  };

  if (isLoading) {
    return (
      <div className='flex items-center justify-center h-96 border rounded-lg'>
        <div className='text-center'>
          <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2'></div>
          <p>Loading chat features...</p>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`flex flex-col h-96 border rounded-lg ${
        flags['chat-enhanced-ui']
          ? 'bg-gradient-to-b from-white to-gray-50'
          : ''
      }`}
    >
      {/* Header with feature indicators */}
      {flags['chat-enhanced-ui'] && (
        <div className='p-3 border-b bg-white/80 backdrop-blur-sm'>
          <div className='flex items-center justify-between'>
            <div className='flex items-center gap-2'>
              <MessageSquare className='h-5 w-5 text-blue-500' />
              <span className='font-medium'>Enhanced Chat</span>
            </div>
            <div className='flex gap-1'>
              {flags['chat-ai-suggestions'] && (
                <Badge variant='secondary' className='text-xs'>
                  <Bot className='h-3 w-3 mr-1' />
                  AI
                </Badge>
              )}
              {flags['chat-voice-input'] && (
                <Badge variant='secondary' className='text-xs'>
                  <Mic className='h-3 w-3 mr-1' />
                  Voice
                </Badge>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Messages */}
      <div className='flex-1 p-4 overflow-y-auto space-y-2'>
        {messages.map((msg) => (
          <div
            key={msg.id}
            className={`flex ${msg.sender === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-xs px-3 py-2 rounded-lg relative group ${
                msg.sender === 'user'
                  ? flags['chat-enhanced-ui']
                    ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-sm'
                    : 'bg-blue-500 text-white'
                  : flags['chat-enhanced-ui']
                    ? 'bg-white border shadow-sm'
                    : 'bg-gray-200'
              }`}
            >
              {/* AI indicator */}
              {msg.isAI && flags['chat-ai-suggestions'] && (
                <div className='absolute -top-2 -left-2'>
                  <Sparkles className='h-4 w-4 text-yellow-500' />
                </div>
              )}

              <div>{msg.text}</div>

              {/* Timestamp (enhanced UI only) */}
              {flags['chat-enhanced-ui'] && msg.timestamp && (
                <div className='text-xs opacity-70 mt-1'>
                  {msg.timestamp.toLocaleTimeString([], {
                    hour: '2-digit',
                    minute: '2-digit',
                  })}
                </div>
              )}

              {/* Message reactions (if enabled) */}
              {flags['chat-message-reactions'] && (
                <div className='absolute -bottom-2 right-0 opacity-0 group-hover:opacity-100 transition-opacity'>
                  <div className='flex gap-1 bg-white rounded-full shadow-lg p-1'>
                    <button className='text-xs hover:bg-gray-100 rounded-full p-1'>
                      👍
                    </button>
                    <button className='text-xs hover:bg-gray-100 rounded-full p-1'>
                      ❤️
                    </button>
                    <button className='text-xs hover:bg-gray-100 rounded-full p-1'>
                      😊
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        ))}

        {/* Typing indicator */}
        {flags['chat-typing-indicator'] && hasRealTimeTyping && (
          <div className='flex justify-start'>
            <div className='bg-gray-200 px-3 py-2 rounded-lg'>
              <div className='flex space-x-1'>
                <div className='w-2 h-2 bg-gray-500 rounded-full animate-bounce'></div>
                <div
                  className='w-2 h-2 bg-gray-500 rounded-full animate-bounce'
                  style={{ animationDelay: '0.1s' }}
                ></div>
                <div
                  className='w-2 h-2 bg-gray-500 rounded-full animate-bounce'
                  style={{ animationDelay: '0.2s' }}
                ></div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Smart replies (if enabled) */}
      {flags['chat-smart-replies'] && messages.length > 0 && (
        <div className='px-4 py-2 border-t bg-gray-50'>
          <div className='flex gap-2 overflow-x-auto'>
            <Button
              variant='outline'
              size='sm'
              onClick={() => setMessage("That's helpful!")}
            >
              <Zap className='h-3 w-3 mr-1' />
              That's helpful!
            </Button>
            <Button
              variant='outline'
              size='sm'
              onClick={() => setMessage('Tell me more')}
            >
              <Zap className='h-3 w-3 mr-1' />
              Tell me more
            </Button>
            <Button
              variant='outline'
              size='sm'
              onClick={() => setMessage('Thanks!')}
            >
              <Zap className='h-3 w-3 mr-1' />
              Thanks!
            </Button>
          </div>
        </div>
      )}

      {/* Input area */}
      <div
        className={`p-4 border-t flex gap-2 ${
          flags['chat-enhanced-ui'] ? 'bg-white/80 backdrop-blur-sm' : ''
        }`}
      >
        {/* File upload button */}
        {flags['chat-file-upload'] && (
          <Button variant='outline' size='icon' onClick={handleFileUpload}>
            <Paperclip className='h-4 w-4' />
          </Button>
        )}

        <Input
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          placeholder={
            flags['chat-ai-suggestions']
              ? 'Type a message... (AI-powered)'
              : 'Type a message...'
          }
          onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
          className={
            flags['chat-enhanced-ui']
              ? 'border-gray-300 focus:border-blue-500'
              : ''
          }
        />

        {/* Voice input button */}
        {flags['chat-voice-input'] && (
          <Button variant='outline' size='icon' onClick={handleVoiceInput}>
            <Mic className='h-4 w-4' />
          </Button>
        )}

        <Button
          onClick={sendMessage}
          className={
            flags['chat-enhanced-ui']
              ? 'bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700'
              : ''
          }
        >
          <Send className='h-4 w-4' />
        </Button>
      </div>

      {/* Feature showcase */}
      {flags['chat-enhanced-ui'] && (
        <div className='px-4 pb-2'>
          <div className='text-xs text-gray-500 text-center'>
            Enhanced with:{' '}
            {Object.entries(flags)
              .filter(([_, enabled]) => enabled)
              .map(([flag]) => flag.replace('chat-', '').replace('-', ' '))
              .join(', ')}
          </div>
        </div>
      )}
    </div>
  );
}

// Usage example in a page
export function ChatPage() {
  const { isEnabled: useEnhancedChat } = useFeatureFlag(
    'chat-enhanced-experience'
  );

  return (
    <div className='container mx-auto p-6'>
      <h1 className='text-2xl font-bold mb-6'>
        Chat Interface{' '}
        {useEnhancedChat && <Badge className='ml-2'>Enhanced</Badge>}
      </h1>

      {useEnhancedChat ? <EnhancedChatComponent /> : <OriginalChatComponent />}

      {/* Development info (only show in dev) */}
      {process.env.NODE_ENV === 'development' && (
        <Card className='mt-4'>
          <CardHeader>
            <h3 className='text-sm font-medium'>Feature Flag Status</h3>
          </CardHeader>
          <CardContent>
            <p className='text-sm text-gray-600'>
              Enhanced Chat: {useEnhancedChat ? '✅ Enabled' : '❌ Disabled'}
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
